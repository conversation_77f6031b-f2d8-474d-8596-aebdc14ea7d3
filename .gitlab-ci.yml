stages:
    - prebuild
    - prebuild-prod
    - build-base
    - build
    - deploy
    - security
    - report
    - test

variables:
    TAG: latest

task_branch_k8s:
    stage: prebuild
    extends: .rules_deploy
    image: cr.yandex/crpsvv5iscafkhlubkmt/foquz-debian-builder-base:1.0.0
    script:
        - chmod 777 ./images/scripts/versions.sh
        - ./images/scripts/versions.sh
    artifacts:
        reports:
            dotenv: build.env

deploy_core:
    stage: deploy
    extends: .rules_deploy
    variables:
        SET_VERSION: true
        TAG: "$TAG"
        RELEASE: "$RELEASE"
        MODE: "$MODE"
        TYPE: "$TYPE"
        BRANCH: "$CI_COMMIT_BRANCH"
        SOURCE_CI_PROJECT_TITLE: "$CI_PROJECT_TITLE"
        SOURCE_CI_PROJECT_PATH: "$CI_PROJECT_PATH"
        SOURCE_CI_COMMIT_SHA: "$CI_COMMIT_SHA"
        SOURCE_CI_COMMIT_MESSAGE: "$CI_COMMIT_TITLE"
        SOURCE_CI_PIPELINE_CREATED_AT: "$CI_PIPELINE_CREATED_AT"
        SKIP_DEPLOY: "$SKIP_DEPLOY"
        SKIP_BUILD: "$SKIP_BUILD"
        CONTAINER_foquz_core_cli_php_base: "$CORE_CLI_BASE"
        CONTAINER_foquz_core_alpine_php_fpm_base: "$CORE_FPM_BASE"
        CONTAINER_foquz_core_migrations: "$CORE_MIGRATIONS"
        CONTAINER_foquz_core_consumer_widget: "$CORE_CONSUMER_WIDGET"
        CONTAINER_foquz_core_consumer_mailings: "$CORE_CONSUMER_MAILING"
        CONTAINER_foquz_core_consumer_main: "$CORE_CONSUMER_MAIN"
        CONTAINER_foquz_core_consumer_quiz: "$CORE_CONSUMER_QUIZ"
        CONTAINER_foquz_core_main_app: "$CORE_FPM"
        CONTAINER_foquz_core_nginx: "$CORE_NGINX"
        CONTAINER_foquz_core_js_main: "$CORE_JS_MAIN"
        CONTAINER_foquz_core_js_widget_iframe: "$CORE_JS_WIDGET_IFRAME"
        CONTAINER_foquz_core_js_mail: "$CORE_JS_MAIL"
        CONTAINER_foquz_core_job_init: "$CORE_INIT"
    trigger:
        project: 'doxsw/foquz-app-deploy'
        branch: 'stage' 

set-production-flags:
    stage: deploy
    rules:
        - if: $CI_COMMIT_BRANCH == 'develop'
    tags:
        - devfoquz
    script:
        - php ./images/scripts/set-production-flags.php

get-production-flags:
    stage: prebuild-prod
    extends: .rules_build_base
    tags:
        - devfoquz
    script:
        - php ./images/scripts/get-production-flags.php
    artifacts:
        reports:
            dotenv: build.env

set-production-version:
    stage: deploy
    extends: .rules_build_base
    tags:
        - devfoquz
    script:
        - php ./images/scripts/set-production-version.php

include:
    - local: 'images/cicd/build-images-base.yml'

    - local: 'images/cicd/build-images-ci.yml'

    - project: 'cicd/foquz-cicd'
      ref: main
      file: 'job-rules/rules.yml'

    # Переменные безопасности
    - project: 'cicd/foquz-cicd'
      ref: main
      file: 'security/code/init.yml'


run_playwright_tests:
    stage: test
    rules:
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    tags:
        - docker-vps-tw
    image: mcr.microsoft.com/playwright:v1.52.0-jammy
    allow_failure: true
    script:
        - apt-get update && apt-get install -y jq
        - npm ci
        - |
            set +e
            PLAYWRIGHT_JSON_OUTPUT_FILE=test-results.json npx playwright test --reporter=json
            PLAYWRIGHT_EXIT_CODE=$?
            set -e
            if [ $PLAYWRIGHT_EXIT_CODE -ne 0 ]; then
              echo "Playwright tests failed with exit code $PLAYWRIGHT_EXIT_CODE"
            fi

        # Отправляем отчет о тестах в Mattermost
        - |
            if [ -f "test-results.json" ]; then
              jq -n --argfile playwright_report test-results.json \
                --arg project_name "$CI_PROJECT_NAME" \
                --arg project_url "$CI_PROJECT_URL" \
                --arg pipeline_id "$CI_PIPELINE_ID" \
                --arg pipeline_url "$CI_PIPELINE_URL" \
                --arg job_id "$CI_JOB_ID" \
                --arg job_url "$CI_JOB_URL" \
                --arg commit_sha "$CI_COMMIT_SHA" \
                --arg commit_short_sha "$CI_COMMIT_SHORT_SHA" \
                --arg commit_ref_name "$CI_COMMIT_REF_NAME" \
                --arg commit_message "$CI_COMMIT_MESSAGE" \
                --arg commit_author "$CI_COMMIT_AUTHOR" \
                --arg pipeline_source "$CI_PIPELINE_SOURCE" \
                '{
                  playwright_report: $playwright_report,
                  gitlab_data: {
                    project_name: $project_name,
                    project_url: $project_url,
                    pipeline_id: $pipeline_id,
                    pipeline_url: $pipeline_url,
                    job_id: $job_id,
                    job_url: $job_url,
                    commit: {
                      sha: $commit_sha,
                      short_sha: $commit_short_sha,
                      ref_name: $commit_ref_name,
                      message: $commit_message,
                      author: $commit_author
                    },
                    pipeline_source: $pipeline_source
                  }
                }' > extended-test-results.json
            
              curl -X POST "${TEST_REPORT_URL}" \
                -H "Content-Type: application/json" \
                -d @extended-test-results.json \
                --fail-with-body || echo "Failed to send report to Mattermost"
            else
              echo "No test results file found"
            fi

