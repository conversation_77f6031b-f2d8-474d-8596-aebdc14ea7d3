<?php

use yii\db\Migration;

/**
 * Class m250904_094328_add_autoclose_delay_column_in_foquz_poll_widget_table
 */
class m250904_094328_add_autoclose_delay_column_in_foquz_poll_widget_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%foquz_poll_widget}}', 'autoclose_delay',
            $this->integer()->null()->after('priority')
                ->comment('Автоматически закрыть виджет после завершения опроса через N секунд')
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%foquz_poll_widget}}', 'autoclose_delay');
    }

}
