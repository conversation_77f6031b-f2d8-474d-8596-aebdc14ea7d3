import { DialogWrapper } from 'Dialogs/wrapper';
import { FilialsDataCollection } from "Models/data-collection/filials";
import { FontsCollection } from "Models/data-collection/fonts";
import { DataCollection } from "Models/data-collection";
import { numberRange } from "Utils/number/range";
import { HidePoppersEvent } from "@/utils/events/modal";
import "Components/input/color-picker";
import { get } from 'lodash';

const targetSettings = {
  cookies: {
    array: 'cookies',
    object: 'valueObj',
    actions: 'cookiesActions',
    trigger: 'enableCookie',
    needAppend: [
      {name: 'name',
      value: 'cookieText'
    },
      {name: 'name_condition',
      value: 'cookieValue'
    },
      {name: 'with_value',
      value: 'cookieValueMode'
    },
      {name: 'value_condition',
      value: 'cookieValueModeAction'
    },
    {
      name: 'value',
      value: 'cookieValueModeText'
    }
    ]
  },
  variables: {
    array: 'variables',
    object: 'valueObj',
    actions: 'cookiesActions',
    trigger: 'enableVariable',
    needAppend: [
      {name: 'name',
      value: 'cookieText'
    },
      {name: 'name_condition',
      value: 'cookieValue'
    },
      {name: 'with_value',
      value: 'cookieValueMode'
    },
      {name: 'value_condition',
      value: 'cookieValueModeAction'
    },
    {
      name: 'value',
      value: 'cookieValueModeText'
    }
    ]
  },
  client_tags: {
    array: 'tags',
    object: 'tagObj',
    actions: 'tagActions',
    trigger: 'enableTag'
  },

  url: {
    array: 'pages',
    object: 'pageObj',
    actions: 'pagesActions',
    trigger: 'enablePages'
  },
  event: {
    array: 'events_',
    object: 'pageObj',
    actions: 'pagesActions',
    trigger: 'enableEvents'
  },
}
const cookiesActions = ['Равен', 'Включает', 'Исключает', 'Начинается с', 'Заканчивается на', 'Регулярное выражение' ]
const tagActions = ['Включить контакты с тегами', 'Исключить контакты с тегами']
const showActions = ['Кнопка / опрос показан', 'Опрос показан', 'Респондент ответил на хотя бы один вопрос', 'Респондент заполнил опрос до конца', 'Показывать всегда']
const pagesActions = ['Точное соответствие', 'Включает', 'Исключает', 'Начинается с', 'Заканчивается на', 'Регулярное выражение' ]
const visitOptions = ['Не обнулять', 'Обнулять']
const visitTimerOptions = ['Таймер на страницу']
const valueObj = (id, data) => {
  const obj = {
    id,
    cookieValue: ko.observable(data?.name_condition || 1),
    cookieText: ko.observable(data?.name || ''),
    cookieValueMode: ko.observable(+data?.with_value || 0),
    cookieValueModeText: ko.observable(data?.value || ''),
    cookieValueModeAction: ko.observable(data?.value_condition || 1),
    textError: ko.observable(false),
    modeTextError: ko.observable(false),
  }
  obj.cookieText.subscribe((v) => {
    if (v) {
      obj.textError(false)
    }
  })
  obj.cookieValueModeText.subscribe((v) => {
    if (v) {
      obj.modeTextError(false)
    }
  })
  return obj
}

const pageObj = (id, data) => {
  const obj = {
    id,
    rool: ko.observable(data?.condition || 1),
    adress: ko.observable(data?.value || ''),
    adressError: ko.observable(false),
  }
  obj.adress.subscribe((v) => {
    if (v) {
      obj.adressError(false)
    }
  })
  return obj
}
const tagObj = (id, data) => {
  const obj = {
    id,
    value: ko.observable(data?.condition || 1),
    tags: ko.observableArray(data?.values || []),
    tagError: ko.observable(false),
  }
  obj.tags.subscribe((v) => {
    if (v.length) {
      obj.tagError(false)
    }
  })
  return obj
}


export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    this.params = params

    this.poll = ko.observable(params.poll?.id || '');

    this.polls = new Directory("poll/list");
    this.pollSelect = params.pollSelect || !this.poll();
    if (this.pollSelect) {
      this.polls.load();

      const hasPolls = this.polls.data() && this.polls.data().length > 0;
      if (hasPolls && !this.poll()) {
        this.poll(this.polls.data()[0].id);
      }
      
      if (this.poll() && this.pollSelect) {
        this.getItemsLinks(this.poll());
      }
    }

    this.poll.subscribe((newValue) => {
     if(this.pollSelect){
        this.getItemsLinks(newValue)

       const subscription = this.pollLinksOptions.subscribe((options) => {
         if (options.length > 0) {
           this.pollLinkId(options[0].id);
           subscription.dispose(); 
         }
       });
      }
    });

    this.priorityCheckLink = params.priorityCheckLink || false;

    this.edit = params.edit

    this.closeByFinishButton = ko.observable(false);
    this.enableAutoClose = ko.observable(false);
    this.autoCloseDelay = ko.observable('');
    this.autoCloseDelayError = ko.observable(false);
    this.priority = ko.observable(null);
    this.priorityError = ko.observable('');

    this.pollLinkId = ko.observable()
    this.pollLinksOptions = ko.observableArray([]);
    this.pollLinksOptionsWidgets = ko.observableArray([]);

    this.pollLinksOptions = ko.pureComputed(() => {
      if (this.pollSelect){
        return this.pollLinksOptionsWidgets()
          .sort((a, b) => a.quote_id - b.quote_id)
          .map(e => ({
            id: e.quote_id,
            text: e.link_name,
            active: e.active
          }));
      }

			if (!params.poll_links && !params?.data?.poll_links?.length) {
        return [];
      }
			
			if (params?.data?.poll_links) {
				return params.data.poll_links.map(link => ({
					id: link.id,
					text: link.name,
					active: link.active
				}))
			}
			 else if (params.poll_links()){
				return params.poll_links().map(link => ({
					id: link.id,
					text: link.name,
					active: link.active
				}))
			}
    });

    this.setDefaultPollLinkId = ko.computed(() => {
      const options = this.pollLinksOptions();
      if (options.length > 0 && !this.pollLinkId()) {
        this.pollLinkId(options[0].id);
      }
    });

    this.pollSettingsUrl = ko.pureComputed(() => {
      if (!this.poll()) return '/foquz/foquz-poll/';
      return `/foquz/foquz-poll/sender?id=${this.poll()}`;
    });

    this.isSelectedLinkActive = ko.pureComputed(() => {
      if (!this.pollLinkId()) return true;

      const options = ko.unwrap(this.pollLinksOptions);

      const selectedLink = ko.utils.arrayFirst(options, link => link.id === this.pollLinkId());
      return selectedLink ? selectedLink.active : true;
    });

    const readNumber = (computedValue) => {
      const value = computedValue();
      if (value === null) {
        return '';
      } 
      return value;
    }

    const writeNumber = (value, computedValue) => {
      const initValue = computedValue();
      let newValue;
      if (!value && (value !== 0)) {
        newValue = null;
      } else {
        value = String(value).replace(/\D/ig, '');
        if (value === '') {
          newValue = null;
        } else {
          newValue = Number(value);
        }
      }
      if (initValue === newValue) {
        computedValue(NaN);
      }
      computedValue(newValue);
    }

    this.formattedPriority = ko.pureComputed({
      read() {
        return readNumber(this.priority);
      },
      write(value) {
        this.priorityError('');
        writeNumber(value, this.priority);
      },
      owner: this
    });

    this.widgetName = ko.observable("Новый виджет");
    this.buttonText = ko.observable("");
    this.widgetId = params.data?.id || null;
    this.editingName = ko.observable(false);
    this.activeTab  = ko.observable('visual');
    this.showMode = ko.observable('click');
    this.buttonMode = ko.observable('text');
    this.noClickMode = ko.observable('page-stop');
    this.buttonPlacement = ko.observable('fix');
    this.font = ko.observable("Arial, Helvetica, sans-serif");
    this.fontSize = ko.observable('14');
    this.bold = ko.observable(false);
    this.italic = ko.observable(false);
    this.simpleView = ko.observable(false);
    this.border = ko.observable(true);
    this.color = ko.observable('#000000');
    this.background = ko.observable('#FFFFFF');
    this.canSave = ko.observable(false);
    this.filialId = ko.observable("");
    this.collections = {
      filials: new FilialsDataCollection({
        integrated: false,
        undefinedFilialOption: false,
        selectableCategories: false,
      }),
      fonts: new FontsCollection(),
      fontSizes: new DataCollection(
        null,
        numberRange(12, 24).map((n) => ({ id: n, text: "" + n }))
      ),
    };
    this.data = null;
    this.collections.filials.load();
    this.codeCopied = ko.observable(false);
    this.enableTarget = ko.observable(false);
    this.enableCookie = ko.observable(false);
    this.enableVariable = ko.observable(false);
    this.enableTag = ko.observable(false);
    this.enableType = ko.observable(false);
    this.enableUser = ko.observable(false);
    this.enablePages = ko.observable(false);
    this.enableEvents = ko.observable(false);
    this.enableDepth = ko.observable(false);
    this.showCookie = ko.observable(false);
    this.showVariable = ko.observable(false);
    this.showTag = ko.observable(false);
    this.showType = ko.observable(false);
    this.showUser = ko.observable(false);
    this.showPages = ko.observable(false);
    this.showEvents = ko.observable(false);
    this.showDepth = ko.observable(false);
    this.cookies = ko.observableArray([]);
    this.variables = ko.observableArray([]);
    this.cookiePc = ko.observable(true);
    this.cookiePad = ko.observable(true);
    this.cookiePhone = ko.observable(true);
    this.userPercent = ko.observable(0);
    this.pages = ko.observableArray([]);
    this.events_ = ko.observableArray([]);
    this.tags = ko.observableArray([]);
    this.tagOptions = ko.observableArray([]);
    this.enableHash = ko.observable(false);
    this.disabled = ko.computed(() => {
      return !this.enableTarget();
    });
    this.targetCounter = ko.observable(0);

    this.cookiesActions = ko.observableArray([]);
    cookiesActions.forEach((element, index) => {
      const obj = {
        id: index + 1,
        text: ko.observable(element)
      }
      this.cookiesActions.push(obj)
    });
    this.tagActions = ko.observableArray([]);
    tagActions.forEach((element, index) => {
      const obj = {
        id: index + 1,
        text: ko.observable(element)
      }
      this.tagActions.push(obj)
    });
    this.showActions = ko.observableArray([])
    this.showAction = ko.observable(1)
    showActions.forEach((element, index) => {
      const obj = {
        id: index,
        text: ko.observable(element)
      }
      this.showActions.push(obj)
    });
    this.pagesActions = ko.observableArray([]);
    pagesActions.forEach((element, index) => {
      const obj = {
        id: index + 1,
        text: ko.observable(element)
      }
      this.pagesActions.push(obj)
    });

    this.visitOptions = ko.observableArray([]);
    this.visitOptionValue = ko.observable(0);
    this.visitCounter = ko.observable('')
    this.visitCounterError = ko.observable(false)
    this.visitTimer = ko.observable('')
    this.visitTimerError = ko.observable(false)

    this.eventOptions = ko.observableArray([]);
    this.eventOptionValue = ko.observable(0);
    this.eventCounter = ko.observable('')
    this.eventCounterError = ko.observable(false)
    this.eventTimer = ko.observable('')
    this.eventTimerError = ko.observable(false)

    visitOptions.forEach((element, index) => {
      const obj = {
        id: index,
        text: ko.observable(element)
      }
      this.visitOptions.push(obj)
      this.eventOptions.push(obj)
    })

    this.visitTimerOptions = ko.observableArray([]);
    this.visitTimerOptionValue = ko.observable(1);
    this.eventTimerOptions = ko.observableArray([]);
    this.eventTimerOptionValue = ko.observable(1);

    visitTimerOptions.forEach((element, index) => {
      const obj = {
        id: index + 1,
        text: ko.observable(element)
      }
      this.visitTimerOptions.push(obj)
      this.eventTimerOptions.push(obj)
    })

    this.cancelSave = false;
    this.showSuccessMessage = ko.observable(false);
    this.typesError = ko.observable(false);

    this.enableVisitCounter = ko.observable(false);
    this.enableVisitTimer = ko.observable(false);
    this.enableEventCounter = ko.observable(false);
    this.enableEventTimer = ko.observable(false);
    this.trackHistoryChanges = ko.observable(false)


    const origin = window.location.origin
    this.code = ko.observable(null)

    this.widgetCode = ko.computed(() => {
      return `
        <script>
          window.FOQUZ_SDK = {
              widget: {
                  widget_code: '${this.code()}', 
              },
              params: {
              },
          };
      
          (function (f, o, q, u, z) {
              u = o.createElement("script");
              z = o.getElementsByTagName("script")[0];
              u.async = 1;
              u.src = q + '?t=' + Date.now();
              z ? z.parentNode.insertBefore(u, z) : o.head.appendChild(u);
          })(window, document, "${origin}/widgets/poll/widget_new.js");
        </script>
      `;
    });

    this.isActive = ko.observable(false);

    this.getTags();

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    this.windowUnitVariants = [
      { value: 1, label: 'px' },
      { value: 0, label: '%' }
    ];

    this.verticalScrollUnit = ko.observable(1);
    this.horizontalScrollUnit = ko.observable(1);
    
    // Добавляем observable для сообщений об ошибках
    this.verticalScrollValueError = ko.observable('');
    this.horizontalScrollValueError = ko.observable('');
    
    this.verticalScrollError = ko.pureComputed(() => {
      if (this.verticalScrollValue.pureValue && this.verticalScrollValueError() && this.verticalScrollValueError().length > 0) {
        return this.verticalScrollValueError();
      }
      return '';
    });
    
    this.horizontalScrollError = ko.pureComputed(() => {
      if (this.horizontalScrollValue.pureValue && this.horizontalScrollValueError() && this.horizontalScrollValueError().length > 0) {
        return this.horizontalScrollValueError();
      }
      return '';
    });

    const readScrollValue = (computedValue) => {
      const value = computedValue();
      if (value === null) {
        return '';
      } 
      return value;
    };

    const writeScrollValue = (value, computedValue, isPercentage = false) => {
      if (!value) {
        computedValue(null);
      } else {
        // Просто устанавливаем значение без форматирования
        computedValue(String(value));
      }
    };

    this.verticalScrollValue = {
      pureValue: ko.observable(null).extend({
        validation: {
          validator: (value) => {
            if (value === null) return true;
            
            // Временно разрешаем пробелы между числами
            // Но все равно показываем ошибку через verticalScrollValueError
            
            // Проверяем наличие недопустимых символов
            if (/[^\d,\s]/.test(value)) {
              return false;
            }
            
            // Разбиваем строку на числа, учитывая как запятые, так и пробелы
            const numbers = value.split(/[,\s]+/).filter(n => n.trim() !== '').map(n => parseInt(n, 10));
            
            if (this.verticalScrollUnit() === 0) {
              // Проверяем только, что числа больше 0, но не ограничиваем их до 100
              return numbers.every(n => n > 0);
            }
            
            return numbers.every(n => n > 0);
          },
          // message: function() {
          //   if (this.verticalScrollUnit() === 0) {
          //     return 'Нужно указать целое число, если чисел несколько, то через запятую';
          //   }
          //   return 'Нужно указать целое число, если чисел несколько, то через запятую';
          // },
        },
        required: {
          message: 'Обязательное поле',
        }
      }),
      value: ko.pureComputed({
        read() {
          return readScrollValue(this.verticalScrollValue.pureValue);
        },
        write(value) {
          writeScrollValue(value, this.verticalScrollValue.pureValue, this.verticalScrollUnit() === 0);
        },
        owner: this
      }),
    };

    this.horizontalScrollValue = {
      pureValue: ko.observable(null).extend({
        validation: {
          validator: (value) => {
            if (value === null) return true;
            
            // Временно разрешаем пробелы между числами
            // Но все равно показываем ошибку через horizontalScrollValueError
            
            // Проверяем наличие недопустимых символов
            if (/[^\d,\s]/.test(value)) {
              return false;
            }
            
            // Разбиваем строку на числа, учитывая как запятые, так и пробелы
            const numbers = value.split(/[,\s]+/).filter(n => n.trim() !== '').map(n => parseInt(n, 10));
            
            if (this.horizontalScrollUnit() === 0) {
              // Проверяем только, что числа больше 0, но не ограничиваем их до 100
              return numbers.every(n => n > 0);
            }
            
            return numbers.every(n => n > 0);
          },
          // message: function() {
          //   if (this.horizontalScrollUnit() === 0) {
          //     return 'Нужно указать целое число, если чисел несколько, то через запятую';
          //   }
          //   return 'Нужно указать целое число, если чисел несколько, то через запятую';
          // },
        },
        required: {
          message: 'Обязательное поле',
        }
      }),
      value: ko.pureComputed({
        read() {
          return readScrollValue(this.horizontalScrollValue.pureValue);
        },
        write(value) {
          writeScrollValue(value, this.horizontalScrollValue.pureValue, this.horizontalScrollUnit() === 0);
        },
        owner: this
      }),
    };

    this.defaultWindowSettings = {
      windowWidthUnit: ko.computed(() => {
        const isClick = this.showMode() === 'click';
        const simpleView = this.simpleView();
        const isPageStop = !isClick && (this.noClickMode() === 'page-stop');
        const isFullScreen = isClick && !simpleView;
  
        if (isPageStop || isFullScreen) {
          return 0
        } else {
          return 1
        }
      }),
      windowWidth: null,
      windowHeight: null,
      windowBorderRadiusUnit: 1,
      windowBorderRadius: null,
      windowPosition: ko.computed(() => {
        const isClick = this.showMode() === 'click';
        const isHello = !isClick && (this.noClickMode() === 'hello');
  
        if (isHello) {
          return 'left-bottom'
        } else {
          return 'center-center'
        }
      }),
    };

    this.minWindowWidth = ko.computed(() => {
      const isClick = this.showMode() === 'click';
      const isHello = !isClick && (this.noClickMode() === 'hello');
      const litePageStop = this.noClickMode() === 'lite-page-stop';
      const simpleView = this.simpleView();
      const isSimple = (isClick && simpleView) || (!isClick && litePageStop);
      if (isHello) {
        return {
          value: 330,
          text: 'Минимальная ширина окна виджета для отображения Hello-board — 330 px.',
        };
      } else if (isSimple) {
        return {
          value: 680,
          text: 'Минимальная ширина окна виджета для упрощённого отображения — 680 px.',
        };
      } else {
        return {
          value: 680,
          text: 'Минимальная ширина окна виджета для полного экрана отображения — 680 px.',
        };
      }
    });

    this.windowWidthUnit = ko.observable(this.defaultWindowSettings.windowWidthUnit());
    this.windowWidth = {
      pureValue: ko.observable(null).extend({
        validation: {
          validator: (value) => {
            if (value === null) return true;

            if (this.windowWidthUnit() === 0) {
              return value <= 100;
            } else {
              return value >= this.minWindowWidth().value;
            }
          },
          message: () => {
            if (this.windowWidthUnit() === 0) {
              return 'Максимальное значение 100%';
            } else {
              return `Минимальная ширина ${this.minWindowWidth().value} px`;
            }
          },
        },
      }),
      value: ko.pureComputed({
        read() {
          return readNumber(this.windowWidth.pureValue);
        },
        write(value) {
          writeNumber(value, this.windowWidth.pureValue);
        },
        owner: this
      }),
    };

    this.windowHeight = {
      pureValue: ko.observable(null).extend({
        validation: {
          validator: (value) => {
            if (value === null) return true;

            return value <= 100;
          },
          message: 'Максимальное значение 100%',
        },
      }),
      value: ko.pureComputed({
        read() {
          return readNumber(this.windowHeight.pureValue);
        },
        write(value) {
          writeNumber(value, this.windowHeight.pureValue);
        },
        owner: this
      }),
    };

    this.windowBorderRadiusUnit = ko.observable(1);
    this.windowBorderRadius = {
      pureValue: ko.observable(null).extend({
        validation: {
          validator: (value) => {
            if (value === null) return true;

            if (this.windowBorderRadiusUnit() === 0) {
              return value <= 50;
            } else {
              return true;
            }
          },
          message: 'Максимальное значение 50%',
        },
      }),
      value: ko.pureComputed({
        read() {
          return readNumber(this.windowBorderRadius.pureValue);
        },
        write(value) {
          writeNumber(value, this.windowBorderRadius.pureValue);
        },
        owner: this
      }),
    };

    this.windowPositionOtions = [
      { id: 'left-bottom', name: 'Слева внизу' },
      { id: 'right-bottom', name: 'Справа внизу' },
      { id: 'left-top', name: 'Слева вверху' },
      { id: 'right-top', name: 'Справа вверху' },
      { id: 'left-center', name: 'Слева по центру' },
      { id: 'right-center', name: 'Справа по центру' },
      { id: 'center-center', name: 'Посередине' },
    ];

    this.windowPosition = ko.observable(this.defaultWindowSettings.windowPosition()).extend({
      required: {
        message: 'Выберете расположение'
      },
    });

    this.placeholderWindowSettings = {
      windowWidth: ko.computed(() => {
        const defaultUnit = this.defaultWindowSettings.windowWidthUnit();
        const unit = this.windowWidthUnit();
        const isClick = this.showMode() === 'click';
        const isHello = !isClick && (this.noClickMode() === 'hello');
  
        if (!unit && !defaultUnit) {
          return '100'
        } else if (unit && !defaultUnit) {
          return ''
        } else if (unit && isHello) {
          return '330'
        } else if (!unit) {
          return ''
        } else {
          return '680'
        }
      }),
      windowHeight: ko.computed(() => {
        if (this.defaultWindowSettings.windowWidthUnit()) {
          return '';
        } else {
          return '100';
        }
      }),
      windowBorderRadius: ko.computed(() => {
        const unit = this.windowBorderRadiusUnit();
        if (unit) {
          if (!this.defaultWindowSettings.windowWidthUnit()) {
            return '0';
          } else {
            return '8';
          }
        } else {
          return '';
        }
      }),
    }

    this.formModel = ko.validatedObservable(
      {
        windowWidth: this.windowWidth.pureValue,
        windowHeight: this.windowHeight.pureValue,
        windowBorderRadius: this.windowBorderRadius.pureValue,
        windowPosition: this.windowPosition,
      },
      { deep: true, live: true }
    );

    this.enableVerticalScroll = ko.observable(false);
    this.enableHorizontalScroll = ko.observable(false);
    this.depthError = ko.observable(false);

    if (params.data) {
      this.data = params.data

      this.closeByFinishButton(params.data['close_by_finish_button'] === 1);
      this.formattedPriority(params.data.priority);
      const initialAutoCloseDelay = get(params, 'data.settings.autoclose_delay') || get(params, 'data.autoclose_delay');
      if (initialAutoCloseDelay && Number(initialAutoCloseDelay) > 0) {
        this.enableAutoClose(true);
        this.autoCloseDelay(String(initialAutoCloseDelay));
      }
      this.isActive(params.data.is_active == 1 ? true : false)
      this.showAction(params.data.show_until)
      this.pollLinkId(params.data.poll_link_id)
      this.code(params.data.code)
      this.widgetName(params.data.name)
      this.filialId(params.data.filial_id || '')
      this.showMode(params.data.appearance ? 'click' : 'no-click')
      this.enableCookie(params.data.triggers_status?.cookies == 1 ? true : false)
      this.enableVariable(params.data.triggers_status?.variables == 1 ? true : false)
      this.enableTag(params.data.triggers_status?.client_tags == 1 ? true : false)
      this.enableType(params.data.triggers_status?.devices == 1 ? true : false)
      this.enableUser(params.data.triggers_status?.coverage == 1 ? true : false)
      this.enablePages(params.data.triggers_status?.url == 1 ? true : false)
      this.enableEvents(params.data.triggers_status?.event == 1 ? true : false)
      this.enableDepth(params.data.triggers_status?.scroll_depth == 1 ? true : false)
      if (params.data.appearance) {
        this.buttonMode(params.data.button_type === 0 ? 'text' : params.data.button_type === 1 ? 'icon' : 'scale')
        this.buttonPlacement(params.data.position=== 0 ? 'fix' : params.data.position === 1 ? 'left' : 'right')
        this.buttonText(params.data.button_text)
        this.font(params.data.font)
        this.fontSize(params.data.font_size)
        this.bold(!!params.data.bold)
        this.italic(!!params.data.italic)
        this.color(params.data.text_color)
        this.background(params.data.background_color)
        this.border(!!params.data.stroke)
        this.simpleView(!!params.data.simple)
      } else {
        this.noClickMode(params.data.form === 3 ? 'page-stop' : params.data.form === 4 ? 'lite-page-stop' : 'hello')
      }

      this.enableTarget(!!+params.data.targeting)

      if (params.data.triggers) {
        if (params.data.triggers.cookies) {
          params.data.triggers.cookies.forEach(item => {
            const cookieObj = valueObj(this.cookies().length + 1, item)
            this.cookies.push(cookieObj)
          })
          if (this.enableCookie() && this.enableTarget()) this.targetCounter(this.targetCounter() + params.data.triggers.cookies.length)
          
        }

        if (params.data.triggers.variables) {
          params.data.triggers.variables.forEach(item => {
            const obj = valueObj(this.cookies().length + 1, item)
            this.variables.push(obj)
          })
          if (this.enableVariable() && this.enableTarget()) this.targetCounter(this.targetCounter() + params.data.triggers.variables.length)
          
        }
        if (params.data.triggers.client_tags) {
          params.data.triggers.client_tags.forEach(item => {
            const tagObj_ = tagObj(this.tags().length + 1, item)
            this.tags.push(tagObj_)
          })
          if (this.enableTag() && this.enableTarget()) this.targetCounter(this.targetCounter() + params.data.triggers.client_tags.length)
          
        }
        if (params.data.triggers.devices) {
          this.cookiePc(!!+params.data.triggers.devices.desktop)
          this.cookiePad(!!+params.data.triggers.devices.tablet)
          this.cookiePhone(!!+params.data.triggers.devices.smartphone)
          if (this.enableType() && this.enableTarget()) this.targetCounter(this.targetCounter() + 1)
        }

        if (params.data.triggers.coverage) {
          this.userPercent(+params.data.triggers.coverage)
          if (this.enableUser() && this.enableTarget()) this.targetCounter(this.targetCounter() + 1)
        }

        if (params.data.triggers.url) {
          this.enableHash(!!+params.data.triggers.url.hashtag)
          if (params.data.triggers.url.conditions) {
            params.data.triggers.url.conditions.forEach(item => {
              const page = pageObj(this.pages().length + 1, item)
              this.pages.push(page)
            })
          }
          
          if (this.enablePages() && this.enableTarget()) this.targetCounter(this.targetCounter() + params.data.triggers.url.conditions.length)

          this.enableVisitCounter(!!+params.data.triggers.url.visits_count.active)
          this.visitCounter(params.data.triggers.url.visits_count.value)
          this.visitOptionValue(params.data.triggers.url.visits_count.flush_counter)

          this.enableVisitTimer(!!+params.data.triggers.url.time.active)
          this.visitTimer(params.data.triggers.url.time.value)
          this.visitTimerOptionValue(params.data.triggers.url.time.timer_type)
          this.trackHistoryChanges(!!+params.data.triggers.url.track_history_changes)
        }

        if (params.data.triggers.event) {
          if (params.data.triggers.event.conditions) {
            params.data.triggers.event.conditions.forEach(item => {
              const ev = pageObj(this.events_().length + 1, item)
              this.events_.push(ev)
            })
          }
          
          if (this.enableEvents() && this.enableTarget()) this.targetCounter(this.targetCounter() + params.data.triggers.event.conditions.length)

          this.enableEventCounter(!!+params.data.triggers.event.count.active)
          this.eventCounter(params.data.triggers.event.count.value)
          this.eventOptionValue(params.data.triggers.event.count.flush_counter)

          this.enableEventTimer(!!+params.data.triggers.event.time.active)
          this.eventTimer(params.data.triggers.event.time.value)
          this.eventTimerOptionValue(params.data.triggers.event.time.timer_type)
        }

        // Добавляем обработку данных для scroll_depth
        if (params.data.triggers.scroll_depth) {
          if (params.data.triggers.scroll_depth.vertical) {
            this.enableVerticalScroll(true);
            this.verticalScrollUnit(Number(params.data.triggers.scroll_depth.vertical.type));
            this.verticalScrollValue.pureValue(params.data.triggers.scroll_depth.vertical.value);
          }
          
          if (params.data.triggers.scroll_depth.horizontal) {
            this.enableHorizontalScroll(true);
            this.horizontalScrollUnit(Number(params.data.triggers.scroll_depth.horizontal.type));
            this.horizontalScrollValue.pureValue(params.data.triggers.scroll_depth.horizontal.value);
          }
          
          if (this.enableDepth() && this.enableTarget()) {
            this.targetCounter(this.targetCounter() + 1);
          }
        }
      }

      if (this.edit) {
        if (this.enableCookie()) {
          this.showCookie(true)
        }
        if (this.enableVariable()) {
          this.showVariable(true)
        }
        if (this.enableType()) {
          this.showType(true)
        }
        if (this.enableUser()) {
          this.showUser(true)
        }
        if (this.enablePages()) {
          this.showPages(true)
        }
        if (this.enableEvents()) {
          this.showEvents(true)
        }
        if (this.enableDepth()) {
          this.showDepth(true)
        }
      }

      const windowSettings = params.data.window_settings;
      if (windowSettings) {
        this.windowWidthUnit(Number(windowSettings.width.type));
        this.windowWidth.value(windowSettings.width.value);

        this.windowHeight.value(windowSettings.height);
  
        this.windowBorderRadiusUnit(Number(windowSettings.round.type));
        this.windowBorderRadius.value(windowSettings.round.value);
  
        this.windowPosition(windowSettings.position);
      } else {
        this.windowPosition(this.defaultWindowSettings.windowPosition());
      }
    }

    this.enableAutoClose.subscribe((val) => {
      if (val) {
        this.scrollToTheEndOfSidesheet();
      }
      if (!val) {
        this.autoCloseDelay('');
        this.autoCloseDelayError(false);
      }
    });

    this.autoCloseDelay.subscribe((val) => {
      if (val) {
        this.autoCloseDelayError(false);
      }
    });

    const defaulPosition = this.defaultWindowSettings.windowPosition;
    const defaulWindowWidthUnit = this.defaultWindowSettings.windowWidthUnit;
    this.windowSettingsEdited = this.edit;

    this.windowPosition.subscribe(() => {
      this.windowSettingsEdited = true;
    });

    this.windowWidthUnit.subscribe(() => {
      this.windowSettingsEdited = true;
    });
    [
      this.noClickMode,
      this.showMode,
      this.simpleView,
    ].forEach(i => {
      i.subscribe(() => {
        setTimeout(() => {
          if (!this.windowSettingsEdited) {
            this.windowPosition(defaulPosition());
            this.windowWidthUnit(defaulWindowWidthUnit());
            this.windowSettingsEdited = false;
          }
        })
      });
    });

    [
      this.showMode,
      this.buttonMode,
      this.noClickMode,
      this.buttonPlacement,
      this.font,
      this.fontSize,
      this.bold,
      this.italic,
      this.simpleView,
      this.border,
      this.color,
      this.background,
      this.filialId
    ].forEach(i => {
      i.subscribe((v) => {
        this.canSave(true)
      })
    })

    this.visitCounter.subscribe((v) => {
      if (v) {
        this.visitCounterError(false)
      }
    })

    this.visitTimer.subscribe((v) => {
      if (v) {
        this.visitTimerError(false)
      }
    })

    this.eventCounter.subscribe((v) => {
      if (v) {
        this.eventCounterError(false)
      }
    })

    this.eventTimer.subscribe((v) => {
      if (v) {
        this.eventTimerError(false)
      }
    })

    this.cookiePc.subscribe((v) => {
      if (v) {
        this.typesError(false)
      }
    })
    this.cookiePad.subscribe((v) => {
      if (v) {
        this.typesError(false)
      }
    })
    this.cookiePhone.subscribe((v) => {
      if (v) {
        this.typesError(false)
      }
    })

    this.enableType.subscribe((v) => {
      if (v) {
        this.targetCounter(this.targetCounter() + 1)
        this.showType(true)
      } else {
        this.targetCounter(this.targetCounter() - 1)
        this.showType(false)
      }
    })

    this.enableUser.subscribe((v) => {
      if (v) {
        this.showUser(true)
        this.targetCounter(this.targetCounter() + 1)
      } else {
        this.showUser(false)
        this.targetCounter(this.targetCounter() - 1)
      }
    })

    this.enableCookie.subscribe((v) => {
      const cookiesLength = this.cookies().length
      if (v) {
        this.showCookie(true)
        this.targetCounter(this.targetCounter() + cookiesLength)
        if (!this.cookies().length) {
          this.addCookieValue()
        }
      } else {
        this.showCookie(false)
        this.targetCounter(this.targetCounter() - cookiesLength)
      }
    })

    this.enableVariable.subscribe((v) => {
      const variablesLength = this.variables().length
      if (v) {
        this.showVariable(true)
        this.targetCounter(this.targetCounter() + variablesLength)
        if (!this.variables().length) {
          this.addVariableValue()
        }
      } else {
        this.showVariable(false)
        this.targetCounter(this.targetCounter() - variablesLength)
      }
    })
    this.enableTag.subscribe((v) => {
      const tagsLength = this.tags().length
      if (v) {
        this.showTag(true)
        this.targetCounter(this.targetCounter() + tagsLength)
        if (!this.tags().length) {
          this.addTag()
        }
      } else {
        this.showTag(false)
        this.targetCounter(this.targetCounter() - tagsLength)
      }
    })
    this.enablePages.subscribe((v) => {
      const pagesLength = this.pages().length
      if (v) {
        this.showPages(true)
        this.targetCounter(this.targetCounter() + pagesLength)
        if (!this.pages().length) {
          this.addPage()
        }
      } else {
        this.targetCounter(this.targetCounter() - pagesLength)
        this.showPages(false)
      }
    })
    this.enableEvents.subscribe((v) => {
      const events_Length = this.events_().length
      if (v) {
        this.showEvents(true)
        this.targetCounter(this.targetCounter() + events_Length)
        if (!this.events_().length) {
          this.addEvent()
        }
      } else {
        this.targetCounter(this.targetCounter() - events_Length)
        this.showEvents(false)
      }
    })
    this.enableDepth.subscribe((v) => {
      if (v) {
        this.showDepth(true);
        this.targetCounter(this.targetCounter() + 1);
      } else {
        this.showDepth(false);
        this.targetCounter(this.targetCounter() - 1);
        this.depthError(false);
      }
    });
    this.enableTarget.subscribe((v) => {
      if (!v) {
        this.targetCounter(0)
      } else {
        this.targetCounter((this.enableUser() ? 1 : 0) + (this.enableType() ? 1 : 0))
        if (this.enablePages()) {
          this.targetCounter(this.targetCounter() + this.pages().length)
        }
        if (this.enableCookie()) {
          this.targetCounter(this.targetCounter() + this.cookies().length)
        }
        if (this.enableVariable()) {
          this.targetCounter(this.targetCounter() + this.variables().length)
        }
        if (this.enableEvents()) {
          this.targetCounter(this.targetCounter() + this.events_().length)
        }
        if (this.enableTag()) {
          this.targetCounter(this.targetCounter() + this.tags().length)
        }
        if (this.enableDepth()) {
          this.targetCounter(this.targetCounter() + 1)
        }
      }
    })
    this.editFormWidgetName = ko.observable('');
    this.widgetName.subscribe(() => {
      if (this.cancelSave) return;

      this.canSave(true)
      this.submit(true)
      this.edit = true
    })

    this.addScript()
    this.pollScale = ko.observable(null);
    this.pollLink = ko.observable(null);

    if (this.poll()) {
      this.getPollInfo();
    } else {
      this.poll.subscribe(() => {
        this.getPollInfo();
      })
    }

    this.enableVerticalScroll.subscribe((v) => {
      if (v) {
        this.depthError(false);
        // Сбрасываем ошибку и флаг isSubmitted при включении вертикальной прокрутки
        this.verticalScrollValueError('');
        if (!this.verticalScrollValue.pureValue()) {
          this.isSubmitted(false);
        }
      }
    });

    this.enableHorizontalScroll.subscribe((v) => {
      if (v) {
        this.depthError(false);
        // Сбрасываем ошибку и флаг isSubmitted при включении горизонтальной прокрутки
        this.horizontalScrollValueError('');
        if (!this.horizontalScrollValue.pureValue()) {
          this.isSubmitted(false);
        }
      }
    });

    this.verticalScrollUnit.subscribe((newUnit) => {
      if (this.verticalScrollValue.pureValue()) {
        if (newUnit === 0) {
          const value = this.verticalScrollValue.pureValue();
          if (value) {
            const numbers = value.split(', ')
            
            this.verticalScrollValue.pureValue(numbers.join(', '));
          }
        }
        
        this.verticalScrollValue.pureValue.isModified(true);
      }
    });

    this.horizontalScrollUnit.subscribe((newUnit) => {
      if (this.horizontalScrollValue.pureValue()) {
        if (newUnit === 0) {
          const value = this.horizontalScrollValue.pureValue();
          if (value) {
            const numbers = value.split(', ')
            
            this.horizontalScrollValue.pureValue(numbers.join(', '));
          }
        }
        
        this.horizontalScrollValue.pureValue.isModified(true);
      }
    });

    this.validateVerticalScrollValue = ko.computed(() => {
      if (!this.verticalScrollValue.pureValue() || !this.enableVerticalScroll()) return true;
      
      const value = this.verticalScrollValue.pureValue();
      if (!value) return true;
      
      try {
        const numbers = value.split(', ').map(n => parseInt(n, 10));
        
        if (this.verticalScrollUnit() === 0) {
          // Проверяем только, что числа больше 0, но не ограничиваем их до 100
          return numbers.every(n => n > 0);
        }
        
        return numbers.every(n => n > 0);
      } catch (e) {
        return false;
      }
    });
    
    this.validateHorizontalScrollValue = ko.computed(() => {
      if (!this.horizontalScrollValue.pureValue() || !this.enableHorizontalScroll()) return true;
      
      const value = this.horizontalScrollValue.pureValue();
      if (!value) return true;
      
      try {
        const numbers = value.split(', ').map(n => parseInt(n, 10));
        
        if (this.horizontalScrollUnit() === 0) {
          // Проверяем только, что числа больше 0, но не ограничиваем их до 100
          return numbers.every(n => n > 0);
        }
        
        return numbers.every(n => n > 0);
      } catch (e) {
        return false;
      }
    });

    setTimeout(() => {
      const verticalInput = element.querySelector('[data-bind*="verticalScrollValue"]');
      const horizontalInput = element.querySelector('[data-bind*="horizontalScrollValue"]');
      
      if (verticalInput) {
        verticalInput.addEventListener('input', (event) => {
          // Удаляем точки из значения
          if (event.target.value.includes('.')) {
            const cursorPos = event.target.selectionStart;
            const newValue = event.target.value.replace(/\./g, '');
            if (newValue !== event.target.value) {
              event.target.value = newValue;
              setTimeout(() => {
                event.target.selectionStart = Math.max(0, cursorPos - 1);
                event.target.selectionEnd = Math.max(0, cursorPos - 1);
              }, 0);
            }
          }
          
          clearTimeout(this._verticalInputTimeout);
          this._verticalInputTimeout = setTimeout(() => {
            this.formatScrollInput(event.target.value, true);
          }, 300);
        });
      }
      
      if (horizontalInput) {
        horizontalInput.addEventListener('input', (event) => {
          // Удаляем точки из значения
          if (event.target.value.includes('.')) {
            const cursorPos = event.target.selectionStart;
            const newValue = event.target.value.replace(/\./g, '');
            if (newValue !== event.target.value) {
              event.target.value = newValue;
              // Устанавливаем курсор в правильную позицию
              setTimeout(() => {
                // Если точка была удалена перед курсором, смещаем курсор на одну позицию назад
                event.target.selectionStart = Math.max(0, cursorPos - 1);
                event.target.selectionEnd = Math.max(0, cursorPos - 1);
              }, 0);
            }
          }
          
          clearTimeout(this._horizontalInputTimeout);
          this._horizontalInputTimeout = setTimeout(() => {
            this.formatScrollInput(event.target.value, false);
          }, 300);
        });
      }
    }, 500); // Даем время для инициализации компонентов
  }

  resetWindowSettings() {
    const {
      windowWidthUnit,
      windowWidth,
      windowHeight,
      windowBorderRadiusUnit,
      windowBorderRadius,
      windowPosition,
    } = this.defaultWindowSettings;

    this.windowWidthUnit(windowWidthUnit());
    this.windowWidth.value(windowWidth);

    this.windowHeight.value(windowHeight);

    this.windowBorderRadiusUnit(windowBorderRadiusUnit);
    this.windowBorderRadius.value(windowBorderRadius);

    this.windowPosition(windowPosition());
    this.windowSettingsEdited = false;
  }

  openWidget(type, params = {}) {
    if (this.poll()) {
      $('.fz-overlay').remove()
      let simple = 0 
      
      if ((this.simpleView() && this.noClickMode() !== 'page-stop') || this.noClickMode() == 'lite-page-stop' || this.noClickMode() == 'hello') {
        simple = 1
      }

      if (this.showMode() == 'click') {
        simple = this.simpleView() ? 1 : 0
      }

      params.quiz = this.pollLink();
      params.simpleView = simple;
      params.close_by_finish_button = this.closeByFinishButton() ? 1 : 0;
      params.windowSettings = {
        width: {
          type: this.windowWidthUnit(),
          value: this.windowWidth.pureValue(),
        },
        height: this.windowHeight.pureValue(),
        round: {
          type: this.windowBorderRadiusUnit(),
          value: this.windowBorderRadius.pureValue(),
        },
        position: this.windowPosition(),
      }

      params.autoclose_delay = this.autoCloseDelay()

      let widgetParams = {
        source: "foquz",
        type: "fz:preview",
        config: {
          mode: type,
          params,
          //feedback: this.getFeedbackParams(),
        },
      };

      window.postMessage(widgetParams, "*");
    }
  }

  openNoClickWidget() {
    const param = {
      hello: this.noClickMode() == 'hello' && this.showMode() == 'no-click',
    };
    if (this.closeByFinishButton()) {
      param['close_by_finish_button'] = 1;
    }
    this.openWidget("quiz", param);
  }
  onRatingClick() {
    this.openWidget("quiz", {
      view: 2,
    });
  }

  getPollInfo() {
    $.ajax({
      url: ApiUrl("poll/widget-poll-info", { id: this.poll(), new: 1 }),
      success: (response) => {
        this.pollScale(response.ratingScale)
        this.pollLink(response.filialPollKey)
      },
      error: (response) => {
        console.error(response.responseJSON);
      },
    });
  }

  getItemsLinks(id) {
    $.ajax({
      url: ApiUrl("test-poll/link-simple", { id: id }),
      success: (response) => {
        this.pollLinksOptionsWidgets(response.items)
      },
      error: (response) => {
        console.error(response.responseJSON);
      },
    });
  }

  addCookieValue() {
    const obj = valueObj(this.cookies().length + 1)
    this.cookies.push(obj)
    if (this.enableCookie()) {
      this.targetCounter(this.targetCounter() + 1)
    }
  }

  addVariableValue() {
    const obj = valueObj(this.variables().length + 1)
    this.variables.push(obj)
    if (this.enableVariable()) {
      this.targetCounter(this.targetCounter() + 1)
    }
  }
  removeCookie(item) {
    const obj = this.cookies().find(i => i.cookieValue() == item.cookieValue() && i.cookieText() == item.cookieText() && i.cookieValueMode() == item.cookieValueMode())
    this.cookies.remove(obj)
    if (this.enableCookie()) {
      this.targetCounter(this.targetCounter() - 1)
    }
  }

  removeVariable(item) {
    const obj = this.variables().find(i => i.cookieValue() == item.cookieValue() && i.cookieText() == item.cookieText() && i.cookieValueMode() == item.cookieValueMode())
    this.variables.remove(obj)
    if (this.enableVariable()) {
      this.targetCounter(this.targetCounter() - 1)
    }
  }

  removePage(item) {
    const obj = this.pages().find(i => i.adress() == item.adress() && i.rool() == item.rool())
    this.pages.remove(obj)
    if (this.enablePages()) {
      this.targetCounter(this.targetCounter() - 1)
    }
  }

  removeEvent(item) {
    const obj = this.events_().find(i => i.adress() == item.adress() && i.rool() == item.rool())
    this.events_.remove(obj)
    if (this.enableEvents()) {
      this.targetCounter(this.targetCounter() - 1)
    }
  }

  addPage() {
    const obj = pageObj(this.pages().length + 1)
    this.pages.push(obj)
    if (this.enablePages()) {
      this.targetCounter(this.targetCounter() + 1)
    }
  }

  addEvent() {
    const obj = pageObj(this.events_().length + 1)
    this.events_.push(obj)
    if (this.enableEvents()) {
      this.targetCounter(this.targetCounter() + 1)
    }
  }

  removeTag(item) {
    const obj = this.tags().find(i => i.id == item.id)
    this.tags.remove(obj)
    if (this.enableTag()) {
      this.targetCounter(this.targetCounter() - 1)
    }
    
  }

  addTag() {
    const obj = tagObj(this.tags().length + 1)
    this.tags.push(obj)
    if (this.enableTag()) {
      this.targetCounter(this.targetCounter() + 1)
    }
    
  }

  copyHTML() {
    copyToClipboard(this.widgetCode());
    HidePoppersEvent.emit();

    this.codeCopied(true);
  }

  addScript() {
    window.FoquzSDK={name:'',test: 0}

    const origin = window.location.origin;
    window.FOQUZ_SDK = {
      preview: true,
      _test: {
        assetsRoot: origin + '/',
        // vueAppBaseUrl: 'http://localhost:5173',
      },
      widget: {
        widget_code: this.code(),
      },
      params: {},
    };


    const script = document.createElement('script');
    script.src = `${origin}/widgets/poll/widget_new.js`;
    script.type = 'text/javascript';
    script.async = true;
    document.head.appendChild(script);
  }

  reset() {
    this.$element.find('.foquz-dialog__close').click()
  }
  
  submit(cancelHide) {
    this.isSubmitted(true);
    if (!this.formModel.isValid()) return;
    
    this.windowSettingsEdited = true;
    const formData = new FormData
    let empty = false
    
    formData.append('window_settings[width][type]', this.windowWidthUnit());
    formData.append('window_settings[width][value]', this.windowWidth.pureValue());
    formData.append('window_settings[height]', this.windowHeight.pureValue());
    formData.append('window_settings[round][type]', this.windowBorderRadiusUnit());
    formData.append('window_settings[round][value]', this.windowBorderRadius.pureValue());
    formData.append('window_settings[position]', this.windowPosition());

    formData.append('close_by_finish_button', this.closeByFinishButton() ? 1 : 0);
    formData.append('priority', this.formattedPriority());

    if (this.enableAutoClose()) {
      const delayVal = parseInt(this.autoCloseDelay(), 10) || 0;
      if (!delayVal || delayVal <= 0) {
        this.autoCloseDelayError(true);
        empty = true;
      } else {
        formData.append('autoclose_delay', delayVal);
      }
    } else {
      formData.append('autoclose_delay', 0);
    }

    formData.append('poll_id', this.poll())
    if (this.editFormWidgetName()) {
      this.cancelSave = true;
      this.widgetName(this.editFormWidgetName());
      this.editFormWidgetName('');
      this.cancelSave = false;
    }
    formData.append('name', this.widgetName())
    formData.append('is_active', this.isActive() ? 1 : 0)
    formData.append('show_until', this.showAction())
    formData.append('poll_link_id', this.pollLinkId())
    formData.append('filial_id', this.filialId())
    formData.append('appearance', this.showMode() === 'click' ? 1 : 0)

    if (this.showMode() === 'click') {
      const modes = ['text', 'icon', 'scale']
      formData.append('form', modes.indexOf(this.buttonMode()))
      formData.append('button_type', modes.indexOf(this.buttonMode()))
      formData.append('position', this.buttonPlacement() === 'fix' ? 0 : this.buttonPlacement() === 'left' ? 1 : 2)
      formData.append('button_text', this.buttonText())
      formData.append('font', this.font())
      formData.append('font_size', this.fontSize())
      formData.append('bold', this.bold() ? 1 : 0)
      formData.append('italic', this.italic() ? 1 : 0)
      formData.append('text_color', this.color())
      formData.append('background_color', this.background())
      formData.append('stroke', this.border() ? 1 : 0)
      formData.append('simple', this.simpleView() ? 1 : 0)

    } else {
      formData.append('form', this.noClickMode() === 'page-stop' ? 3 : this.noClickMode() === 'lite-page-stop' ? 4 : 5)
    }

    

    formData.append('targeting', this.enableTarget() ? 1 : 0)

    for (const key in targetSettings) {
      const setObj = targetSettings[key]
      formData.append(`triggers_status[${key}]`, this[setObj.trigger]() ? 1 : 0)

        if (key == 'url') {
          formData.append(`triggers[url][hashtag]`, this.enableHash() ? 1 : 0)
          formData.append(`triggers[url][visits_count][active]`, this.enableVisitCounter() ? 1 : 0)
          formData.append(`triggers[url][visits_count][value]`, this.visitCounter())
          formData.append(`triggers[url][visits_count][flush_counter]`, this.visitOptionValue())
          formData.append(`triggers[url][time][active]`, this.enableVisitTimer() ? 1 : 0)
          formData.append(`triggers[url][track_history_changes]`, this.trackHistoryChanges() ? 1 : 0)
          formData.append(`triggers[url][time][value]`, this.visitTimer())
          formData.append(`triggers[url][time][timer_type]`, this.visitTimerOptionValue())
          if (this.enableVisitCounter() && !this.visitCounter() && this.enableTarget()) {
            this.visitCounterError(true)
            empty = true
          }
          if (this.enableVisitTimer() && !this.visitTimer() && this.enableTarget()) {
            this.visitTimerError(true)
            empty = true
          }
        }

        if (key == 'event') {
          formData.append(`triggers[event][count][active]`, this.enableEventCounter() ? 1 : 0)
          if (this.enableEventCounter() && !this.eventCounter() && this.enableTarget()) {
            this.eventCounterError(true)
            empty = true
          }
          formData.append(`triggers[event][count][value]`, this.eventCounter())
          formData.append(`triggers[event][count][flush_counter]`, this.eventOptionValue())
          formData.append(`triggers[event][time][active]`, this.enableEventTimer() ? 1 : 0)
          if (this.enableEventTimer() && !this.eventTimer() && this.enableTarget()) {
            this.eventTimerError(true)
            empty = true
          }
          formData.append(`triggers[event][time][value]`, this.eventTimer())
          formData.append(`triggers[event][time][timer_type]`, this.eventTimerOptionValue())
        }
        if (setObj.array && this[setObj.array]()) {

          this[setObj.array]().forEach((i, index) => {
            if (key == 'cookies' || key == 'variables') {
              if (!i.cookieText().length && this.enableTarget() && this[setObj.trigger]()) {
                i.textError(true)
                empty = true
              }
              if (i.cookieValueMode() && !i.cookieValueModeText().length && this.enableTarget() && this[setObj.trigger]()) {
                i.modeTextError(true)
                empty = true
              }
            }

            if (key == 'client_tags') {
              if (!i.tags().length && this.enableTarget() && this[setObj.trigger]()) {
                i.tagError(true)
                empty = true
              }

              formData.append(`triggers[client_tags][${index}][condition]`, i.value())
              i.tags().forEach((t, p)=> {
                formData.append(`triggers[client_tags][${index}][values][${p}]`, t)
              })
            }

            if (key == 'url') {
              if (!i.adress().length && this.enableTarget() && this[setObj.trigger]()) {
                i.adressError(true)
                empty = true
              }
              formData.append(`triggers[url][conditions][${index}][condition]`, i.rool())
              formData.append(`triggers[url][conditions][${index}][value]`, i.adress())
            }

            if (key == 'event') {
              if (!i.adress().length && this.enableTarget() && this[setObj.trigger]()) {
                i.adressError(true)
                empty = true
              }
              formData.append(`triggers[event][conditions][${index}][condition]`, i.rool())
              formData.append(`triggers[event][conditions][${index}][value]`, i.adress())
            }

            if (setObj.needAppend) {
              setObj.needAppend.forEach((el) => {
                formData.append(`triggers[${key}][${index}][${el.name}]`, i[el.value]())
              })
            }

          })
        }
        
        
    }

    formData.append('triggers_status[devices]', this.enableType() ? 1 : 0)

    formData.append('triggers_status[coverage]', this.enableUser() ? 1 : 0)

    formData.append(`triggers[devices][desktop]`, this.cookiePc() ? 1 : 0)
    formData.append(`triggers[devices][tablet]`, this.cookiePad() ? 1 : 0)
    formData.append(`triggers[devices][smartphone]`, this.cookiePhone() ? 1 : 0)
    formData.append(`triggers[coverage]`, this.userPercent())

    

    if (this.enableType() || this.data?.triggers?.devices?.length) {

      if (!this.cookiePc() && !this.cookiePad() && !this.cookiePhone() && this.enableTarget()) {
        this.typesError(true)
        empty = true
      }
        
    }

    if (this.enableDepth()) {
      this.depthError(false);
      
      if (!this.enableVerticalScroll() && !this.enableHorizontalScroll()) {
        this.depthError(true);
        empty = true;
      } else {
        if (this.enableVerticalScroll()) {
          if (!this.verticalScrollValue.pureValue()) {
            this.verticalScrollValue.pureValue.isModified(true);
            this.verticalScrollValueError('Обязательное поле');
            empty = true;
          } else {
            const value = this.verticalScrollValue.pureValue();
            const numbers = this._extractNumbers(value);
            
            if (numbers.length === 0) {
              this.verticalScrollValueError('Нужно указать целое число, если чисел несколько, то через запятую');
              empty = true;
            } else {
              const hasZeroOrNegative = numbers.some(n => n <= 0);
              
              if (hasZeroOrNegative) {
                this.verticalScrollValueError('Значение должно быть больше 0');
                empty = true;
              } else if (this.verticalScrollUnit() === 0) {
                const hasNumbersOver100 = numbers.some(n => n > 100);
                
                if (hasNumbersOver100) {
                  this.verticalScrollValueError('Максимальное значение 100%');
                  empty = true;
                }
              }
              
              if (!empty) {
                this.verticalScrollValue.pureValue(numbers.join(', '));
              }
            }
          }
        }
        
        if (this.enableHorizontalScroll()) {
          if (!this.horizontalScrollValue.pureValue()) {
            this.horizontalScrollValue.pureValue.isModified(true);
            this.horizontalScrollValueError('Обязательное поле');
            empty = true;
          } else {
            const value = this.horizontalScrollValue.pureValue();
            const numbers = this._extractNumbers(value);
            
            if (numbers.length === 0) {
              this.horizontalScrollValueError('Нужно указать целое число, если чисел несколько, то через запятую');
              empty = true;
            } else {
              const hasZeroOrNegative = numbers.some(n => n <= 0);
              
              if (hasZeroOrNegative) {
                this.horizontalScrollValueError('Значение должно быть больше 0');
                empty = true;
              } else if (this.horizontalScrollUnit() === 0) {
                const hasNumbersOver100 = numbers.some(n => n > 100);
                
                if (hasNumbersOver100) {
                  this.horizontalScrollValueError('Максимальное значение 100%');
                  empty = true;
                }
              }
              
              if (!empty) {
                this.horizontalScrollValue.pureValue(numbers.join(', '));
              }
            }
          }
        }
      }
    }

    formData.append('triggers_status[scroll_depth]', this.enableDepth() ? 1 : 0);

    if (this.enableVerticalScroll()) {
      formData.append('triggers[scroll_depth][vertical][type]', this.verticalScrollUnit());
      formData.append('triggers[scroll_depth][vertical][value]', this.verticalScrollValue.pureValue());
    }

    if (this.enableHorizontalScroll()) {
      formData.append('triggers[scroll_depth][horizontal][type]', this.horizontalScrollUnit());
      formData.append('triggers[scroll_depth][horizontal][value]', this.horizontalScrollValue.pureValue());
    }

    if (empty) return

    const self = this
    if (!this.edit) {
      $.ajax({
        url : `/foquz/api/poll/create-widget?access-token=${window.APIConfig.apiKey}`,
        data : formData,
        type : "POST",
        processData: false,
        contentType: false,
        error(res) {
          self.cancelSave = false;
          self.processError(res);
        },
        success(res) {
          self.cancelSave = false;
          self.params.callback({edit: false, data: res.model});
          self.widgetId = res.model.id;
          HidePoppersEvent.emit();
          self.showSuccessMessage(true);
          self.edit = true;
          self.code(res.model.code);
          self.widgetName(res.model.name);
        }
      })
    } else {
      $.ajax({
        url : `/foquz/api/poll/update-widget?id=${self.widgetId}&access-token=${window.APIConfig.apiKey}`,
        data : formData,
        type : "PUT",
        processData: false,
        contentType: false,
        error(res) {
          self.cancelSave = false;
          self.processError(res);
        },
        success(res) {
          self.cancelSave = false;
          self.params.callback({edit: true, data: res.model});
          HidePoppersEvent.emit();
          self.showSuccessMessage(true);
          self.code(res.model.code);
          self.widgetName(res.model.name);
        }
      })
    }
  }

  _extractNumbers(str) {
    if (typeof str !== 'string') {
      str = String(str || '');
    }
    
    str = str.replace(/\./g, '');
    
    const parts = str.split(/[,\s]+/).filter(part => part.trim() !== '');
    
    return parts.map(part => {
      const num = parseInt(part, 10);
      return isNaN(num) ? 0 : num;
    }).filter(num => num !== 0); // Фильтруем нулевые значения
  }

  getTags() {
    const self = this
    $.ajax({
      url : `/foquz/api/poll/tags?all=1&access-token=${window.APIConfig.apiKey}`,
      type : "GET",
      processData: false,
      contentType: false,
      success : function (res) {
        self.tagOptions(res.items)
      }
    })
  }

  processError(res) {
    const errors = res?.responseJSON?.errors;
    if (!errors) {
      console.error('неизвестная ошибка сервера');
      return;
    }

    if ('priority' in errors) {
      this.priorityError(errors['priority']);
    }
  }

  restrictScrollInput(data, event) {
    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', ',', ' ', '.'];
    const input = event.target;
    const value = input.value;
    const cursorPos = input.selectionStart;
    
    if (event.key === '.') {
      if (cursorPos > 0 && value.charAt(cursorPos - 1) === ' ') {
        event.preventDefault();
        return false;
      }
    }
    
    if (event.inputType === 'insertText' && event.data === '.') {
      if (cursorPos > 1 && value.charAt(cursorPos - 2) === ' ') {
        const newValue = value.substring(0, cursorPos - 1) + value.substring(cursorPos);
        input.value = newValue;
        
        setTimeout(() => {
          input.selectionStart = cursorPos - 1;
          input.selectionEnd = cursorPos - 1;

          const inputEvent = new Event('input', { bubbles: true });
          input.dispatchEvent(inputEvent);
        }, 0);
      }
    }
    
    if (event.ctrlKey && event.key === 'v') {
      setTimeout(() => {
        const pastedValue = input.value;
        
        const cleanedValue = pastedValue.replace(/[^\d,\s]/g, '');
        
        if (cleanedValue !== pastedValue) {
          input.value = cleanedValue;
          
          const inputEvent = new Event('input', { bubbles: true });
          input.dispatchEvent(inputEvent);
        }
      }, 0);
      
      return true;
    }
    
    if (/^\d$/.test(event.key)) {
      const isPercentageField = input.closest('[params*="verticalScrollUnit"]') 
        ? this.verticalScrollUnit() === 0 
        : this.horizontalScrollUnit() === 0;
      
      if (!isPercentageField) {
        return true;
      }
      
      return true;
    }
    
    if (!/^\d$/.test(event.key) && !allowedKeys.includes(event.key)) {
      if (!(event.ctrlKey && event.key === 'v')) {
        event.preventDefault();
        return false;
      }
    }
    
    setTimeout(() => {
      if (input.value.includes('.')) {
        const newValue = input.value.replace(/\./g, '');
        if (newValue !== input.value) {
          input.value = newValue;
          
          const inputEvent = new Event('input', { bubbles: true });
          input.dispatchEvent(inputEvent);
        }
      }
    }, 0);
    
    return true;
  }
  
  formatScrollInput(value, isVertical) {
    if (isVertical) {
      this.verticalScrollValueError('');
    } else {
      this.horizontalScrollValueError('');
    }
    
    if (typeof value === 'string') {
      value = value.replace(/\./g, '');
    }
    
    const unit = isVertical ? this.verticalScrollUnit() : this.horizontalScrollUnit();
    
    if (unit === 0) {
      const numbers = this._extractNumbers(value);
      
      const hasNumbersOver100 = numbers.some(n => n > 100);
      
      if (hasNumbersOver100) {
        if (isVertical) {
          this.verticalScrollValueError('Максимальное значение 100%');
        } else {
          this.horizontalScrollValueError('Максимальное значение 100%');
        }
      }
    }
    
    return value;
  }

  scrollToTheEndOfSidesheet(delay = 300) {
    setTimeout(() => {
      const el = this.element

      if (!el) return

      const scrollableContainer
      window.scrollTo(0, document.body.scrollHeight);
    }, delay);
  }
}
