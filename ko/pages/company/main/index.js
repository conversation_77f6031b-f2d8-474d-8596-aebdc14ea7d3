import { get as _get } from "lodash";

import './components';
import { TariffModel } from 'Legacy/models/tariff';
import { PaymentModel } from './payment-model';
import 'Components/affiliate-companies-table';
import { AffiliateCompaniesTable } from 'Models/affiliate-companies';

import 'Dialogs/service-old-sidesheet';
import { PromocodesTable } from './promocodes-table';
import { htmlToText } from '../../../utils/string/html-to-text';

var IIKO_TYPE = '1';

const ServiceAccessViewModel = function (data) {
  this.id = data.id;
  this.service = ko.observable(data.service);
  this.name = ko.observable(data.name);
  this.connectedAt = data.connectedAt;

  if (data.service == IIKO_TYPE) {
    this.mainApiUrl = data.mainApiUrl;
    this.additionalApiUrl = data.additionalApiUrl;
    this.login = data.login;
    this.password = data.password;
  }

  this.active = ko.observable(data.active);
};
let dateFormat = function (dte) {
  if (dte == null) {
    return '-';
  }

  let dteSplit = dte.substr(0, 11).split('-');
  let year = dteSplit[0]; //special yr format, take last 2 digits
  let month = dteSplit[1];
  let day = dteSplit[2];
  return day + '.' + month + '.' + year;
};
let setData = function (data) {
  let newData = [];
  for (let i in data) {
    viewModel.formModel().serviceAccesses.splice(
      0,
      0,
      new ServiceAccessViewModel({
        id: data[i]['id'],
        service: data[i]['service_type_id'],
        name: data[i]['name'],
        connectedAt: dateFormat(data[i]['date_connect']),
        mainApiUrl: data[i]['url'],
        additionalApiUrl: data[i]['url2'],
        login: data[i]['username'],
        password: data[i]['password'],
        active: Number(data[i]['is_active'])
      })
    );
  }
  return newData;
};
let urlParams = {};
let ajaxRequest = function (url, oldData = false) {
  $('#reviews-loader').css('display', '');
  viewModel.formModel().serviceAccesses.remove((m) => []);
  $.ajax({
    url: url,
    success: function (data) {
      setTimeout(function () {
        $('#reviews-loader').css('display', 'none');
        setData(data);
      }, 300);
    },
    error: function () {
      $('#reviews-loader').css('display', 'none');
    }
  });
};

let createUrl = function (basicUrl, urlParams) {
  let str = '';
  for (var key in urlParams) {
    str = str + key + '=' + urlParams[key] + '&';
  }
  return basicUrl + '?' + str;
};

$('body').on('change', '.update_active', function () {
  let idService = $(this).attr('idService');
  let checked = $(this).prop('checked') == true ? 1 : 0;
  $.ajax({
    type: 'POST',
    url: '/foquz/api/company-access/update?id=' + idService,
    data: { CompanyIikoAccess: { is_active: checked } },
    success: function (id) {
      window.companyId = id;
      $('#nav-service-accesses-tab').click();
    }
  });
});
$('body').on('click', '.sort_column', function () {
  let sort = $(this).attr('attr-sort');
  let typeSort = $(this).attr('type-sort');
  let ts = '';
  $('.foq-table__sorting-icon').remove();
  if (typeSort == 'asc') {
    $(this).attr('type-sort', 'desc');
    $(this).after(
      '<i class="foq-table__sorting-icon foq-table__sorting-icon--order_asc"></i>'
    );
  } else {
    $(this).attr('type-sort', 'asc');
    ts = '-';
    $(this).after(
      '<i class="foq-table__sorting-icon foq-table__sorting-icon--order_desc"></i>'
    );
  }
  $(this).parent().find('');
  urlParams.companyId = $.urlParam('id');
  urlParams.sort = ts + sort;
  let newUrl = createUrl('/foquz/api/company-access/get-list', urlParams);
  // let newUrl = createUrl('api/company-access/get-list',urlParams)
  // let newUrl = '/foquz/api/company-access/get-list?companyId='+$.urlParam('id')
  ajaxRequest(newUrl);
});

$('body').on('keypress', '.foq-table__head-cell-filter', function (e) {
  if (e.which == 13) {
    let val = $(this).val();
    let title = $(this).attr('attr-name');
    let sort = urlParams.sort;
    urlParams = {};
    urlParams.sort = sort;
    urlParams.companyId = $.urlParam('id');
    urlParams[title] = val;
    let newUrl = createUrl('/foquz/api/company-access/get-list', urlParams);
    ajaxRequest(newUrl);
  }
});

const ViewModel = function (mode, data) {
  PageModel(this);

  this.dialogs = ko.observable(null);

  if (data && data.name) {
    data.name = htmlToText(data.name);
  }

  this.mode = mode;
  this.data = data;

  this.isSubmitted = ko.observable(this.mode === 'edit');
  this.pending = ko.observable(false);

  this.tariffModel = new TariffModel(data, { canEdit: true });

  this.isBaseTariff = ko.computed(() => {
    const isBaseTariff = this.tariffModel.currentTariffId() <= 1;
    return isBaseTariff;
  });

  this.paymentModel = new PaymentModel(data, DATA_TOKEN);

  this.serverErrors = {
    username: ko.observable(''),
    alias: ko.observable('')
  };

  this.formModel = (() => {
    const formModel = ko.validatedObservable(null, {
      deep: true,
      live: true
    });

    formModel({
      name: ko.observable(this.mode === 'edit' ? this.data.name : '').extend({
        required: {
          message: 'Обязательное поле'
        }
      }),
      companyName: ko.observable(this.mode === 'edit' ? this.data.name : ''),
      domain: ko
        .observable(this.mode === 'edit' ? this.data.domain : '')
        .extend({
          required: {
            message: 'Обязательное поле'
          },
          validation: [
            {
              validator: (value) => {
                return validator.isFQDN(value) || value.includes('localhost:');
              },
              message: 'Неверный формат параметра «Домен»'
            },
            {
              validator: () => !this.serverErrors.alias(),
              message: () => this.serverErrors.alias()
            }
          ]
        }),
      isCreateAutoMailings: ko.observable(
        this.mode === 'edit' ? this.data.isCreateAutoMailings : false
      ),
      isBaseTariff: this.isBaseTariff,
      isCreateMailings: ko.observable(
          this.mode === 'edit' ? this.data.isCreateMailings : false
      ),
      isContactsEnabled: ko.observable(
          this.mode === 'edit' ? this.data.isContactsEnabled : false
      ),
      login: ko.observable(this.mode === 'edit' ? this.data.login : '').extend({
        required: {
          message: 'Обязательное поле',
          onlyIf: () => {
            if (this.mode == 'create') {
              return true;
            }
            return (
              formModel() === null ||
              formModel().password() !== '' ||
              formModel().passwordCheck() !== '' ||
              formModel().email() !== ''
            );
          }
        },
        minLength: {
          params: 2,
          message: 'Должно быть введено хотя бы 2 символа'
        },
        pattern: {
          params: /^\S*$/,
          message: 'Неверный формат параметра «Логин»'
        },
        validation: {
          validator: () => !this.serverErrors.username(),
          message: () => this.serverErrors.username()
        }
      }),
      password: ko
        .observable(this.mode === 'edit' ? this.data.password : '')
        .extend({
          required: {
            message: 'Обязательное поле',
            // onlyIf: () => {
            //     return formModel() === null ||
            //         formModel().login() !== '' ||
            //         formModel().passwordCheck() !== '' ||
            //         formModel().email() !== '';
            // }
            onlyIf: () => {
              if (this.mode == 'create') {
                return true;
                // return (
                //   formModel() === null ||
                //   formModel().login() !== '' ||
                //   formModel().passwordCheck() !== '' ||
                //   formModel().email() !== ''
                // );
              }
              return !(
                this.data.user_id === 0 ||
                formModel() === null ||
                formModel().password() === '' ||
                this.data.user_id !== 0 ||
                formModel() === null ||
                formModel().password() !== ''
              );
            }
          },
          minLength: {
            params: 6,
            message: 'Длина параметра «Пароль» должна быть не менее 6 символов'
          }
        }),
      passwordCheck: ko
        .observable(this.mode === 'edit' ? this.data.passwordCheck : '')
        .extend({
          required: {
            message: 'Обязательное поле',
            // onlyIf: () => {
            //     return formModel() === null ||
            //         formModel().login() !== '' ||
            //         formModel().password() !== '' ||
            //         formModel().email() !== '';
            // }
            onlyIf: () => {
              console.log(
                'password check validator',
                this.mode,
                ko.toJS(this.formModel)
              );
              if (this.mode == 'create') {
                return true;
              }
              return !(
                this.data.user_id === 0 ||
                formModel() === null ||
                formModel().password() === '' ||
                this.data.user_id !== 0 ||
                formModel() === null ||
                formModel().password() !== ''
              );
            }
          },
          validation: {
            validator: (value) => {
              return formModel() === null || value === formModel().password();
            },
            message: 'Значение должно совпадать со значением параметра «Пароль»'
          }
        }),
      email: ko.observable(this.mode === 'edit' ? this.data.email : '').extend({
        email: {
          message: 'Неверный формат параметра «Email»'
        }
      }),
      serviceAccesses: ko.observableArray(
        (this.mode === 'edit' ? this.data.serviceAccesses : []).map(
          (data) => new ServiceAccessViewModel(data)
        )
      ),
      user_id: ko.observable(this.mode === 'edit' ? this.data.user_id : 0)
    });

    formModel().isBaseTariff.subscribe((isBaseTariff) => {
      formModel().isContactsEnabled(!isBaseTariff);
    });
    return formModel;
  })();

  this.formModel().login.subscribe((_) => {
    this.serverErrors.username('');
  });

  this.formModel().domain.subscribe((_) => {
    this.serverErrors.alias('');
  });

  this.connectedAtStringFilter = ko.observable('');
  this.serviceStringFilter = ko.observable('');
  this.nameStringFilter = ko.observable('');
  this.activeStringFilter = ko.observable('');

  this.formControlErrorStateMatcher = function (formControl) {
    return ko.computed(() => {
      return this.isSubmitted() && !formControl.isValid();
    });
  };

  this.formControlSuccessStateMatcher = function (formControl) {
    return ko.computed(() => {
      return this.isSubmitted() && formControl.isValid();
    });
  };

  this.backendValidationEmail = ko.observable(null);
  this.formModel().email.subscribe(v => {
    if (this.backendValidationEmail()) {
      this.backendValidationEmail(null);
    }
  });

  this.getServiceName = function (service) {
    var service_types = ENCODED_SERVICES_TYPES;
    var needed = service_types.find((st) => st.id == service);
    return needed.name;
  };

  this.openCreateServiceAccessModal = function () {
    const id = _.uniqueId();

    this.dialogs().add({
      name: 'service-old-sidesheet',
      params: {
        mode: 'create',
        services: ENCODED_SERVICES_TYPES
      },
      events: {
        submit: (result) => {
          if (result !== undefined) {
            this.formModel().serviceAccesses.splice(
              0,
              0,
              new ServiceAccessViewModel({
                id,
                connectedAt: moment().format('DD.MM.YYYY'),
                ...result
              })
            );
          }
        }
      }
    });
  };

  this.openEditServiceAccessModal = function (id) {
    const serviceAccess = this.formModel()
      .serviceAccesses()
      .find((r) => r.id === id);

    this.dialogs().add({
      name: 'service-old-sidesheet',
      params: {
        mode: 'edit',
        data: {
          ...serviceAccess,
          service: serviceAccess.service(),
          services: ENCODED_SERVICES_TYPES,
          name: serviceAccess.name(),
          active: serviceAccess.active()
        }
      },
      events: {
        submit: (result) => {
          if (result !== undefined) {
            serviceAccess.service(result.service);
            serviceAccess.name(result.name);
            serviceAccess.active(result.active);

            Object.getOwnPropertyNames(result)
              .filter(
                (property) => !['service', 'name', 'active'].includes(property)
              )
              .forEach((property) => {
                serviceAccess[property] = result[property];
              });
          }
        }
      }
    });
  };

  this.openDeleteServiceAccessModal = function (id) {
    window.idServiceRemove = id;
    this.modalOpens.push({
      dialogTemplateName:
        'company-delete-service-access-modal-content-template',
      close: (result) => {
        if (result !== undefined) {
          this.formModel().serviceAccesses.remove((m) => m.id === id);
        }
      }
    });
  };

  this.cancel = function () {
    if (this.mode === 'create') {
      this.isSubmitted(false);
      this.formModel().name('');
      this.formModel().domain('');
      this.formModel().isCreateAutoMailings('');
      this.formModel().isCreateMailings('');
      this.formModel().isContactsEnabled('');
      this.formModel().login('');
      this.formModel().password('');
      this.formModel().passwordCheck('');
      this.formModel().email('');
      this.formModel().serviceAccesses([]);
      this.formModel().user_id(0);
    } else {
      this.formModel().name(this.data.name);
      this.formModel().domain(this.data.domain);
      this.formModel().isCreateAutoMailings(
        this.data.auto_poll_enabled === 1 ? true : false
      );
      this.formModel().isCreateMailings(
          this.data.mailings_enabled === 1 ? true : false
      );
      this.formModel().isContactsEnabled(
          this.data.contacts_enabled === 1 ? true : false
      );
      this.formModel().login(this.data.login);
      this.formModel().password(this.data.password);
      this.formModel().passwordCheck(this.data.passwordCheck);
      this.formModel().email(this.data.email);
      this.formModel().serviceAccesses(
        this.data.serviceAccesses.map(
          (data) => new ServiceAccessViewModel(data)
        )
      );
      this.formModel().user_id(this.data.user_id);
    }
  };

  $.urlParam = function (name) {
    var results = new RegExp('[?&]' + name + '=([^&#]*)').exec(
      window.location.href
    );
    if (results == null) {
      return null;
    } else {
      return results[1] || 0;
    }
  };

  this.submit = function () {
    this.isSubmitted(true);

    if (this.formModel.isValid()) {
      this.pending(true);

      const data = {
        name: this.formModel().name(),
        domain: this.formModel().domain(),
        auto_poll_enabled: this.formModel().isCreateAutoMailings() ? 1 : 0,
        mailings_enabled: this.formModel().isCreateMailings() ? 1 : 0,
        contacts_enabled: this.formModel().isContactsEnabled() ? 1 : 0,
        login: this.formModel().login(),
        password: this.formModel().password(),
        passwordCheck: this.formModel().passwordCheck(),
        email: this.formModel().email(),
        serviceAccesses: this.formModel()
          .serviceAccesses()
          .map((serviceAccess) => ({
            ...serviceAccess,
            service: serviceAccess.service(),
            active: serviceAccess.active()
          })),
        user_id: this.formModel().user_id
      };

      let Company = {
        name: data.name,
        alias: data.domain,
        auto_poll_enabled: data.auto_poll_enabled,
        mailings_enabled: data.mailings_enabled,
        contacts_enabled: data.contacts_enabled
      };
      let User =
        this.mode == 'create'
          ? {
              username: data.login,
              password: data.password,
              repeat_password: data.passwordCheck,
              email: data.email,
              role: 'foquz_admin'
            }
          : {
              id: data.user_id,
              username: data.login,
              email: data.email,
              role: 'foquz_admin'
            };
      if (data.password !== '') {
        User.password = data.password;
        User.repeat_password = data.passwordCheck;
      }

      let url =
        this.mode == 'create'
          ? '/foquz/api/company/create?access-token=' + APIConfig.apiKey
          : '/foquz/api/company/update?access-token=' + APIConfig.apiKey + '&id=' + $.urlParam('id');

      $.ajax({
        type: 'POST',
        url: url,
        data: { Company: Company, User: User },
        success: (response) => {
          this.formModel().companyName(this.formModel().name());

          if (this.mode == 'create') {
            let id = response.id;
            if (id) {
              location = '/foquz/company/update?id=' + id;
              return;
            }
            let errors = response.errors;
            if (errors) {
              Object.keys(this.serverErrors).forEach((key) => {
                this.serverErrors[key](errors[key] || '');
              });
            }
            this.pending(false);
            return;

            //window.companyId = id;
            //t.cantSetupServices(false);
            //$('#nav-service-accesses-tab').click();
          } else {
            let id = response;
            window.companyId = id;
            this.pending(false);
            $('#nav-service-accesses-tab').click();
          }
        },
        error: (response) => {
          console.error(response.responseJSON);
          if (_get(response.responseJSON, 'errors.email')) {
            viewModel.formModel().email.error(response.responseJSON.errors.email);
          }
          viewModel.pending(false);
        }
      });
    }
  };
  this.afterServiceAdd = function (element) {
    $(element).hide().fadeIn(200);
  };

  this.beforeServiceRemove = function (element) {
    $(element).fadeOut(200, () => $(element).remove());
  };

  this.affiliateCompanies = this.data
    ? new AffiliateCompaniesTable({
        companyId: this.data.id
      })
    : null;

  this.promocodesTable = new PromocodesTable(this.data && this.data.id);
  this.promocodesTable.load();
};

// const viewModel = new ViewModel('create');
/*Режим редактирования:*/
let viewModel;

if (window.MODE == 'edit') {
  viewModel = new ViewModel('edit', {
    ...COMPANY_DATA,

    id: COMPANY_DATA.id,
    name: COMPANY_DATA.name,
    domain: COMPANY_DATA.alias,
    isCreateAutoMailings: COMPANY_DATA.auto_poll_enabled === 1 ? true : false,
    isCreateMailings: COMPANY_DATA.mailings_enabled === 1 ? true : false,
    isContactsEnabled: COMPANY_DATA.contacts_enabled === 1 ? true : false,
    user_id: USER_DATA.id,
    login: USER_DATA.username,
    password: '',
    passwordCheck: '',
    email: USER_DATA.email,
    serviceAccesses: COMPANY_DATA.serviceAccesses
  });
} else {
  viewModel = new ViewModel('create');
}

const $content = $('.company__content');

viewModel.initializing = ko.observable(true);

viewModel.onInit = function () {
  viewModel.initializing(false);
};

ko.applyBindings(viewModel, $content.get()[0]);
