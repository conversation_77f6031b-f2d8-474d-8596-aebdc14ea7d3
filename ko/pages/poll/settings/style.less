.poll-settings {
  &__title {
    padding-bottom: 0;
  }
  hr {
    margin: 25px 0;
  }
  .mt-40 {
    margin-top: 40px;
  }
  .pl-60 {
    padding-left: 60px;
  }
  &__period {
    .fc-period-picker {
      width: 100%;
    }
  }
  &__target-count {
    .fc-input {
      width: 100%;
    }
  }
  &__time-to-pass {
    .fc-input {
      width: 115px;
      text-align: center;

      &__valid {
        display: none;
      }
    }
  }
  &__restart {
    .fc-input {
      width: 115px;
      text-align: center;

      &__valid {
        display: none;
      }
    }
  }
  &__actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  &__reset {
    margin-right: 10px;
  }

  &-langs {
    &__content {
      padding-left: 64px;
    }
  }
  .stop-sending-block {
    display: flex;
    margin-top: 15px;
    &-period {
      margin-left: 30px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      &__input {
        width: 90px;
        text-align: center;
      }
      &__label {
        margin-left: 12px;
      }
    }
    &__select {
      flex-grow: 1;
    }
  }
  .poll-settings__option-disclaimer,
  .poll-settings__switch-disclaimer {
    margin-top: 10px;
    color: #73808d;
    font-size: 12px;
    line-height: 14px;
  }
  .poll-settings__option-disclaimer {
    margin-top: 10px;
  }
  .poll-settings__switch-disclaimer {
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .fc-switch label {
    align-items: center;
  }
}

.reset-mailing-frequency__form {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  padding-left: 60px;
}

.reset-mailing-frequency__form-control {
  display: flex;
  align-items: center;

  .fc-input {
    max-width: 91px;
    margin-right: 13px;
  }
}

.reset-mailing-frequency__form-control-label {
  font-size: 16px;
  font-weight: 400;
  line-height: 13px;
}

.poll-settings__reset-mailing-frequency {
  .fc-switch {
    margin-bottom: 15px;
  }
}

.poll-settings__textarea-css-block{
    position: relative;
    border: 1px solid #CFD8DC;
    border-radius: 4px;
    padding: 12px 6px 12px 15px;

    .copy-btn{
      z-index: 2;
      position: absolute;
      top: 14px;
      right: 13px;

      .fc-icon {
        color: #73808d;
        transition: all 250ms;
        cursor: pointer;
      }

      &.has-text:hover .fc-icon {
        color: #2e2f31 !important;
        transform: scale(1.2);
      }
    }
 }

.poll-settings__textarea-css-block:focus-within {
  border-color: #3a5cdc;
  box-shadow: 0 0 5px rgba(63, 101, 241, 0.5);
}

.poll-settings__textarea-css-item{
    width: 100%;

    &:before{
      content: '<style>';
      color: #73808D;
      font-size: 16px;
      font-weight: 400;
      display: block;
      height: 18px;
      margin-bottom: 5px;
    }
    &:after{
      content: '</style>';
      color: #73808D;
      font-size: 16px;
      font-weight: 400;
      display: block;
      height: 18px;
      margin-top: 5px;
    }

  textarea {
    border: 0;
    padding: 0;
    padding-right: 32px;
    width: 100%;
    resize: none !important;
    line-height: 110%;
    font-size: 16px;

    //scrollbar-width: thin;
    //scrollbar-color: #8E99A3 #E7EBED;

    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #E7EBED;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #8E99A3;
      border-radius: 4px;

      &:hover {
        background: #8E99A3;
        cursor: pointer;
      }
    }
  }
}

.poll-settings__textarea-css-container{
  position: relative;

  &::before, &::after {
    content: "";
    position: absolute;
    left: 0;
    width: 95%;
    height: 15px;
    pointer-events: none;
    transition: opacity 0.2s;
  }

  &::before {
    top: 0;
    background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    opacity: 0;
  }

  &::after {
    bottom: 0;
    background: linear-gradient(0deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    opacity: 0;
  }

  &.shadow-top::before {
    opacity: 1;
  }

  &.shadow-bottom::after {
    opacity: 1;
  }
}

.delete-btn{
  margin-top: 15px;
}

.poll-settings__other-input {
  .fc-input__valid {
    display: none !important;
  }
}