export default function (data) {
  if (data.ratingType !== "variants") {
    data.variants = null
  }

  const clarifyingQuestion = data.clarifyingQuestion

  const question = {
    comment_enabled: data.npsCommentEnabled ? 1 : 0,
    comment_required: data.galleryCommentRequaired ? 1 : 0,
    placeholder_text: data.npsCommentPlaceholder,
    comment_minlength: data.npsCommentLengthRange[0],
    comment_maxlength: data.npsCommentLengthRange[1],
    comment_label: data.npsCommentLabel,
    enableGallery: data.npsGalleryEnabled,
    gallery: data.npsGallery
      .filter((v) => v.mediaId)
      .map((v) => {
        return {
          id: v.mediaId,
          description: v.description,
        };
      }),
    files: data.npsGallery.filter((v) => v.mediaId).map((v) => v.mediaId),

    design: data.npsType,
    start_point_color: data.npsStartColor,
    end_point_color: data.npsEndColor,
    start_label: data.npsStartLabel,
    end_label: data.npsEndLabel,
    from_one: data.fromOne,
    extra_question_type: data.extraQuestionType,

    skip: data.skip ? 1 : 0,
    skip_text: data.skipText || "",

    set_variants: data.ratingType === "variants" ? 1 : 0,
  };

  if (clarifyingQuestion.enabled) {
    question.extra_required = clarifyingQuestion.clarifyingQuestionIsRequired ? 1 : 0;
    question.detail_question = clarifyingQuestion.text;
    question.for_all_rates = clarifyingQuestion.forAllRates ? 1 : 0;
    question.variants_with_files = clarifyingQuestion.enableFile ? 1 : 0;
    question.self_variant_file_id = clarifyingQuestion.customAnswerFile?.id || null
    question.min_choose_extra_variants = clarifyingQuestion.minСhooseVariants || ''
    question.max_choose_extra_variants = clarifyingQuestion.maxСhooseVariants || ''

    if (!question.for_all_rates) {
      const [from, to] = clarifyingQuestion.forRates
      question.extra_question_rate_from = from || 0
      question.extra_question_rate_to = to || 10
    }

    question.variants_element_type = clarifyingQuestion.variantsType;

    question.is_self_answer = clarifyingQuestion.customAnswerEnabled ? 1 : 0;
    let customAnswerRange = clarifyingQuestion.customAnswerRange;
    question.comment_minlength = customAnswerRange[0];
    question.comment_maxlength = customAnswerRange[1];
    question.placeholder_text = clarifyingQuestion.customAnswerPlaceholder;
    question.self_variant_text = clarifyingQuestion.customAnswerLabel;
    question.extra_required = clarifyingQuestion.required ? 1 : 0;


  } else {
    question.detail_question = '';
  }


  question.random_variants_order = data.randomOrder ? 1 : 0;

  if (data.useDonor) {
    question.donor = data.donorId;
    question.donor_chosen = data.donorVariantsType == 1 ? 1 : 0;
    data.variants.map(v => {
      v.need_extra = ko.unwrap(v.needExtra) ? 1 : 0
    })
  } else {
    question.donor = null;
  }
  
  return question;
}
