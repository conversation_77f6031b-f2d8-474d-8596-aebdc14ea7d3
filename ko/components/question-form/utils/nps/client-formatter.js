import { get as _get } from 'lodash';

export default function (data, mode) {
  let enableComment =
    mode === "cpoint"
      ? data.commentEnabled
      : data.comment_enabled || false;
  let commentLengthRange =
    mode === "cpoint"
      ? data.commentLengthRange
      : [data.comment_minlength || 0, data.comment_maxlength || 250];
  let placeholder = data.placeholderText || '';

  let gallery = (data.gallery || []).map((v, i) => {
    return {
      id: v.id,
      mediaId: v.id,
      description: v.description || '',
      preview: v.poster || '',
      url: v.url || '',
      position: "position" in v ? v.position : i,
    };
  });

  gallery.sort((a, b) => a.position - b.position);

  let npsSettings = data.npsRatingSetting || {};

  const clarifyingToEach = data.extra_question_type === 3;
  const isDonor = data.donor !== null && data.donor !== undefined;

  // Deduplicate variants by id, keeping the latest occurrence
  const mergedVariants = {};
  (data.detail_answers || []).forEach(v => {
    mergedVariants[v.id] = { ...mergedVariants[v.id], ...v };
  });
  let variants = Object.values(mergedVariants).filter(v => !v.is_deleted);


  variants = variants.map((v) => {
    // Filter extra question variants
    let extraVariants = (data.detail_answers || []).filter(extra => {
      try {
        if (isDonor) {
          // Если question_detail_id отсутствует (null), используем v.id, иначе v.question_detail_id
          const compareId = v.question_detail_id != null ? v.question_detail_id : v.id;
          return String(extra.question_detail_id) === String(compareId) && extra.extra_question;
        }
        return String(extra.question_detail_id) === String(v.id) && extra.extra_question;
      } catch (error) {
        console.error('Error filtering extra question variants:', error);
        return false;
      }
    });

    // Deduplicate extra question variants by id
    const uniqueExtraVariants = new Map();
    extraVariants.forEach(extra => {
      uniqueExtraVariants.set(extra.id, extra);
    });
    extraVariants = Array.from(uniqueExtraVariants.values());

    const selfVariantFile = v.selfVariantFile || (v.is_self_answer && v.file_id ? {
      id: v.file_id,
      fileUrl: v.file_url,
      previewUrl: v.preview_url
    } : null);

    const variant = {
      id: v.id,
      donorId: v.question_detail_id,
      value: v.variant || '',
      points: v.points || null,
      needExtra: v.need_extra || false,
      dictionary_element_id: v.dictionary_element_id || null,
      extra_question: v.extra_question || false,
      detail_question: v.detail_question || '',
      selfVariantFile: selfVariantFile,
      ...v,
      detail_question_options: clarifyingToEach
        ? extraVariants.map(extra => ({
          id: extra.id,
          value: extra.variant || '',
          points: extra.points || null,
          extra_question: extra.extra_question || false,
          position: extra.position || 0,
          file_id: extra.file_id || null,
          file_url: extra.file_url,
          preview_url: extra.preview_url
        }))
        : []
    };
    return variant;
  });

  let detailQuestion = data.detail_question || '';
  let variantsType = data.variants_element_type || 0;
  let enableSelfAnswer = parseInt(data.is_self_answer) === 1;
  let clarifyingQuestion = {
    enabled: mode === 'cpoint'
      ? data.clarifyingQuestionEnabled
      : _get(data, 'detail_question', '') !== '',
    forAllRates: mode === 'cpoint' ? data.forAllRates : data.for_all_rates || false,
    forRates: data.forAllRates || data.for_all_rates ? [] : [data.extra_question_rate_from || 0, data.extra_question_rate_to || 10],
    text: (mode === 'cpoint' ? data.clarifyingQuestion : detailQuestion) || '',
    variantsType: mode === 'cpoint'
      ? parseInt(data.clarifyingQuestionVariantsType) || 0
      : variantsType,
    variants: clarifyingToEach
      ? []
      : variants.filter(el => el.extra_question) || [],
    customAnswerEnabled: mode === 'cpoint'
      ? data.customClarifyingQuestionAnswerAvailable || false
      : enableSelfAnswer,
    customAnswerLengthRange: commentLengthRange,
    customAnswerPlaceholder: placeholder,
    customAnswerLabel: data.self_variant_text || '',
    required: data.extra_required || false,
    enableFile: data.variants_with_files || false,
    minСhooseVariants: data.min_choose_extra_variants || null,
    maxСhooseVariants: data.max_choose_extra_variants || null,
    clarifyingQuestionIsRequired: data.extra_required || false,
    customAnswerFile: data.selfVariantFile || null
  };

  return {
    npsGallery: gallery,
    npsGalleryEnabled: gallery.length > 0,
    extraQuestionType: data.extra_question_type || 0,
    npsCommentEnabled: enableComment,
    npsCommentLengthRange: commentLengthRange,
    npsCommentPlaceholder: placeholder,
    npsCommentLabel: data.comment_label || '',
    npsCommentRequired: data.comment_required || false,
    npsType: npsSettings.design || 1,
    npsStartColor: npsSettings.start_point_color || '#F96261',
    npsEndColor: npsSettings.end_point_color || '#00C968',
    npsStartLabel: npsSettings.start_label || '',
    npsEndLabel: npsSettings.end_label || '',
    skip: data.skip ? 1 : 0,
    skipText: data.skip_text || '',
    ratingType: data.set_variants ? 'variants' : 'standart',
    fromOne: data.from_one || false,
    variants: variants,
    randomOrder: data.random_variants_order || false,
    donorId: data.donor || null,
    donorVariantsType: data.donor_chosen === 1 ? 1 : 0,
    clarifyingQuestion: clarifyingQuestion,
    selfVariantFile: data.selfVariantFile || null
  };
}