import { NPSGradient, NPSLabel } from 'Legacy/utils/nps';
import { gradientCss } from 'Utils/color/gradient';

export class QuestionViewModel {
  constructor(params) {
    this.review = params.review;
    this.question = params.question;
    const hiddenQuestions = window.HIDDEN_QUESTIONS || params.review.hiddenQuestions
    this.isHidden = !!(hiddenQuestions && hiddenQuestions.length && hiddenQuestions.find(el => el == params.question.id));


    this.getGradient = () =>
      gradientCss({
        start: [236, 239, 241],
        end: [249, 250, 250],
        count: this.question.variants.length
      });

    this.getGradientPoint = function (point, count) {
      const start = [236, 239, 241];
      const finish = [249, 250, 250];

      let x = start[0],
        y = start[1],
        z = start[2];

      count = count - 1;
      if (point === count) {
        x = finish[0];
        y = finish[1];
        z = finish[2];
      } else if (point > 0) {
        const k = point / count / ((count - point) / point);
        x = (start[0] + k * finish[0]) / (1 + k);
        y = (start[1] + k * finish[1]) / (1 + k);
        z = (start[2] + k * finish[2]) / (1 + k);
      }

      return `rgb(${x}, ${y}, ${z})`;
    };

    this.getFilesFancyboxCaption = function (answer) {
      const passedAt = answer.passedAt;
      const text = answer.comment;
      return (
        `<div class="review-complaint__fancybox-description">
                <div class="review-complaint__fancybox-description-passed-at">${passedAt}</div>` +
        (text !== null
          ? `<div class="review-complaint__fancybox-description-text">${text}</div>`
          : '') +
        `</div>`
      );
    };

    this.getRatingLabel = function (value) {
      switch (value) {
        case 1:
          return _t('Ужасно');
        case 2:
          return _t('Плохо');
        case 3:
          return _t('Нормально');
        case 4:
          return _t('Хорошо');
        case 5:
          return _t('Отлично');
      }
    };

    this.getRatingStars = (config) => {
      let stars = Array(config.count)
        .fill()
        .map((_, i) => i);
      return stars;
    };

    this.getNPSColor = (config, value) => {
      if (config.design == 2) return 'none';
      let scale = NPSGradient(config.start_point_color, config.end_point_color);
      return scale[value];
    };

    this.getNPSLabel = (value) => {
      if(!value) {
        return '–'
      }
      return NPSLabel(value);
    };

    this.sortGallery = (gallery) => {
      let sorted = [...gallery];
      sorted.sort((a, b) => {
        return a.position - b.position;
      });
      return sorted;
    };
  }
}
