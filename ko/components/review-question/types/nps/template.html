<!-- ko using: question -->
  <div class="review-question-view review-question-view--type_nps">
    <div class="review-question-view__name">
      <!-- ko if: typeof position !== "undefined" -->
      <span class="position" data-bind="text: position + '.'"></span>
      <!-- /ko -->
      <span>
        <!-- ko text: displayName -->
        <!-- /ko -->
        <!-- ko if: isRequired -->
        <span class="question-name__required-mark">*</span>
        <!-- /ko -->
      </span>
      <!-- ko if: $data.linkWithClientField -->
        <small
          class="service-text"
          data-bind="
            text: _t(
              'main',
              'связан с параметром {param} контакта',
              {
                param: linkedClientField
              },
            ),
          "
        ></small>
      <!-- /ko -->
    </div>
    <!-- ko if: subDescription -->
      <div
        class="review-question-view__subdescription service-text"
        data-bind="text: subDescription"
      ></div>
    <!-- /ko -->
    <!-- ko if: text !== null -->
      <div class="review-question-view__text" data-bind="text: text">
      </div>
    <!-- /ko -->
    <div class="review-question-view__body">
      <!-- ko ifnot: hasAnswer -->
        <div class="review-question-view-block skip-block">
          <div
            class="review-question-view-block__text review-question-view-block__text--skipped"
            data-bind="
              text: $component.isHidden ?
                'Вопрос для респондента не отображался' :
                'Вопрос пропущен',
            "
          ></div>
        </div>
      <!-- /ko -->
      <!-- ko if: hasAnswer -->
        <div
          class="review-question-view__answer-section"
          data-bind="
            css: {
              'answer-section--variants': isVariants,
            },
          "
        >
          <!-- ko if: gallery.length -->
            <div
              class="review-question-view__gallery f-color-text ml-n5p mt-n1 mb-4 d-flex align-items-center cursor-pointer"
              data-bind="fancyboxGalleryItem: {
              gallery: $component.sortGallery(gallery).map(function(i) {
                return {
                  src: i.url,
                  opts: {
                    caption: i.description
                  }
                }
              }),
              index: 0,
              noCursor: true,
              }"
            >
              <span class="f-icon f-icon--picture mr-2">
                <svg>
                  <use href="#picture-icon"></use>
                </svg>
              </span>
              <span
                class="f-color-primary f-fs-3 bold"
                data-bind="text: _t('answers', 'Галерея')"
              ></span>
            </div>
          <!-- /ko -->
          <!-- ko if: skipped -->
           <div class="review-question-stars__skipped" data-bind="text: _t('answers', 'Респондент отказался от оценки')"></div>
          <!-- /ko -->
          <!-- ko if: !skipped && (typeof answerData.rating === "undefined" || answerData.rating > -1) -->
            <!-- ko if: hasAnswer -->
              <!-- ko ifnot: isVariants -->
                <div class="d-flex align-items-center nps-result">
                  <div
                    class="nps-scale nps-scale--review"
                    data-bind="
                      css: {
                        'nps-scale--colored': npsRating.design !== 2,
                      },
                    "
                  >
                    <div class="nps-scale__list">
                      <div
                        class="nps-scale__item"
                        data-bind="
                          style: {
                            'background-color': $component.getNPSColor(npsRating, answer.answer.rating),
                          },
                          css: {
                            'mr-3': !window.IS_EXTERNAL,
                          },
                        "
                      >
                        <span data-bind="text: answer.answer.rating"></span>
                      </div>
                    </div>
                  </div>
                  <div class="f-fs-3 f-color-text bold nps-result__label" data-bind="text: $component.getNPSLabel(answer.answer.rating)"></div>
                </div>
              <!-- /ko -->
              <!-- ko if: isVariants -->
                <!-- ko if: clarifyingQuestion && extra_question_type == 2 && !window.IS_EXTERNAL -->
                <div class="review-question-view-clarifying-question__text" data-bind="click: function () {console.log(extra_question_type)}">
                  <!-- ko text: clarifyingQuestion -->
                  <!-- /ko -->
                  <!-- ko if: $data.forAllRates || (!extra_question_rate_from && !extra_question_rate_to) -->
                  <small class="service-text" data-bind="text: '(' + _t('для всех оценок') +')'"></small>
                  <!-- /ko -->
                  <!-- ko ifnot: $data.forAllRates || (!extra_question_rate_from && !extra_question_rate_to) -->
                  <small class="service-text" data-bind="text: '(' + _t(`для оценок ${extra_question_rate_from}-${extra_question_rate_to}`) +')'"></small>
                  <!-- /ko -->
                </div>
                <!-- /ko -->
                <!-- ko if: !window.IS_EXTERNAL -->
                  <div class="star-variants" data-bind="log">
                    <!-- ko foreach: { data: variants, as: 'variant' } -->
                      <div
                        class="star-variant"
                        data-bind="
                          css: {
                            'star-variant--deleted': variant.deleted,
                            'star-variant--selected': $parent.answerData[variant.id] > 0,
                          },
                        "
                      >
                        <div class="star-variant__text flex-grow-1">
                          <span data-bind="text: variant.text"></span>
                          <!-- ko if: variant.deleted -->
                          <span class="removed">(Удален)</span>
                          <!-- /ko -->
                        </div>
                        <div class="star-variant__rating">
                          <!-- ko if: $parent.answerData[variant.id] > -1 -->
                          <div class="d-flex align-items-center justify-content-end nps-result">
                            <div
                              class="f-fs-1 f-color-text bold nps-result__label" 
                              data-bind="text: $component.getNPSLabel($parent.answerData[variant.id]),
                               css: { 'mr-3': $parent.answerData[variant.id] }"
                            ></div>
                            <!-- ko if: $parent.answerData[variant.id] -->
                            <div
                              class="nps-scale nps-scale--review"
                              data-bind="
                                css: {
                                  'nps-scale--colored': $parent.npsRating.design !== 2,
                                },
                              "
                            >
                              <div class="nps-scale__list">
                                <div
                                  class="nps-scale__item"
                                  data-bind="
                                    style: {
                                      'background-color': $component.getNPSColor($parent.npsRating, $parent.answerData[variant.id]),
                                    },
                                  "
                                >
                                  <span data-bind="text: $parent.answerData[variant.id], click: function () {console.log($parent)}"></span>
                                </div>
                              </div>
                            </div>
                            <!-- /ko -->
                          </div>
                          <!-- /ko -->
                          <!-- ko ifnot: $parent.answerData[variant.id] > -1 -->
                          <div class="d-flex justify-content-end bold">–</div>
                          <!-- /ko -->
                        </div>
                      </div>
                      <!-- ko if: $parent.clarifyingQuestion && $parent.extra_question_type == 2 && window.IS_EXTERNAL  -->
                      <div class="review-star-variant__clarifying-row-text" data-bind="text: $parent.clarifyingQuestion">
                      </div>
                      <!-- /ko -->
                      <!-- ko if: variant.detail_question  -->
                      <div class="review-question-view-clarifying-question__text">
                        <!-- ko text: variant.detail_question -->
                        <!-- /ko -->
                        <!-- ko if: variant.for_all_rates || (!variant.extra_question_rate_from && !variant.extra_question_rate_to) -->
                        <small class="service-text" data-bind="text: '(' + _t('для всех оценок') +')'"></small>
                        <!-- /ko -->
                        <!-- ko ifnot: variant.for_all_rates || (!variant.extra_question_rate_from && !variant.extra_question_rate_to) -->
                        <small class="service-text" data-bind="text: '(' + _t(`для оценок ${variant.extra_question_rate_from}-${variant.extra_question_rate_to}`) +')'"></small>
                        <!-- /ko -->
                      </div>
                      <!-- /ko -->
                      <!-- ko if: ($parent.clarifyingQuestion && $parent.extra_question_type == 2) || $parent.extra_question_type == 3 -->
                        <!-- Уточняющий вопрос -->
                        <!-- ko template: { name: 'review-question-variant-view-clarifying-question-template-multiple', data: $parent } -->
                        <!-- /ko -->
                        <!-- /Уточняющий вопрос -->
                      <!-- /ko -->
                    <!-- /ko -->
                  </div>
                <!-- /ko -->
                <!-- ko if: window.IS_EXTERNAL -->
                  <!-- ko foreach: { data: variants, as: 'variant' } -->
                    <div class="variant">
                      <div class="variant-header">
                        <span data-bind="text: variant.text"></span>
                        <!-- ko if: variant.deleted -->
                        <span class="removed">(Удален)</span>
                        <!-- /ko -->
                      </div>
                      <div class="d-flex align-items-center nps-result">
                        <div
                          class="nps-scale nps-scale--review"
                          data-bind="
                            css: {
                              'nps-scale--colored': $parent.npsRating.design !== 2,
                            },
                          "
                        >
                          <div class="nps-scale__list">
                            <div
                              class="nps-scale__item"
                              data-bind="
                                style: {
                                  'background-color': $component.getNPSColor($parent.npsRating, $parent.answerData[variant.id]),
                                },
                              "
                            >
                              <span data-bind="text: $parent.answerData[variant.id], click: function () {console.log($parent)}"></span>
                            </div>
                          </div>
                        </div>
                        <div class="f-fs-3 f-color-text bold nps-result__label" data-bind="text: $component.getNPSLabel($parent.answerData[variant.id])"></div>
                      </div>
                    </div>
                  <!-- /ko -->
                <!-- /ko -->
              <!-- /ko -->
            <!-- /ko -->      
          <!-- /ko -->
        </div>
      <!-- /ko -->
      <!-- ko if: !skipped && (answerData.rating == -1) -->
      <div class="review-question-stars__skipped-variant">–</div>
      <!-- /ko -->
    </div>
    <!-- ko if: !skipped && hasAnswer && clarifyingQuestion && extra_question_type == 1 -->
      <!-- ko template: { name: 'review-question-view-clarifying-question-template-new', data: $data } -->
      <!-- /ko -->
    <!-- /ko -->
    <!-- ko if: answer -->
      <!-- ko template: {
        name: 'review-question-view-comment-template',
        data: { text: answer && answer.answer && answer.answer.comment || answer.comment }
      } -->
      <!-- /ko -->
    <!-- /ko -->
  </div>
<!-- /ko -->
