import {
  EVENTS,
  CUSTOM_EVENTS,
  CLASSES,
  CSS_CLASSES,
  TIMEOUTS,
  URL_PARAMS,
  URL_PATHS,
  WIDGET_SETTINGS,
  WINDOW_SETTINGS,
} from "./constants";

export function createElement(tagName, attrs, children) {
  const el = document.createElement(tagName);
  Object.entries(attrs || {}).forEach(([key, value]) => {
    el.setAttribute(key, value);
  });

  (children || []).forEach((child) => el.appendChild(child));
  return el;
}

export function createText(text) {
  const el = document.createTextNode(text);
  return el;
}

export function createStyleLink(href) {
  return new Promise((resolve) => {
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    document.head.appendChild(link);

    link.onload = () => {
      resolve();
    };
  });
}

function buildModalElements(isVue, settings = {}, windowSettings = {}) {
  const mask = createElement("div", { class: CSS_CLASSES.MODAL_MASK });

  const shouldSetAutoHeight =
    !windowSettings.height || windowSettings.height === "null";

  const frame = createElement("iframe", {
    class: `${CSS_CLASSES.POLL_FRAME} ${
      shouldSetAutoHeight ? CSS_CLASSES.POLL_FRAME_AUTO_HEIGHT : ""
    }`,
    allow: "clipboard-write",
  });
  let close = null;
  let loader = null;

  const closeElement = createElement("div", { class: CSS_CLASSES.MODAL_CLOSE });
  const loaderElement = createElement(
    "div",
    { class: CSS_CLASSES.MODAL_LOADER },
    [createElement("span")]
  );

  if (!settings.isSimple) {
    close = closeElement;
  }

  if (!isVue) {
    close = closeElement;
    loader = loaderElement;
  }

  return { mask, frame, close, loader };
}

function attachModalEventListeners(
  modal,
  frame,
  hideModal,
  { isSimple, isVue, windowSettings, viewType }
) {
  // Resize listener with debouncing
  let resizeTimeout;
  window.addEventListener("resize", () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      if (modal.classList.contains(CLASSES.SHOWN)) {
        const maxHeight = getAllowedModalMaxHeight(viewType, modal);
        sendHeightToIframe(frame, maxHeight, windowSettings, viewType);
      }
    }, TIMEOUTS.RESIZE_DEBOUNCE);
  });

  // Message listener
  window.addEventListener("message", (event) => {
    const { data } = event;
    try {
      const eventData = JSON.parse(data);
      switch (eventData.type) {
        case EVENTS.FZ_RESIZE:
          modal.style.setProperty(
            "--fqz-modal-iframe-content-height",
            `${eventData.height}px`
          );
          break;
        case EVENTS.APP_READY:
          sendHeightToIframe(
            frame,
            getAllowedModalMaxHeight(viewType, modal),
            windowSettings,
            viewType
          );
          break;
        case EVENTS.CLOSE_WIDGET:
          hideModal();
          break;
      }
    } catch (e) {
      // Silently handle parse errors
    }
  });
}

export function getWindowDimension(dimension, defaultDimension) {
  const hasDimensionValue =
    dimension?.value !== undefined &&
    dimension?.value !== null &&
    dimension?.value !== "null" &&
    dimension?.value !== "";

  const value = hasDimensionValue ? dimension?.value : defaultDimension?.value;
  const type = hasDimensionValue
    ? String(dimension?.type)
    : String(defaultDimension?.type);

  return type === "1" ? `${value}px` : `${value}%`;
}

export function getWindowSettings(settings = {}, viewType = "STANDARD") {

  if (!settings) {
    settings = {};
  }

  const defaultSettings = WINDOW_SETTINGS.DEFAULTS[viewType] || WINDOW_SETTINGS.DEFAULTS.STANDARD;
  const mergedSettings = {
    width: settings.width || defaultSettings.width,
    height: {
      value: settings.height?.value || settings.height,
      type: settings.height?.type || defaultSettings.height?.type,
    },
    round: settings.round || defaultSettings.round,
    position: settings.position || defaultSettings.position,
    minHeight: defaultSettings.minHeight,
  };

  const normalizedHeight =
    mergedSettings.height.value && mergedSettings.height.value !== "null"
      ? mergedSettings.height.value + "%"
      : null;

  const result = {
    width: getWindowDimension(mergedSettings.width, defaultSettings.width),
    height: normalizedHeight,
    borderRadius: getWindowDimension(
      mergedSettings.round,
      defaultSettings.round
    ),

    position: mergedSettings.position,
    minHeight: mergedSettings.minHeight,
  };

  return result;
}

export function createFrameModal(showLoadingState, heloMode, settings = {}) {
  const isSimpleView =
    settings.widget?.form === WIDGET_SETTINGS.FORM_TYPES.SIMPLE ||
    settings.widget?.form === WIDGET_SETTINGS.FORM_TYPES.SIMPLE_PAGE_STOP;

  let viewType = "STANDARD";
  if (heloMode) {
    viewType = "HELLO_BOARD";
  } else if (isSimpleView) {
    viewType = "SIMPLE";
  }

  const windowSettings = getWindowSettings(
    settings.widget?.window_settings,
    viewType
  );

  const isVue = window.FOQUZ_SDK?._test?.legacy !== true;
  const { mask, frame, close, loader } = buildModalElements(
    isVue,
    settings,
    windowSettings
  );

  let modalClass = isVue
    ? `${CLASSES.MODAL} ${CLASSES.MODAL_VUE}`
    : CLASSES.MODAL;

  if (windowSettings.position) {
    modalClass = `${modalClass} ${CLASSES.MODAL}--position-${windowSettings.position}`;
  }

  const isSimple =
    settings.isSimple ||
    settings?.widget?.form === WIDGET_SETTINGS.FORM_TYPES.SIMPLE ||
    settings?.widget?.form === WIDGET_SETTINGS.FORM_TYPES.SIMPLE_PAGE_STOP;

  if (isSimple) {
    modalClass = `${modalClass} ${CLASSES.MODAL_VUE_SIMPLE}`;
  }

  const contentChildren = [
    ...(close ? [close] : []),
    frame,
    ...(loader ? [loader] : []),
  ];

  const normalizedHeight =
    windowSettings.height && windowSettings.height !== "null"
      ? windowSettings.height
      : null;

  const modal = createElement(
    "div",
    {
      id: "foquz-poll-modal",
      class: modalClass,
      style: `
      --fqz-modal-width: ${windowSettings.width};
      ${normalizedHeight ? `--fqz-modal-height: ${normalizedHeight};` : ""}
      --fqz-modal-border-radius: ${windowSettings.borderRadius};
      --fqz-modal-position: ${windowSettings.position};
    `,
    },
    [
      createElement("div", { class: CSS_CLASSES.MODAL_SCROLL }, [
        mask,
        createElement(
          "div",
          { class: CSS_CLASSES.MODAL_CONTENT },
          contentChildren
        ),
      ]),
    ]
  );

  if (close) {
    close.innerHTML = `<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11 1L1 11M11 11L1 1" stroke="#73808D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`;
    close.onclick = (e) => {
      e.preventDefault();
      hideModal();
    };
  }

  const hideModal = () => {
    if (heloMode) {
      modal.classList.add(CLASSES.LOADING);
      setTimeout(() => {
        modal.classList.remove(CLASSES.SHOWN);
        dispatchCustomEvent(CUSTOM_EVENTS.HIDDEN);
      }, TIMEOUTS.MODAL_ANIMATION);
    } else {
      modal.classList.remove(CLASSES.SHOWN);
      dispatchCustomEvent(CUSTOM_EVENTS.HIDDEN);
    }
  };

  const showModal = () => {
    modal.classList.add(CLASSES.SHOWN);
    dispatchCustomEvent(CUSTOM_EVENTS.SHOWN);
    sendHeightToIframe(
      frame,
      getAllowedModalMaxHeight(viewType, modal),
      windowSettings,
      viewType
    );
  };

  attachModalEventListeners(modal, frame, hideModal, {
    isSimple,
    isVue,
    windowSettings,
    viewType,
  });

  mask.onclick = (e) => {
    e.preventDefault();
    hideModal();
  };

  let loaded = false;
  let onLoad = null;

  const loading = (inProcess) => {
    modal.classList.toggle(CLASSES.LOADING, inProcess);
    loaded = !inProcess;

    if (!inProcess && typeof onLoad === "function") {
      onLoad();
      onLoad = null;
    }
  };

  if (!isVue) {
    hideModal();
    loading(true);
  }

  /**
   * Событие ресайза внутрий iframe. Для легаси виджета на нокауте.
   * @param {MessageEvent} event
   */
  const messageCb = (event) => {
    if (event.source !== frame.contentWindow) return;
    const { data } = event;
    try {
      const eventData = JSON.parse(data);
      if (eventData.type === EVENTS.FZ_RESIZE) {
        const height = eventData.height;
        if (height > WIDGET_SETTINGS.MIN_IFRAME_HEIGHT)
          frame.height = eventData.height;
      }
    } catch (e) {}
  };

  if (!isVue) {
    window.addEventListener("message", messageCb, false);
  }

  return {
    element: modal,
    frame,
    hide: hideModal,
    show() {
      if (isVue) {
        showModal();
        return;
      }
      if (loaded || showLoadingState) showModal();
      else onLoad = showModal;
    },
    setPollUrl(url) {
      loading(true);
      frame.onload = () => {
        frame.onload = null;
        loading(false);
      };
      frame.src = url;
    },
    destroy() {
      modal.remove();
      dispatchCustomEvent(CUSTOM_EVENTS.DESTROYED);
      window.removeEventListener("message", messageCb, false);
    },
  };
}

export function handlePollLink(link, widget) {
  if (!link) return "";

  let url = link;
  const useVueApp = window.FOQUZ_SDK?._test?.legacy !== true;
  let vueAppBaseUrl = window.FOQUZ_SDK?._test?.vueAppBaseUrl;

  if (useVueApp) {
    const urlParts = url.split("/");
    const pollKeyWithParams = urlParts.pop();

    if (vueAppBaseUrl) {
      const normalizedUrl = vueAppBaseUrl.endsWith("/")
        ? vueAppBaseUrl.slice(0, -1)
        : vueAppBaseUrl;
      url = `${normalizedUrl}/poll-vueAAA/${pollKeyWithParams}`;
    }
  }

  const urlObj = new URL(url);
  urlObj.searchParams.set(URL_PARAMS.NO_PREVIEW, 1);
  urlObj.searchParams.set(URL_PARAMS.IN_FRAME, 1);

  if (widget?.["close_by_finish_button"] === "1") {
    urlObj.searchParams.set(URL_PARAMS.CLOSE_BY_FINISH, 1);
  }

  if (widget?.autoclose_delay) {
    urlObj.searchParams.set("autoCloseDelay", widget.autoclose_delay);
  }

  return urlObj.toString();
}

export function getAllowedModalMaxHeight(viewType, modalElement) {
  const paddingYFromCss = getComputedStyle(modalElement).getPropertyValue(
    "--fqz-modal-overall-padding-y"
  );

  const numberPaddingYFromCss = Number.parseInt(paddingYFromCss);

  if (paddingYFromCss && numberPaddingYFromCss) {
    return window.innerHeight - numberPaddingYFromCss;
  }

  return window.innerHeight;
}

// Helper function to send height to iframe
export function sendHeightToIframe(frame, height, windowSettings, viewType) {
  if (!frame?.contentWindow) return;

  const isDefaultHeight = () => {
    const heightValue = windowSettings?.height;
    if (!heightValue || heightValue === "null") return true;
    return false;
  };

  let heightToSend = Number(height);

  const windowWidth = window.innerWidth;

  frame.contentWindow.postMessage(
    JSON.stringify({
      type: "fz:set_max_height",
      height: heightToSend,
      minHeight: windowSettings.minHeight,
      setAutoHeight: isDefaultHeight(),
      windowWidth,
    }),
    "*"
  );
}

export function toFormData(params) {
  const formData = new FormData();

  function addParam(key, value, parents = []) {
    const fullSet = [...parents, key];

    const fullKey = fullSet
      .map((p, i) => {
        if (i === 0) return p;
        return `[${p}]`;
      })
      .join("");
    formData.append(fullKey, value);
  }

  function formatParam(key, value, parents = []) {
    if (value === undefined) return;

    const type = typeof value;

    if (type === "number" || type === "string") {
      addParam(key, value, parents);
      return;
    }

    if (type === "boolean") {
      addParam(key, !!value ? 1 : 0, parents);
      return;
    }

    if (Array.isArray(value)) {
      value.forEach((item, i) => {
        formatParam(i, item, [...parents, key]);
      });
      return;
    }

    if (type === "object") {
      Object.entries(value).forEach(([childKey, childValue]) => {
        formatParam(childKey, childValue, [...parents, key]);
      });
      return;
    }

    addParam(key, value, parents);
  }

  Object.entries(params).forEach(([key, value]) => {
    formatParam(key, value, []);
  });

  return formData;
}

export function dispatchCustomEvent(type, detail) {
  const event = new Event(type);
  event.detail = detail;
  document.dispatchEvent(event);
}

export function getScrollDepthTrigger(widget) {
  const hasScrollDepthTrigger = widget?.triggers?.scroll_depth;
  const hasEnabledTriggerStatus = widget?.triggers_status?.scroll_depth === "1";
  if (!hasScrollDepthTrigger || !hasEnabledTriggerStatus) {
    return null;
  }

  const result = {};
  
  if (widget.triggers.scroll_depth.vertical) {
    const { type, value } = widget.triggers.scroll_depth.vertical;
    result.vertical = {
      type: type === "1" ? "pixels" : "percent",
      value: parseInt(value, 10)
    };
  }
  
  if (widget.triggers.scroll_depth.horizontal) {
    const { type, value } = widget.triggers.scroll_depth.horizontal;
    result.horizontal = {
      type: type === "1" ? "pixels" : "percent",
      value: parseInt(value, 10)
    };
  }
  
  return Object.keys(result).length ? result : null;
}

export function throttle(func, delay) {
  let lastCall = 0;
  return function(...args) {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return func.apply(this, args);
    }
  };
}

export function setupScrollDepthTrigger(trigger, onTrigger) {
  if (!trigger) return () => {};

  let triggered = false;
  let lastDocHeight = Math.max(
    document.body.scrollHeight,
    document.documentElement.scrollHeight
  );
  let lastDocWidth = Math.max(
    document.body.scrollWidth,
    document.documentElement.scrollWidth
  );

  // Track if individual conditions are met
  let verticalConditionMet = !trigger.vertical; // Default to true if not specified
  let horizontalConditionMet = !trigger.horizontal; // Default to true if not specified

  const checkScrollDepth = () => {
    if (triggered) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    const docHeight = Math.max(
      document.body.scrollHeight,
      document.documentElement.scrollHeight
    );
    const docWidth = Math.max(
      document.body.scrollWidth,
      document.documentElement.scrollWidth
    );
    const windowHeight = window.innerHeight;
    const windowWidth = window.innerWidth;

    // Update last known document dimensions
    lastDocHeight = docHeight;
    lastDocWidth = docWidth;

    // Check vertical trigger
    if (trigger.vertical && !verticalConditionMet) {
      if (trigger.vertical.type === "pixels") {
        if (scrollTop >= trigger.vertical.value) {
          verticalConditionMet = true;
        }
      } else { // percent
        const scrollPercent = (scrollTop / (docHeight - windowHeight)) * 100;
        if (scrollPercent >= trigger.vertical.value) {
          verticalConditionMet = true;
        }
      }
    }
    
    // Check horizontal trigger
    if (trigger.horizontal && !horizontalConditionMet) {
      if (trigger.horizontal.type === "pixels") {
        if (scrollLeft >= trigger.horizontal.value) {
          horizontalConditionMet = true;
        }
      } else { // percent
        const scrollPercent = (scrollLeft / (docWidth - windowWidth)) * 100;
        if (scrollPercent >= trigger.horizontal.value) {
          horizontalConditionMet = true;
        }
      }
    }

    // Trigger if both conditions are met
    if (verticalConditionMet && horizontalConditionMet && !triggered) {
      triggered = true;
      onTrigger();
    }
  };

  // Check on scroll with throttling (100ms)
  const scrollHandler = throttle(() => {
    requestAnimationFrame(checkScrollDepth);
  }, 200);

  // Check if ResizeObserver is supported
  const isResizeObserverSupported = typeof ResizeObserver !== "undefined";
  
  let resizeObserver = null;
  
  if (isResizeObserverSupported) {
    // Modern approach: Use ResizeObserver for dynamic content changes
    resizeObserver = new ResizeObserver((entries) => {
      const docHeight = Math.max(
        document.body.scrollHeight,
        document.documentElement.scrollHeight
      );
      const docWidth = Math.max(
        document.body.scrollWidth,
        document.documentElement.scrollWidth
      );
      
      // Only recheck if document dimensions have changed
      if (docHeight !== lastDocHeight || docWidth !== lastDocWidth) {
        requestAnimationFrame(checkScrollDepth);
      }
    });
    
    // Observe body for dynamic content changes
    resizeObserver.observe(document.body);
  }
  
  // Initial check
  checkScrollDepth();
  
  // Add scroll listener
  window.addEventListener("scroll", scrollHandler, { passive: true });

  // Return cleanup function
  return () => {
    window.removeEventListener("scroll", scrollHandler);
    
    if (isResizeObserverSupported && resizeObserver) {
      resizeObserver.disconnect();
    }
  };
}
