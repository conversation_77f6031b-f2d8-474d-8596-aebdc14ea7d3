import { NPS_QUESTION } from 'Data/question-types';
import { ReviewAnswer } from './answer';
import { reduce as _reduce, get as _get } from 'lodash';

export class ReviewAnswerNps extends ReviewAnswer {
  constructor(data) {
    super(data);
    
    this.setVariants = data.setVariants;
    this.skipped = data.skipped;

    this.clarifyingComments = _reduce(_.get(data, 'answer.extra', {}), (acc, el, key) => {
      const comment = el.answer || el.self_variant;
      if (!comment) {
        return acc;
      }
      const rating = +data.answer[+key];
      return [
        ...acc,
        {
          headerText: _get(data, 'variants.length') ? (_get(data.variants.find(v => v.id == key), 'question') || '') : '',
          commentText: comment,
          rating,
          ratingPoint: this.getRatingPoint(rating, 1, this.starCount),
        },
      ];
    }, []);
  }

  get type() {
    return NPS_QUESTION;
  }

  get withRating() {
    return this.rating !== null && this.rating >= 0;
  }

  get ratingPoint() {
    return this.getRatingPoint(this.rating, 0, 10);
  }
}
