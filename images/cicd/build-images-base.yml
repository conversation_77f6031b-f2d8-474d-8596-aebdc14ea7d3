include:
  - project: 'cicd/foquz-cicd'
    ref: main
    file: 'job-rules/rules.yml'

.job_build_imag_base: &job_build_configuration_base
  allow_failure: true
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  script:
    - >-
      if [ -z "$CI_COMMIT_TAG" ] && [ "$CI_COMMIT_BRANCH" = "develop"  ]; then    
        echo "DEVELOP"  
        echo "${NAME_POD}=true" >> build.env      
      else 
        if [ -n "$CI_COMMIT_TAG" ] && [ -z  "$PB_FLAG"  ]; then    
          echo "PRODUCTION NOT CHANGED"  
        else
          chmod 777 ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
          ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
          mkdir -p /kaniko/.docker
          echo "{\"auths\":{\"${REGISTRY}\":{\"auth\":\"$(echo -n "json_key:${CI_REGISTRY_KEY}" | base64 | tr -d '\n' )\"}}}" > /kaniko/.docker/config.json
          /kaniko/executor --cache=false \
          --context "${CI_PROJECT_DIR}"  \
          --build-arg "MODE=${MODE}" \
          --build-arg "TYPE=${TYPE}" \
          --dockerfile "${CI_PROJECT_DIR}/images/${IMAGE_NAME}/dockerfile" \
          --destination "${REGISTRY}/${IMAGE_NAME}:${TAG}"
          echo "${NAME_POD}=true" >> build.env
        fi
      fi
  artifacts:
    reports:
      dotenv: build.env


# Локальные anchor'ы (специфичные для проекта)
.branch_condition: &branch_condition
  $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes' || $CI_COMMIT_BRANCH == 'develop'

# Локальные условия изменений файлов (специфичные для проекта)

.php-changes: &php-changes
  paths:
    - 'images/${IMAGE_NAME}/**/*'
    - 'composer.json'

.mail-changes: &mail-changes
  paths:
    - 'emails-markup/**/*'
    - 'images/${IMAGE_NAME}/*'

build-foquz-core-alpine-php-fpm-base:
  stage: build-base
  extends: .rules_build_base
  variables:
    IMAGE_NAME: "foquz-core-alpine-php-fpm-base"
    NAME_POD: "CORE_FPM_BASE"
    PB_FLAG: "$PB_CORE_FPM_BASE"
  rules:
    - !reference [.rules_build_base, rules]
    # Используем локальный anchor для веток + локальные changes
    - if: *branch_condition
      changes: *php-changes
  <<: *job_build_configuration_base

build-foquz-core-cli-php-base:
  stage: build-base
  extends: .rules_build_base
  variables:
    IMAGE_NAME: "foquz-core-cli-php-base"
    NAME_POD: "CORE_CLI_BASE"
    PB_FLAG: "$PB_CORE_CLI_BASE"
  rules:
    - !reference [.rules_build_base, rules]
    # Используем локальный anchor для веток + локальные changes
    - if: *branch_condition
      changes: *php-changes
  <<: *job_build_configuration_base

build-foquz-core-foquz-core-js-mail-image:
  stage: build-base
  extends: .rules_build_base
  variables:
    IMAGE_NAME: "foquz-core-js-mail"
    NAME_POD: "CORE_JS_MAIL"
    PB_FLAG: "$PB_CORE_JS_MAIL"
  rules:
    - !reference [.rules_build_base, rules]
    # Используем локальный anchor для веток + локальные changes
    - if: *branch_condition
      changes: *mail-changes
  <<: *job_build_configuration_base



