include:
  - project: 'cicd/foquz-cicd'
    ref: main
    file: 'job-rules/rules.yml'

.job_build_image: &job_build_configuration
  allow_failure: true
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  script:
    - >-
      if [ -z "$CI_COMMIT_TAG" ] && [ "$CI_COMMIT_BRANCH" = "develop"  ]; then    
        echo "DEVELOP"      
        echo "${NAME_POD}=true" >> build.env
      else 
        if [ -n "$CI_COMMIT_TAG" ] && [ -z  "$PB_FLAG"  ]; then    
          echo "PRODUCTION NOT CHANGED"  
        else
          chmod 777 ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
          source ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
          echo "${BASE_TAG}"
          mkdir -p /kaniko/.docker
           echo "{\"auths\":{\"${REGISTRY}\":{\"auth\":\"$(echo -n "json_key:${CI_REGISTRY_KEY}" | base64 | tr -d '\n' )\"}}}" > /kaniko/.docker/config.json
          /kaniko/executor --cache=false \
          --build-arg "RELEASE=${RELEASE}" \
          --build-arg "REGISTRY=${REGISTRY}" \
          --build-arg "MODE=${MODE}"  \
          --build-arg "TYPE=${TYPE}" \
          --build-arg "TAG_BASE=${BASE_TAG}" \
          --build-arg "TAG_CORE_JS_MAIL=${CORE_JS_MAIL_TAG}" \
          --build-arg "SENTRY_PROJECT=${SENTRY_PROJECT}" \
          --build-arg "SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}" \
          --context "${CI_PROJECT_DIR}" \
          --dockerfile "${CI_PROJECT_DIR}/images/${IMAGE_NAME}/dockerfile" \
          --destination "${REGISTRY}/${IMAGE_NAME}:${TAG}" 
          echo "${NAME_POD}=true" >> build.env
        fi
      fi
  artifacts:
    reports:
      dotenv: build.env

.tag_condition: &tag_condition
  $CI_COMMIT_TAG =~ /^Ver-\d+\.\d+.\d+.*(|\+\w+)$/

.branch_condition: &branch_condition
  $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'  || $CI_COMMIT_BRANCH == 'develop'

.js-main-changes: &js-main-changes
  paths:
    - 'ko/**/*'
    - 'less/**/*'
    - 'messages/**/*'
    - 'webpack/**/*'
    - 'images/${IMAGE_NAME}/**/*'

.js-widget-iframe-changes: &js-widget-iframe-changes
  paths:
    - 'ko/widgets/poll_new/**/*'
    - 'images/${IMAGE_NAME}/*'

.core-nginx-changes: &core-nginx-changes
  paths:
    - 'web/**/*'
    - 'images/foquz-core-nginx/**/*'

.migrations-changes: &migrations-changes
  paths:
    - 'migrations/**/*'
    - 'images/foquz-core-migrations/**/*'
    - 'composer.json'

.job-init-changes: &job-init-changes
  paths:
    - 'commands/**/*'
    - 'images/foquz-core-job-init/**/*'

build-foquz-core-js-main:
  extends: .rules_build_base
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-js-main"
    NAME_POD: "CORE_JS_MAIN"
    PB_FLAG: "$PB_CORE_JS_MAIN"
  rules:
    - !reference [.rules_build_base, rules]
    - if: *branch_condition
      changes: *js-main-changes
  <<: *job_build_configuration

build-foquz-core-js-widget-iframe:
  extends: .rules_build_base
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-js-widget-iframe"
    NAME_POD: "CORE_JS_WIDGET_IFRAME"
    PB_FLAG: "$PB_CORE_JS_WIDGET_IFRAME"
  rules:
    - !reference [.rules_build_base, rules]
    - if: *branch_condition
      changes: *js-widget-iframe-changes
  <<: *job_build_configuration

build-foquz-core-nginx:
  extends: .rules_build_base
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-nginx"
    NAME_POD: "CORE_NGINX"
    PB_FLAG: "$PB_CORE_NGINX"
  rules:
    - !reference [.rules_build_base, rules]
    - if: *branch_condition
      changes: *core-nginx-changes
  <<: *job_build_configuration

build-foquz-core-main-app:
  extends: .rules_build_base
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-main-app"
    NAME_POD: "CORE_FPM"
    PB_FLAG: "$PB_CORE_FPM"
  rules:
    - !reference [.rules_build_base, rules]
    - if: *branch_condition
  <<: *job_build_configuration

build-foquz-core-consumer-main:
  extends: .rules_build_base
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-consumer-main"
    NAME_POD: "CORE_CONSUMER_MAIN"
    PB_FLAG: "$PB_CORE_CONSUMER_MAIN"
  rules:
    - !reference [.rules_build_base, rules]
    - if: *branch_condition
  <<: *job_build_configuration

build-foquz-core-consumer-mailings:
  extends: .rules_build_base
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-consumer-mailings"
    NAME_POD: "CORE_CONSUMER_MAILING"
    PB_FLAG: "$PB_CORE_CONSUMER_MAILING"
  rules:
    - !reference [.rules_build_base, rules]
    - if: *branch_condition
  <<: *job_build_configuration

#build-core-debug:
#  stage: build
#  variables:
#    IMAGE_NAME: "foquz-core-debug"
#    NAME_POD: "CORE_DEBUG"
#  rules:
#    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH =~ /TASK-.*/  || $CI_COMMIT_BRANCH =~ /Ver-.*/
#  <<: *job_build_configuration


build-foquz-core-consumer-widget:
  extends: .rules_build_base
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-consumer-widget"
    NAME_POD: "CORE_CONSUMER_WIDGET"
    PB_FLAG: "$PB_CORE_CONSUMER_WIDGET"
  rules:
    - !reference [.rules_build_base, rules]
    - if: $CI_COMMIT_TAG =~ /^Ver-\d+\.\d+.\d+.*(|\+mwi)$/
    - if: *branch_condition
  <<: *job_build_configuration


build-foquz-core-consumer-quiz:
  extends: .rules_build_base
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-consumer-quiz"
    NAME_POD: "CORE_CONSUMER_QUIZ"
    PB_FLAG: "$PB_CORE_CONSUMER_QUIZ"
  rules:
    - !reference [.rules_build_base, rules]
    - if: *branch_condition
  <<: *job_build_configuration

build-foquz-core-migrations:
  extends: .rules_build_base
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-migrations"
    NAME_POD: "CORE_MIGRATIONS"
    PB_FLAG: "$PB_CORE_MIGRATIONS"
  rules:
    - !reference [.rules_build_base, rules]
    - if: *branch_condition
      changes: *migrations-changes
  <<: *job_build_configuration

build-foquz-core-job-init:
   extends: .rules_build_base
   stage: build
   variables:
      IMAGE_NAME: "foquz-core-job-init"
      NAME_POD: "CORE_INIT"
      PB_FLAG: "$PB_CORE_INIT"
   rules:
      - !reference [.rules_build_base, rules]
      - if: *branch_condition
        changes: *job-init-changes
   <<: *job_build_configuration

#build-foquz-core-doc-builder:
#   stage: build
#   variables:
#     IMAGE_NAME: "foquz-core-doc-builder"
#     NAME_POD: "CORE_DOC_BUILDER"
#   rules:
#     - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
#       changes:
#         paths:
#           - 'commands/SwaggerController.php'
#           - 'modules/foquz/controllers/**/*'
#           - 'images/foquz-core-doc-builder/**/*'
#           - 'images/rebuild_all'
#   <<: *job_build_configuration

deploy-docs:
  stage: deploy
  resource_group: devfoquz
  variables:
    RELEASE: "$RELEASE"
  tags:
    - devfoquz
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
      changes:
        paths:
          - 'commands/SwaggerController.php'
          - 'modules/foquz/controllers/**/*'
          - 'images/foquz-core-doc-builder/**/*'
          - 'images/rebuild_all'
  image:
    name: $CI_REGISTRY/foquz-core-doc-builder:$TAG
    entrypoint: [""]
  script:
    - composer install
    - cp /var/www/devfoquz.ru/project/config/db.php config/db.php
    - cp /var/www/devfoquz.ru/project/config/db-log.php config/db-log.php
    - cp /var/www/devfoquz.ru/project/config/mailer.php config/mailer.php
    - cp /var/www/devfoquz.ru/project/config/params-local.php config/params-local.php
    - mkdir -p docs
    - ./yii swagger/go docs/swagger.yml
    - ./yii swagger/go docs/redoc.yml v1
    - >-
      curl -X POST "https://docs.foquzdev.ru/api/docs/upload" \
        -H "Host: ${RELEASE}.docs.foquzdev.ru" \
        -H "Authorization: Bearer ${DOCS_AUTH_TOKEN_DEV}" \
        -F "doc_type=core" \
        -F "file=@./"docs/swagger.yml
    - >-
      curl -X POST "https://docs.foquzdev.ru/api/docs/upload" \
        -H "Host: ${RELEASE}.docs.foquzdev.ru" \
        -H "Authorization: Bearer ${DOCS_AUTH_TOKEN_DEV}" \
        -F "doc_type=core_client" \
        -F "file=@./"docs/redoc.yml

