<?php

use app\components\RabbitMQComponent;
use app\modules\foquz\behaviors\queue\DiagnosticBehavior;
use yii\queue\amqp_interop\Queue;
use yii\queue\LogBehavior;
use yii\queue\serializers\JsonSerializer;
use yii\redis\Cache;


$config = [
    'components' => [
        'cache'              => [
            'class' => yii\redis\Cache::class,
            'redis' => [
                'hostname' => getenv('REDIS_HOSTNAME'),
                'password' => getenv('REDIS_PASSWORD'),
                'database' => getenv('REDIS_CACHE_DATABASE'),
                'port'     => getenv('REDIS_PORT'),
            ],
        ],
        'redis'              => [
            'class'    => yii\redis\Connection::class,
            'hostname' => getenv('REDIS_HOSTNAME'),
            'password' => getenv('REDIS_PASSWORD'),
            'database' => getenv('REDIS_DATABASE'),
            'port'     => getenv('REDIS_PORT'),
        ],
        'rabbit_queue'  => [
            'host'         => getenv('RABBITMQ_HOSTNAME'),
            'port'         => getenv('RABBITMQ_PORT'),
            'user'         => getenv('RABBITMQ_USER'),
            'password'     => getenv('RABBITMQ_PASSWORD'),
            'vhost'        => getenv('RABBITMQ_VHOST'),
            'class'        => yii\queue\amqp_interop\Queue::class,
            'queueName'    => 'foquz',
            'driver'       => yii\queue\amqp_interop\Queue::ENQUEUE_AMQP_LIB,
            'serializer'   => yii\queue\serializers\JsonSerializer::class,
            'exchangeName' => 'foquz',
            'as log'       => yii\queue\LogBehavior::class,
        ],
    ],
    'bootstrap'  => [
        'rabbit_queue',
    ],
];

return $config;