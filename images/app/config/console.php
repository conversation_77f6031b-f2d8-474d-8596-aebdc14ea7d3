<?php

use app\components\JsonStdoutLogTarget;
use notamedia\sentry\SentryTarget;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\View;

$protocol = empty(getenv('HTTP_PROTOCOL')) ? "https" : getenv('HTTP_PROTOCOL');
$mailerFromName = empty(getenv('MAILER_FROM_NAME')) ? "Foquz" : getenv('MAILER_FROM_NAME');
$mailerFromEmail = empty(getenv('MAILER_FROM')) ? "<EMAIL>" : getenv('MAILER_FROM');

$params = [
    'protocol'              => $protocol,
    'main_sender'           => [$mailerFromEmail => $mailerFromName],
    'main_sender_email'     => $mailerFromEmail,
    'default_logo_link'     => empty(getenv('BASE_URL')) ? 'https://foquz.ru' : getenv('BASE_URL'),
    'ffmpeg_binaries'       => '/usr/bin/ffmpeg',
    'ffprobe_binaries'      => '/usr/bin/ffprobe',
    'bsDependencyEnabled'   => false,
    'system_sender'         => $mailerFromEmail,
    'queueNamePrefix'       => 'core',
    'host'                  => getenv('HOST'),
    'short_link'            => getenv('SHORT_LINK'),
    'hr_manager_company_id' => 2108,
    'adminEmail'            => empty(getenv('ADMIN_EMAIL')) ? "<EMAIL>" : getenv('ADMIN_EMAIL'),
    'queueDiagnosticFile'          => '@runtime/last_start_time.txt',
    'template_company_id'          => empty(getenv('TEMPLATE_COMPANY_ID')) ? 0 : getenv('TEMPLATE_COMPANY_ID'),
    'template_company_folder_id'   => empty(getenv('TEMPLATE_FOLDER_ID')) ? 0 : getenv('TEMPLATE_FOLDER_ID'),
];


$config = [
    'modules'             => [
        'user-management' => [
            'class'               => 'webvimark\modules\UserManagement\UserManagementModule',
            'controllerNamespace' => 'vendor\webvimark\modules\UserManagement\controllers',
        ],
    ],
    'id'                  => 'foquz-app',
    'basePath'            => dirname(__DIR__),
    'bootstrap'           => ['log'],
    'controllerNamespace' => 'app\commands',
    'timeZone'            => 'Europe/Moscow',
    'aliases'             => [
        '@bower'   => '@vendor/bower-asset',
        '@npm'     => '@vendor/npm-asset',
        '@web'     => '@app/web',
        '@uploads' => '@app/web/uploads',
    ],
    'components'          => [
        'urlManager'  => [
            'class'           => 'yii\web\UrlManager',
            'scriptUrl'       => 'https://foquz.ru/',
            'baseUrl'         => 'https://foquz.ru/',
            'hostInfo'        => 'https://foquz.ru/',
            'enablePrettyUrl' => true,
            'showScriptName'  => false,
            'rules'           => [
                'GET /p/<id:\w+>' => 'foquz/default/anonymous',
            ],
        ],
        'authManager' => [
            'class' => 'yii\rbac\DbManager',
        ],
        'log'         => [
            'flushInterval' => 1,
        'targets'          => [
                'errors' => [
                    'class'   => JsonStdoutLogTarget::class,
                    'url'     => 'php://stderr',
                    'levels'  => ['error', 'warning'],
                    'except'  => [
                        'yii\web\HttpException:404',
                    ],
                    'logVars' => ['_FILES'/*, '_COOKIE', '_SESSION'*/],
                    'exportInterval' => 1
                ],
                'info'   => [
                    'class'          => JsonStdoutLogTarget::class,
                    'url'            => 'php://stdout',
                    'levels'         => ['info'],
                    'categories'     => [
                        'app\*',
                      //  'User',
                        'notifications',
                        'yii\queue\*',
                        'object_changes',
                        'auth',
                        'debug',
                        'app\modules\foquz\queue\*'
                    ],
                    'logVars'        => [],
                    'exportInterval' => 1
                ],
            ],
        ],
        'db'          => [
        'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=' . getenv('DB_HOST') . ':' . getenv('DB_PORT') . ';dbname=' . getenv('DB_DBNAME'),
            'username' => getenv('DB_USER'),
            'password' => getenv('DB_PASSWORD'),
            'charset'  => 'utf8mb3',
            'enableSchemaCache' => true,
            'schemaCacheDuration' => 60 * 60 * 6,
        ],
        'view'        => [
            'class'     => View::class,
            'renderers' => [
                'twig' => [
                    'class'     => 'yii\twig\ViewRenderer',
                    'cachePath' => '@runtime/Twig/cache',
                    'options'   => [
                        'auto_reload' => true,
                    ],
                    'globals'   => [
                        'html' => ['class' => Html::class],
                        'Url'  => ['class' => Url::class],
                    ],
                    'uses'      => ['yii\bootstrap'],
                ],
            ],
            'theme'     => [
                'pathMap' => [
                    '@vendor/webvimark/module-user-management/views' => '@app/views/user',
                ],
            ],
        ],
    ],
    'params'              => $params,
];

$config = array_merge_recursive(
    $config,
    require(__DIR__ . '/console-db-log.php')
);

if (file_exists(__DIR__ . '/console.pod.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/console.pod.php')
    );
}


if (file_exists(__DIR__ . '/console.type.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/console.type.php')
    );
}


if (file_exists(__DIR__ . '/console.mode.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/console.mode.php')
    );
}

if (file_exists(__DIR__ . '/console.type.mode.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/console.type.mode.php')
    );
}

if (file_exists("/app/.mysql/root.crt")) {
    $config['components']['db']['attributes'] = [
        PDO::MYSQL_ATTR_SSL_CA                 => "/app/.mysql/root.crt",
        PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false
    ];
} elseif (file_exists("/var/.mysql/root.crt")) {
    $config['components']['db']['attributes'] = [
        PDO::MYSQL_ATTR_SSL_CA                 => "/var/.mysql/root.crt",
        PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false
    ];

}

if (file_exists(__DIR__ . '/params-local.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/params-local.php')
    );
}


if (!empty(getenv('SENTRY_CLI'))) {
    $config['components']['log']['targets']['sentry'] = [
        'class'   => SentryTarget::class,
        'dsn'     => getenv('SENTRY_CLI'),
        'levels'  => ['error', 'warning'],
        'context' => true,
    ];
}

return $config;
