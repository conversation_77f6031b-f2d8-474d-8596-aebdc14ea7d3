<?php

use yii\debug\Module;

$cookieDomain = "." . getenv('HOST');

$config = [
    'as hostControl' => [
        'class'        => 'yii\filters\HostControl',
        'allowedHosts' => [
            'doxswf.ru',
            '*.doxswf.ru',
            'devfoquz.ru',
            '*.devfoquz.ru',
            'foquzdev.ru',
            '*.foquzdev.ru',
            'localhost',
            '*.localhost',
            'foquz-nginx',
            getenv("INTERNAL_HOST"),
        ]
    ],
    "components"     => [
        'session'       => [
            'cookieParams' => [
                'domain'   => $cookieDomain,
            ],
        ],
        'request'       => [
            'csrfCookie'          => [
                'domain'   =>  $cookieDomain,
            ],
        ],
        'user'          => [
            'identityCookie'  => [
                'domain' => $cookieDomain,
            ],
        ],
    ],
    'modules' => [
        'debug' => [
            'class' => 'yii\debug\Module',
            'allowedIPs' => array_filter(explode(',', getenv("DEBUG_ALLOWED_IPS") ?: '')),
            'disableIpRestrictionWarning' => true,
        ],
    ],
    'bootstrap' => ['debug'],
    "params"         => [

    ]
];

return $config;




