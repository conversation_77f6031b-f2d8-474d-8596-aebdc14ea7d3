FROM cr.yandex/crpsvv5iscafkhlubkmt/php:8.2-cli-alpine3.22

ENV WORK_DIR /data/foquz
ARG PHPEXT_REDIS_VERSION=6.1.0
ARG PHPEXT_ZIP_VERSION=1.22.4
ARG MODE= production

ADD ./images/foquz-core-cli-php-base/php.ini /usr/local/etc/php/conf.d/errors.ini
COPY ./images/foquz-core-cli-php-base/composer.json /app/composer.json


RUN set -eux; \
  persistentDeps=" \
    libzip \
    icu \
    freetype \
    libjpeg-turbo \
    libpng \
    libwebp \
    libxpm \
  "; \
  \
  buildDeps=" \
    curl \
    git \
    libzip-dev \
    icu-dev \
    linux-headers \
    freetype-dev \
    libjpeg-turbo-dev \
    libpng-dev \
    libwebp-dev \
    libxpm-dev \
  "; \
  \
  apk add --no-cache --virtual .persistent-deps ${persistentDeps}; \
  apk add --no-cache --virtual .build-deps ${buildDeps}; \
  \
  pecl bundle -d /usr/src/php/ext zip-${PHPEXT_ZIP_VERSION}; \
  pecl bundle -d /usr/src/php/ext redis-${PHPEXT_REDIS_VERSION}; \
  \
  docker-php-ext-configure gd \
    --with-freetype \
    --with-jpeg \
    --with-webp \
    --with-xpm \
  ; \
  docker-php-ext-install -j$(nproc) \
   exif \
   gd \
   intl \
   pdo_mysql \
   sockets \
   redis \
   zip \
   opcache; \
  \
  apk add --no-cache  \
    ffmpeg \
 		busybox-extras; \
  apk del --no-cache --no-network .build-deps; \
  docker-php-source delete; \
	mv "$PHP_INI_DIR/php.ini-$MODE" "$PHP_INI_DIR/php.ini"; \
	php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"; \
	php -r "if (hash_file('sha384', 'composer-setup.php') === 'dac665fdc30fdd8ec78b38b9800061b4150413ff2e3b6f88543c636f7cd84f6db9189d43a81e5503cda447da73c7e5b6') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;"; \
  php composer-setup.php; \
	mv composer.phar /usr/local/bin/composer; \
  rm composer-setup.php; \
  cd /app; \
  composer install --no-cache --no-dev --optimize-autoloader;  \
	composer clearcache;  \
  composer audit --abandoned=ignore;  \
	rm /usr/local/bin/composer; \
  rm -rf /tmp/* /var/tmp/*;
