#!/bin/sh

echo "$MODE"
echo "$TYPE"
echo "$IMAGE_NAME"
echo "$TAG"
echo "$CORE_FPM_BASE"


if [ -e "images/dockerignore/$IMAGE_NAME/$TYPE$MODE" ]
then
    echo "images/dockerignore/$IMAGE_NAME/$TYPE$MODE > .dockerignore";
    cp "images/dockerignore/$IMAGE_NAME/$TYPE$MODE"  ".dockerignore"
else
  if [ -e "images/dockerignore/$IMAGE_NAME/$MODE" ]
  then
    echo "images/dockerignore/$IMAGE_NAME/$MODE > .dockerignore";
    cp "images/dockerignore/$IMAGE_NAME/$MODE"  ".dockerignore"
  else
     if [ -e "images/dockerignore/$IMAGE_NAME/$TYPE" ]
     then
       echo "images/dockerignore/$IMAGE_NAME/$TYPE > .dockerignore";
       cp "images/dockerignore/$IMAGE_NAME/$TYPE"  ".dockerignore"
     else
       if [ -e "images/dockerignore/$IMAGE_NAME/.dockerignore" ]
       then
         echo "images/dockerignore/$IMAGE_NAME/.dockerignore > .dockerignore";
         cp "images/dockerignore/$IMAGE_NAME/.dockerignore"  ".dockerignore"
      fi
     fi
  fi
fi


if [ -e "images/$IMAGE_NAME/$TYPE$MODE.dockerfile" ]
then
    echo "images/$IMAGE_NAME/$TYPE$MODE.dockerfile > images/$IMAGE_NAME/dockerfile";
    cp "images/$IMAGE_NAME/$TYPE$MODE.dockerfile"  "images/$IMAGE_NAME/dockerfile"
else
  if [ -e "images/$IMAGE_NAME/$MODE.dockerfile" ]
  then
    echo "images/$IMAGE_NAME/$MODE.dockerfile > images/$IMAGE_NAME/dockerfile";
    cp "images/$IMAGE_NAME/$MODE.dockerfile"  "images/$IMAGE_NAME/dockerfile"
  else
     if [ -e "images/$IMAGE_NAME/$TYPE.dockerfile" ]
     then
       echo "images/$IMAGE_NAME/$TYPE.dockerfile > images/$IMAGE_NAME/dockerfile";
       cp "images/$IMAGE_NAME/$TYPE.dockerfile"  "images/$IMAGE_NAME/dockerfile"
     fi
  fi
fi

if [ -e "images/$IMAGE_NAME/composer.$MODE.json" ]
then
    echo "images/$IMAGE_NAME/composer.$MODE.json > images/$IMAGE_NAME/composer.json"
    cp "images/$IMAGE_NAME/composer.$MODE.json"  "images/$IMAGE_NAME/composer.json"
fi

if [ -e "images/$IMAGE_NAME/php.$MODE.ini" ]
then
    echo "images/$IMAGE_NAME/php.$MODE.ini > images/$IMAGE_NAME/php.ini"
    cp "images/$IMAGE_NAME/php.$MODE.ini"  "images/$IMAGE_NAME/php.ini"
fi


if [ "$IMAGE_NAME" = "foquz-core-main-app" ]
then
    if [ -e "images/app/config/web.$TYPE.$MODE.php" ]
    then
       echo "images/app/config/web.$TYPE.$MODE.php > images/app/config/web.type.mode.php"
       cp "images/app/config/web.$TYPE.$MODE.php"  "images/app/config/web.type.mode.php"
    fi
    if [ -e "images/app/config/web.$TYPE.php" ]
    then
       echo "images/app/config/web.$TYPE.php > images/app/config/web.type.php"
       cp "images/app/config/web.$TYPE.php"  "images/app/config/web.type.php"
    fi
    if [ -e "images/app/config/web.$MODE.php" ]
    then
       echo "images/app/config/web.$MODE.php > images/app/config/web.mode.php"
       cp "images/app/config/web.$MODE.php"  "images/app/config/web.mode.php"
    fi

    if [ -e "images/app/web/index.$TYPE.$MODE.php" ]
    then
        echo "images/app/web/index.$TYPE.$MODE.php > images/app/web/index.php"
        cp "images/app/web/index.$TYPE.$MODE.php"  "images/app/web/index.php"
    else
      if [ -e "images/app/web/index.$MODE.php" ]
      then
        echo "images/app/web/index.$MODE.php > images/app/web/index.php"
        cp "images/app/web/index.$MODE.php"  "images/app/web/index.php"
      else
         if [ -e "images/app/web/index.$TYPE.php" ]
         then
            echo "images/app/web/index.$TYPE.php > images/app/web/index.php"
            cp "images/app/web/index.$TYPE.php"  "images/app/web/index.php"
         fi
      fi
    fi
fi



if [ "$IMAGE_NAME" = "foquz-core-main-app" ] || [ "$IMAGE_NAME" = "foquz-core-migrations"  ]   || [ "$IMAGE_NAME" = "foquz-core-job-init"  ]  \
|| [ "$IMAGE_NAME" = "foquz-core-debug"  ] || [ "$IMAGE_NAME" = "foquz-core-consumer-widget"  ] || [ "$IMAGE_NAME" = "foquz-core-consumer-quiz"  ] \
|| [ "$IMAGE_NAME" = "foquz-core-consumer-main"  ] || [ "$IMAGE_NAME" = "foquz-core-consumer-mailings"  ]
then
    if [ -e "images/app/config/console.$TYPE.$MODE.php" ]
    then
       echo "images/app/config/console.$TYPE.$MODE.php > images/app/config/console.type.mode.php"
       cp "images/app/config/console.$TYPE.$MODE.php"  "images/app/config/console.type.mode.php"
    fi
    if [ -e "images/app/config/console.$TYPE.php" ]
    then
       echo "images/app/config/console.$TYPE.php > images/app/config/console.type.php"
       cp "images/app/config/console.$TYPE.php"  "images/app/config/console.type.php"
    fi
    if [ -e "images/app/config/console.$MODE.php" ]
    then
       echo "images/app/config/console.$MODE.php > images/app/config/console.mode.php"
       cp "images/app/config/console.$MODE.php"  "images/app/config/console.mode.php"
    fi

    if [ -e "images/app/yii.$TYPE.$MODE" ]
    then
        echo "images/app/yii.$TYPE.$MODE > images/app/yii"
        cp "images/app/yii.$TYPE.$MODE"  "images/app/yii"
    else
      if [ -e "images/app/yii.$MODE" ]
      then
        echo "images/app/yii.$MODE > images/app/yii"
        cp "images/app/yii.$MODE"  "images/app/yii"
      else
         if [ -e "images/app/yii.$TYPE" ]
         then
            echo "images/app/yii.$TYPE > images/app/yii"
            cp "images/app/yii.$TYPE"  "images/app/yii"
         fi
      fi
    fi

    if [ -d "images/app" ]
    then
        echo  "images/app > ."
       cp -R "images/app/." "."
    fi
fi

if [ "$IMAGE_NAME" = "foquz-core-main-app" ] 
then
    if [ -e "images/$IMAGE_NAME/config/console.pod.php" ]
    then
       echo "images/$IMAGE_NAME/app/config/console.pod.php > images/$IMAGE_NAME/app/config/console.pod.php"
       cp "images/$IMAGE_NAME/app/config/console.pod.php"  "images/$IMAGE_NAME/app/config/console.pod.php"
    fi
fi 

if [ "$IMAGE_NAME" = "foquz-core-main-app" ] || [ "$IMAGE_NAME" = "foquz-core-migrations"  ]   || [ "$IMAGE_NAME" = "foquz-core-job-init"  ]  \
|| [ "$IMAGE_NAME" = "foquz-core-debug"  ] || [ "$IMAGE_NAME" = "foquz-core-consumer-widget"  ] || [ "$IMAGE_NAME" = "foquz-core-consumer-quiz"  ] \
|| [ "$IMAGE_NAME" = "foquz-core-consumer-main"  ] || [ "$IMAGE_NAME" = "foquz-core-consumer-mailings"  ] || [ "$IMAGE_NAME" = "foquz-core-doc-builder"  ]
then
    if [ -e "images/$IMAGE_NAME/app/config/console.$TYPE.$MODE.php" ]
    then
       echo "images/$IMAGE_NAME/app/config/console.$TYPE.$MODE.php > images/$IMAGE_NAME/app/config/console.type.mode.php"
       cp "images/$IMAGE_NAME/app/config/console.$TYPE.$MODE.php"  "images/$IMAGE_NAME/app/config/console.type.mode.php"
    fi
    if [ -e "images/$IMAGE_NAME/app/config/console.$TYPE.php" ]
    then
       echo "images/$IMAGE_NAME/app/config/console.$TYPE.php > images/$IMAGE_NAME/app/config/console.type.php"
       cp "images/$IMAGE_NAME/app/config/console.$TYPE.php"  "images/$IMAGE_NAME/app/config/console.type.php"
    fi
    if [ -e "images/$IMAGE_NAME/app/config/console.$MODE.php" ]
    then
       echo "images/$IMAGE_NAME/app/config/console.$MODE.php > images/$IMAGE_NAME/app/config/console.mode.php"
       cp "images/$IMAGE_NAME/app/config/console.$MODE.php"  "images/$IMAGE_NAME/app/config/console.mode.php"
    fi
    if [ -e "images/$IMAGE_NAME/app/config/console.pod.php" ]
    then
       echo "images/$IMAGE_NAME/app/config/console.pod.php > images/$IMAGE_NAME/app/config/console.pod.php"
       #cp "images/$IMAGE_NAME/app/config/console.pod.php"  "images/$IMAGE_NAME/app/config/console.pod.php"
    fi


    if [ -e "images/$IMAGE_NAME/app/yii.$TYPE.$MODE" ]
    then
        echo "images/$IMAGE_NAME/app/yii.$TYPE.$MODE > images/$IMAGE_NAME/app/yii"
        cp "images/$IMAGE_NAME/app/yii.$TYPE.$MODE"  "images/$IMAGE_NAME/app/yii"
    else
      if [ -e "images/$IMAGE_NAME/app/yii.$MODE" ]
      then
        echo "images/$IMAGE_NAME/app/yii.$MODE > images/$IMAGE_NAME/app/yii"
        cp "images/$IMAGE_NAME/app/yii.$MODE"  "images/$IMAGE_NAME/app/yii"
      else
         if [ -e "images/$IMAGE_NAME/app/yii.$TYPE" ]
         then
            echo "images/$IMAGE_NAME/app/yii.$TYPE > images/$IMAGE_NAME/app/yii"
            cp "images/$IMAGE_NAME/app/yii.$TYPE"  "images/$IMAGE_NAME/app/yii"
         fi
      fi
    fi

    if [ -d "images/$IMAGE_NAME/app" ]
    then
       echo  "images/$IMAGE_NAME/app > ."
       cp -R "images/$IMAGE_NAME/app/." "."
    fi

    if  [ "$IMAGE_NAME" = "foquz-core-main-app" ]
    then
        if [ "$CORE_FPM_BASE" = "true"  ]
        then
          echo "1"
          BASE_TAG="$TAG"
        else
          echo "http://deploy.devfoquz.ru/get_tag.php?release=$RELEASE&image=foquz-core-alpine-php-fpm-base"
          BASE_TAG=$(wget "http://deploy.devfoquz.ru/get_tag.php?release=$RELEASE&image=foquz-core-alpine-php-fpm-base"  -q -O  -)
        fi
    else
        if [ "$CORE_CLI_BASE" = "true" ]
        then
          echo "1"
          BASE_TAG="$TAG"
        else
          echo "http://deploy.devfoquz.ru/get_tag.php?release=$RELEASE&image=foquz-core-cli-php-base"
          BASE_TAG=$(wget "http://deploy.devfoquz.ru/get_tag.php?release=$RELEASE&image=foquz-core-cli-php-base"  -q -O  -)
        fi
    fi

    if [ "$CORE_JS_MAIL" = "true"  ]
    then
      echo "1"
      CORE_JS_MAIL_TAG="$TAG"
    else
      echo "http://deploy.devfoquz.ru/get_tag.php?release=$RELEASE&image=foquz-core-js-mail"
      CORE_JS_MAIL_TAG=$(wget "http://deploy.devfoquz.ru/get_tag.php?release=$RELEASE&image=foquz-core-js-mail"  -q -O  -)
    fi
fi

if  [ "$IMAGE_NAME" = "foquz-core-js-main" ]
then
    if [ "$MODE" = "development" ]
    then
      export SENTRY_PROJECT="$SENTRY_DEV_PROJECT"
      export SENTRY_AUTH_TOKEN="$SENTRY_DEV_AUTH_TOKEN"
    else
      export SENTRY_PROJECT="$SENTRY_PROD_PROJECT"
      export SENTRY_AUTH_TOKEN="$SENTRY_PROD_AUTH_TOKEN"
    fi
fi

echo "$BASE_TAG"
echo "$CORE_JS_MAIL_TAG"
echo "$SENTRY_PROJECT"
echo "$SENTRY_AUTH_TOKEN"
export BASE_TAG="$BASE_TAG"
export CORE_JS_MAIL_TAG="$CORE_JS_MAIL_TAG"
cat ./.dockerignore
