<?php

namespace app\models;


use app\modules\foquz\models\BaseModel;
use yii\behaviors\TimestampBehavior;
use yii\helpers\ArrayHelper;

/**
 * Class for table filials
 *
 * @property int $id
 * @property string $crm_id
 * @property string $name
 * @property boolean $is_active
 * @property string $iiko_id
 * @property int $company_id
 * @property int|null $category_id
 * @property string $address
 * @property string $google_location
 * @property string $yandex_link
 * @property string $param1 Параметр 1 для переменной
 * @property string $param2 Параметр 2 для переменной
 * @property string $param3 Параметр 3 для переменной
 * @property string $created_at
 * @property string $google_coordinates
 * @property string $logo Имя файла логотипа
 * @property FilialCategory|null $category
 */
class Filial extends BaseModel
{
    public static function tableName()
    {
        return 'filials';
    }

    public function rules()
    {
        return [
            [['crm_id', 'name', 'iiko_id', 'address', 'google_location', 'yandex_link', 'created_at', 'google_coordinates', 'logo'], 'string'],
            [['param1', 'param2', 'param3'], 'string', 'max' => 2048],
            [['is_active'], 'safe'],
            [['category_id'], 'integer'],
            [['category_id'], 'exist', 'targetRelation' => 'category'],
            [['crm_id'], 'unique'],
        ];
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::className(),
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => date('Y-m-d H:i:s')
            ]
        ];
    }

    public function fields()
    {
        return ArrayHelper::merge(parent::fields(), [
            'category_name' => function () {
                return $this->category->name ?? null;
            },
        ]);
    }

    public function beforeSave($insert)
    {
       if($this->google_location && $this->google_location != $this->getOldAttribute('google_location')){
           /*неактуально*/
          /* $client = GoogleClient::getBusinessClient();
           $accountId = \Yii::$app->params['google_reviews_account_id'];
           $locationId = $this->google_location;
           $response = $client->get("https://mybusiness.googleapis.com/v4/accounts/$accountId/locations/$locationId");
           $body = $response->getBody();
           $location = json_decode($body);
           if(isset($location->locationKey))
               $this->google_coordinates = $location->locationKey->placeId;*/
        }
        return parent::beforeSave($insert); // TODO: Change the autogenerated stub
    }

    public static function create($name)
    {
        $filial = self::find()->where(["name" => $name])->one();
        if (!$filial) {
            $filial = new self;
            $filial->name = $name;
            $filial->save();
        }
        return $filial;
    }

    public static function iiko($params, $companyId = null)
    {
        $filial = null;
        if ($params) {
            $id = @$params["deliveryTerminalId"];
            $name = @$params["restaurantName"];
            if ($id) {
                $filial = self::find()->where(["iiko_id" => $id])->one();
                if (!$filial) {
                    $filial = new self();
                    $filial->name = $name;
                    $filial->iiko_id = $id;
                    $filial->company_id = $companyId;
                    if (!$filial->save()) {
                        $filial = null;
                    }

                } else if ($filial->name != $name || $filial->company_id<>$companyId) {
                    $filial->name = $name;
                    $filial->company_id = $companyId;
                    if (!$filial->save()) {
                        $filial = null;
                    }
                }
            }
        }


        return $filial;
    }

    public function getCategory()
    {
        return $this->hasOne(FilialCategory::class, ['id' => 'category_id']);
    }
}
