<?php

namespace app\models\company;

use app\modules\foquz\models\BaseModel;
use app\modules\foquz\models\FoquzPoll;
use Yii;

/**
 * This is the model class for table "company_request_processing_settings".
 *
 * @property int $id
 * @property int $company_id
     * @property int|null $request_processing_enabled
 * @property int|null $answer_with_complain
 * @property int|null $rate_answer
 * @property int|null $rate_answer_percent
 * @property int|null $rate_answer_with_comment
 *
 * @property Company $company
 * @property RequestSettings2Poll[] $requestSettings2Polls
 */
class CompanyRequestProcessingSettings extends BaseModel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_request_processing_settings';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['company_id'], 'required'],
            [['company_id', 'request_processing_enabled', 'answer_with_complain', 'rate_answer', 'rate_answer_percent', 'rate_answer_with_comment'], 'integer'],
            [['percents'], 'integer', 'min'=> 20, 'max' => 100],
            [['company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::className(), 'targetAttribute' => ['company_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'company_id' => 'Company ID',
            'request_processing_enabled' => 'Request Processing Enabled',
            'answer_with_complain' => 'Answer With Complain',
            'rate_answer' => 'Rate Answer',
            'rate_answer_percent' => 'Rate Answer Percent',
            'rate_answer_with_comment' => 'Rate Answer With Comment',
            'percents' => 'Rate Answer Percent',
        ];
    }

    /**
     * Gets query for [[Company]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCompany()
    {
        return $this->hasOne(Company::className(), ['id' => 'company_id']);
    }

    /**
     * Gets query for [[RequestSettings2Polls]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getRequestSettings2Polls()
    {
        return $this->hasMany(RequestSettings2Poll::className(), ['request_settings_id' => 'id']);
    }

    public function getPollsForComplaints()
    {
        return $this->hasMany(FoquzPoll::class, ['id' => 'foquz_poll_id'])->via('requestSettings2Polls', function($query) {
            $query->andWhere(['type' => RequestSettings2Poll::PROCESSING_TYPE_ANSWER_WITH_COMPLAIN]);
        });
    }

    public function getPollsForRates()
    {
        return $this->hasMany(FoquzPoll::class, ['id' => 'foquz_poll_id'])->via('requestSettings2Polls', function($query) {
            $query->andWhere(['type' => RequestSettings2Poll::PROCESSING_TYPE_RATE_ANSWER]);
        });
    }

    public function getPollsForRatesWithComment()
    {
        return $this->hasMany(FoquzPoll::class, ['id' => 'foquz_poll_id'])->via('requestSettings2Polls', function($query) {
            $query->andWhere(['type' => RequestSettings2Poll::PROCESSING_TYPE_RATE_ANSWER_WITH_COMMENT]);
        });
    }

    public function fields()
    {
        $fields =  parent::fields(); // TODO: Change the autogenerated stub

        return $fields + [
            'requestSettings2Polls'
            ];
    }

    public function savePollRelations($type, $poll)
    {
        $rp = new RequestSettings2Poll();
        $rp->request_settings_id = $this->id;
        $rp->foquz_poll_id = $poll->id;
        $rp->type = $type;
        $rp->save();
    }
}
