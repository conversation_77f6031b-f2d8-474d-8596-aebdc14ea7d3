<?php

namespace app\models\company;

use app\components\RabbitMQComponent;
use app\models\Language;
use app\models\User;
use app\modules\foquz\models\BaseModel;
use app\modules\foquz\models\channels\GlobalEmailSettings;
use app\modules\foquz\models\CompanyAffiliateCode;
use app\modules\foquz\models\CompanyTariff;
use app\modules\foquz\models\ContactAdditionalField;
use app\modules\foquz\models\ExternalResource;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollDesignTemplate;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\LegalEntity;
use app\modules\foquz\models\mailings\MailingListSend;
use app\modules\foquz\models\requests_project\Project;
use app\modules\foquz\models\Tariff;
use app\modules\foquz\services\notifications\NotificationsSystemService;
use DateTime;
use Yii;
use yii\base\DynamicModel;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;
use yii\helpers\Html;
use yii\web\IdentityInterface;

/**
 * This is the model class for table "{{%company}}".
 *
 * @property int $id
 * @property string $created_at
 * @property string $updated_at
 * @property string $alias
 * @property string $short_alias Короткий домен
 * @property string $name
 * @property int $limit_answers
 * @property boolean $unlimited
 * @property int $limit_mails
 * @property boolean $unlimited_mails
 * @property int $affiliate_code_id
 * @property boolean $deleted
 * @property string $deleted_at
 * @property int $deleted_by
 * @property string $hashid
 * @property int $language_id
 * @property int $limit_users
 * @property bool $mailing_limit Ограничение отправки опросов для одного контакта
 * @property int $mailing_frequency Частота отправки, раз в n дней
 * @property bool $widget_display_limit Ограничение показа виджета для одного контакта (0 - не ограничено, 1 - ограничено)
 * @property int|null $widget_display_frequency Ограничение показа виджета для одного контакта
 * @property string $pachka_access_token
 * @property boolean $is_answer_limited Получение новых ответов ограничено
 *
 * @property CompanyIikoAccess[] $companyIikoAccesses
 * @property CompanyStaff[] $companyStaff
 * @property User[] $companyUsers
 * @property FoquzPoll[] $foquzPolls
 * @property ContactAdditionalField[] $contactAdditionalFields
 * @property Tariff $tariff
 * @property-read CompanyTariff $currentTariff
 * @property FoquzPollDesignTemplate[] $designTemplates
 * @property CompanyFilterSet[] $companyFilterSettings
 * @property-read int $answersLimit
 * @property-read int $answersTariffPeriodCount
 * @property-read int $activeUsersCount
 * @property-read string $shortLink
 * @property-read int|null $userLimit
 * @property-read bool $allow_mailing_send
 *
 * @property int $answer_cost [int(11)]
 * @property int $legal_entity_id [int(11)]
 * @property bool $auto_poll_enabled [tinyint(1)]
 * @property bool $mailings_enabled [tinyint(1)] Рассылки включены
 * @property bool $contacts_enabled [tinyint(1)] Раздел Контакты включен
 * @property string $requests_projects_key [varchar(255)]
 * @property bool $show_foquz_label Показывать метку foquz в упрощенной версии прохождения
 * @property int|null $filesize_limit Лимит для файла (Мб)
 */
class Company extends BaseModel
{
    const DELETED = 1;
    const NOT_DELETED = 0;

    public $company_id;

    /** @inheritDoc */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => (new DateTime('now'))->format('Y-m-d H:i:s')
            ]
        ];
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            $defaultTariff = Tariff::findOne(['title' => 'Базовый']);
            $companyTariff = new CompanyTariff([
                'company_id' => $this->id,
                'tariff_id'  => $defaultTariff->id,
                'from'       => date('Y-m-d', strtotime('first day of this month')),
                'to'         => date('Y-m-d', strtotime('last day of this month'))
            ]);
            $companyTariff->save();
            $t = time();
            $rand = rand(100000, 999999);
            $number = $rand + $t;
            $companyAffiliateCode = new CompanyAffiliateCode([
                'company_id' => $this->id,
                'code'       => substr($number, -6),
                'url'        => \Yii::$app->params['protocol'] . '://' . \Yii::$app->params['host'] . '/r/' . uniqid(substr($number,
                        -6))
            ]);
            $companyAffiliateCode->save();

            $this->createDefaultEmailSettings();

            $this->hashid = uniqid($this->id);
            $this->save();
        }
        if (isset(Yii::$app->rabbit)) {
            /** @var RabbitMQComponent $rabbit */
            $rabbit = Yii::$app->rabbit;
            $rabbit->queue('widget.company_settings')
                ->type('upsert_settings')
                ->push([
                    'company_id'        => $this->id,
                    'display_limit'     => $this->widget_display_limit ? $this->widget_display_frequency : null,
                    'is_answer_limited' => (int)$this->is_answer_limited,
                ]);
        }
        parent::afterSave($insert, $changedAttributes); // TODO: Change the autogenerated stub
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%company}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['alias', 'name'], 'required'],
            [
                'alias',
                'filter',
                'filter' => function ($value) {
                    return strtolower($value);
                }
            ],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['alias', 'short_alias'], 'string', 'max' => 60],
            [['name', 'hashid'], 'string', 'max' => 255],
            [
                ['limit_answers', 'limit_mails', 'affiliate_code_id', 'deleted_by', 'legal_entity_id', 'limit_users'],
                'integer'
            ],
            [['mailing_frequency', 'widget_display_frequency'], 'integer', 'min' => 0],
            [['unlimited', 'unlimited_mails', 'deleted'], 'safe'],
            [
                [
                    'auto_poll_enabled',
                    'mailing_limit',
                    'widget_display_limit',
                    'mailings_enabled',
                    'is_answer_limited',
                    'contacts_enabled',
                    'show_foquz_label',
                ],
                'boolean'
            ],
            [['alias'], 'unique', 'message' => 'Компания с указанным идентификатором уже добавлена в систему'],
            [['alias', 'name'], 'trim'],
            ['language_id', 'integer'],
            ['filesize_limit', 'integer', 'min' => 0],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'                       => 'ID',
            'created_at'               => 'Создан',
            'updated_at'               => 'Обновлен',
            'alias'                    => 'Домен',
            'short_alias'              => 'Короткий домен',
            'name'                     => 'Название',
            'widget_display_limit'     => 'Ограничение показа виджета для одного контакта',
            'widget_display_frequency' => 'Ограничение показа виджета для одного контакта',
            'mailings_enabled'         => 'Рассылки включены',
            'is_answer_limited'        => 'Получение новых ответов ограничено',
            'contacts_enabled'         => 'Раздел Контакты включен',
        ];
    }

    public function isTariffBase(): bool
    {
        return ($this->tariff->id === Tariff::TARIFF_BASE);
    }

    public function isContactsAccessEnabled(): bool
    {
        if (!$this->isTariffBase()) {
            return true;
        }
        return $this->contacts_enabled;
    }

    public function fields()
    {
        return parent::fields() + [
                'tariff'                   => function () {
                    if ($this->currentTariff && $this->currentTariff->cost) {
                        $this->tariff->cost = $this->currentTariff->cost;
                    }
                    return $this->tariff;
                },
                'answersTariffPeriodCount' => function ($model) {
                    return $model->answersTariffPeriodCount;
                },
                'answersLimit'             => function ($model) {
                    return $model->answersLimit;
                },
                'mailsLimit'               => function ($model) {
                    return $model->mailsLimit;
                },
                'userLimit'                => function () {
                    return $this->getUserLimit() ?: 'Без ограничений';
                },
                'answersLeft'              => function ($model) {
                    return $model->answersLimit - $model->answersTariffPeriodCount;
                },
                'mailsLeft'                => function ($model) {
                    //13 секунд даже на маленьких компаниях
                    //todo fixme надо оптимизировать
                    return 0;//$model->mailsLimit - $model->mailsTariffPeriodCount;
                },
                'tariff_from'              => function ($model) {
                    return $model->currentTariff->from ?? null;
                },
                'tariff_to'                => function ($model) {
                    return $model->currentTariff->to ?? null;
                },
                'who_brought'              => function ($model) {
                    return $model->affiliate_code_id && $model->usedCode && !$model->usedCode->company->deleted ? [
                        'id'   => $model->usedCode->company->id,
                        'name' => $model->usedCode->company->name
                    ] : null;
                },
                'legalEntity'              => function ($model) {
                    return $model->legalEntity;
                },
                'requisites'               => function ($model) {
                    return $model->requisites;
                },
                'auto_poll_enabled',
                'isAnswersLimitsOver'      => function () {
                    return $this->isAnswersLimitsOver();
                },
                'contacts_assess_enabled'  => function () {
                    return $this->isContactsAccessEnabled();
                }
            ];
    }

    /**
     * @return ActiveQuery
     */
    public function getCompanyStaff()
    {
        return $this->hasMany(CompanyStaff::className(), ['company_id' => 'id']);
    }

    /**
     * @return ActiveQuery | User[]
     */
    public function getCompanyUsers()
    {
        return $this->hasMany(User::className(), ['id' => 'user_id'])->via('companyStaff');
    }

    public function getRequestsProjects()
    {
        return $this->hasMany(Project::class, ['company_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getCompanyIikoAccesses()
    {
        return $this->hasMany(CompanyIikoAccess::className(), ['company_id' => 'id']);
    }

    public function getTariff()
    {
        return $this->hasOne(Tariff::class, ['id' => 'tariff_id'])->via('currentTariff');
    }

    public function getTariffQuery()
    {
        return $this->hasOne(Tariff::class, ['id' => 'tariff_id'])->viaTable(CompanyTariff::tableName(),
            ['company_id' => 'id'], function ($query) {
                $query->where(['<=', 'from', date('Y-m-d')])
                    ->andWhere(['>=', 'to', date('Y-m-d')])
                    ->orderBy(['company_tariffs.id' => SORT_DESC]);
            });
    }

    public function getCurrentTariff()
    {
        if (!CompanyTariff::findOne(['company_id' => $this->id])) {
            $companyTariff = new CompanyTariff([
                'company_id' => $this->id,
                'tariff_id'  => Tariff::DEFAULT,
                'from'       => date('Y-m-d', strtotime('first day of this month')),
                'to'         => date('Y-m-d', strtotime('last day of this month')),
            ]);
            $companyTariff->save();
        }

        return $this->hasOne(CompanyTariff::class, ['company_id' => 'id'])->orderBy(['id' => SORT_DESC]);
    }

    public function getDesignTemplates()
    {
        return $this->hasMany(FoquzPollDesignTemplate::className(), ['company_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getFoquzPolls()
    {
        return $this->hasMany(FoquzPoll::className(), ['company_id' => 'id']);
    }

    public function getContactAdditionalFields()
    {
        return $this->hasMany(ContactAdditionalField::className(), ['company_id' => 'id']);
    }

    public function getAnswersTariffPeriodCount()
    {
        //return 0;
        if ($this->currentTariff) {
            return (int)$this->currentTariff->have_answers;
            return (int)FoquzPollAnswer::find()->leftJoin('foquz_poll',
                'foquz_poll.id = foquz_poll_answer.foquz_poll_id')
                ->where([
                    'in',
                    'foquz_poll_answer.status',
                    [FoquzPollAnswer::STATUS_DONE, FoquzPollAnswer::STATUS_IN_PROGRESS]
                ])
                ->andWhere([
                    'between',
                    'foquz_poll_answer.updated_at',
                    $this->currentTariff->from . ' 00:00:00',
                    $this->currentTariff->to . ' 23:59:59'
                ])
                ->andWhere(['foquz_poll.company_id' => $this->id])
                ->count();
        } else {
            return 0;
        }
    }

    public function getMailsTariffPeriodCount()
    {
        if ($this->currentTariff) {
            $pollsSends = FoquzPollMailingListSend::find()
                ->leftJoin('channel', 'channel.id = foquz_poll_mailing_list_send.channel_id')
                ->leftJoin('foquz_poll', 'foquz_poll.id = channel.poll_id')
                ->where([
                    'company_id'   => $this->id,
                    'channel.name' => 'Email'
                ])
                ->andWhere([
                    'between',
                    'foquz_poll_mailing_list_send.sended',
                    $this->currentTariff->from . ' 00:00:00',
                    $this->currentTariff->to . ' 23:59:59'
                ])
                ->count();
            $mailingsSends = MailingListSend::find()
                ->leftJoin('mailings_channels', 'mailings_channels.id = mailing_list_send.channel_id')
                ->leftJoin('mailings', 'mailings_channels.mailing_id = mailings.id')
                ->where([
                    'company_id'             => $this->id,
                    'mailings_channels.name' => 'Email'
                ])
                ->andWhere([
                    'between',
                    'mailing_list_send.sended',
                    $this->currentTariff->from . ' 00:00:00',
                    $this->currentTariff->to . ' 23:59:59'
                ])
                ->count();
            return $pollsSends + $mailingsSends;
        } else {
            return 0;
        }
    }

    public function getEndpoints()
    {
        return $this->hasMany(ExternalResource::className(), ['company_id' => 'id']);
    }

    public function getAffiliateCode()
    {
        return $this->hasOne(CompanyAffiliateCode::className(), ['company_id' => 'id']);
    }

    public function getUsedCode()
    {
        return $this->hasOne(CompanyAffiliateCode::className(), ['id' => 'affiliate_code_id']);
    }

    public function getEndpointIds()
    {
        return $this->hasMany(ExternalResource::className(), ['company_id' => 'id'])->select('endpoint_id');
    }

    public function getAnswersLimit()
    {
        return $this->limit_answers && !$this->unlimited ? $this->limit_answers : ($this->tariff ? $this->tariff->answers : null);
    }

    public function getUserLimit()
    {
        return $this->limit_users ?? $this->tariff->users ?? 0;
    }

    public function getAnswerCost()
    {
        return $this->answer_cost ?: ($this->tariff ? $this->tariff->answer_cost : null);
    }

    public function getLegalEntity()
    {
        return $this->hasOne(LegalEntity::class, ['id' => 'legal_entity_id']);
    }

    public function getMailsLimit()
    {
        return $this->limit_mails ?: ($this->tariff ? $this->tariff->mails : null);
    }

    public function copyDesignTemplates($company_id)
    {
        foreach ($this->designTemplates as $tdTemplate) {
            $newTemplate = new FoquzPollDesignTemplate();
            $newTemplate->attributes = $tdTemplate->attributes;
            $newTemplate->company_id = $company_id;
            $newTemplate->save();
        }
    }

    private function createDefaultEmailSettings()
    {
        $emailSettings = new GlobalEmailSettings([
            'company_id'            => $this->id,
            'sender'                => $this->name,
            'sender_name'           => $this->name,
            'send_from_system_mail' => 1,
        ]);
        $emailSettings->save();
    }

    public function getRequisites()
    {
        return $this->hasOne(CompanyRequisites::class, ['company_id' => 'id']);
    }

    public function beforeSave($insert)
    {
        if ($insert) {
            if (!$this->legal_entity_id) {
                $this->legal_entity_id = LegalEntity::findOne(['is_default' => true])->id;
            }
        }
        return parent::beforeSave($insert);
    }

    public function getLanguage()
    {
        return $this->hasOne(Language::class, ['id' => 'language_id']);
    }

    public function getBonusesBalance()
    {
        $sql = 'SELECT
                    ((SELECT SUM(bonuses) FROM bonus_accounts WHERE company_id = c.id) -
                    (SELECT SUM(more_limit_answers) FROM company_tariffs WHERE company_id = c.id)) as balance
                FROM company c
                WHERE c.id =' . $this->id;

        $result = \Yii::$app->getDb()->createCommand($sql)->queryOne();

        return (int)$result['balance'];
    }

    public function isTariffAnswersLimitOver(): bool
    {
        return !$this->unlimited && $this->answersLimit && ($this->answersLimit <= $this->answersTariffPeriodCount);
    }

    public function isBonusesLimitOver(): bool
    {
        return true;
        return $this->getBonusesBalance() <= 0;
    }

    public function isAnswersLimitsOver(): bool
    {
        return $this->isTariffAnswersLimitOver() && $this->isBonusesLimitOver();
    }

    /**
     * Возвращает количество активных пользователей компании без учета респондентов
     * @return int|null
     */
    public function getActiveUsersCount(): ?int
    {
        return (int)CompanyStaff::find()
            ->leftJoin('user', 'user.id = company_staff.user_id')
            ->leftJoin('auth_assignment', 'auth_assignment.user_id = user.id')
            ->where(['company_staff.company_id' => $this->id, 'user.status' => User::STATUS_ACTIVE])
            ->andWhere(['NOT', ['auth_assignment.item_name' => 'foquz_respondent']])
            ->count('DISTINCT(user.id)');
    }

    public function updateTarriffsMoreLimitAnswers(int $count = 1): void
    {
        $companyTariff = CompanyTariff::find()
            ->where(['company_id' => $this->id])->orderBy(['id' => SORT_DESC])
            ->limit(1)->one();
        if (empty($companyTariff)) {
            $companyTariff = new CompanyTariff([
                'company_id' => $this->id,
                'tariff_id'  => Tariff::DEFAULT,
                'from'       => date('Y-m-d', strtotime('first day of this month')),
                'to'         => date('Y-m-d', strtotime('last day of this month')),
            ]);
            $companyTariff->save();
        }
        $tariff = $companyTariff;
        $this->populateRelation('currentTariff', $tariff);

        if ($tariff !== null && $this->isTariffAnswersLimitOver()) {
            $tariff->updateCounters(['more_limit_answers' => $count]);
        }
    }

    public function updatePollStat()
    {
        \Yii::$app->db->createCommand('
            UPDATE foquz_poll 
            SET answers_count = ( SELECT COUNT(*) FROM foquz_poll_answer WHERE foquz_poll_answer.foquz_poll_id = foquz_poll.id GROUP BY foquz_poll_answer.foquz_poll_id ) 
            WHERE foquz_poll.company_id = :company_id
        ')->bindValue(':company_id', $this->id)->execute();
    }

    /**
     * Возвращает короткую ссылку для компании
     * @return string
     */
    public function getShortLink(): string
    {
        if ($this->short_alias) {
            return \Yii::$app->params['protocol'] . '://' . $this->short_alias . '/q';
        }
        return \Yii::$app->params['short_link'];
    }

    /**
     * Отправка заявки на подключение раздела "Контакты"
     * @param IdentityInterface $user
     * @param Company $company
     * @param array $post
     * @return void
     * @throws \yii\base\InvalidConfigException
     * @throws \yii\db\Exception
     */
    public function sendContactsAccessRequest(IdentityInterface $user, Company $company, array $post): void
    {
        $userName = (empty($post['name'])) ? $user->username : Html::encode($post['name']);
        $userPhone = (empty($post['phone'])) ? $user->phone : FoquzContact::preformatPhone($post['phone']);

        $model = DynamicModel::validateData(['phone' => $userPhone, 'name' => $userName], [
            [['phone'], 'required'],
            [['name',], 'string', 'max' => 255],
            [['phone'], 'string', 'max' => 12],
        ]);
        if ($model->hasErrors()) {
            throw new \InvalidArgumentException($model->errors[key($model->errors)][0]);
        }

        if (!empty(\Yii::$app->queue_mailings_out)) {
            $service = new NotificationsSystemService(\Yii::$app->queue_mailings_out);
            $service->sendToSalesClaimContacts($company, $userName, $userPhone);
        }


    }

    /**
     * Получает алиас компании из кэша или базы данных
     * @param int $companyID
     * @return string|null
     */
    public static function getCompanyAlias(int $companyID): ?string
    {
        return Yii::$app->cache->getOrSet(
            'company_alias_' . $companyID,
            function () use ($companyID) {
                return self::findOne($companyID)?->alias;
            },
            60*60*24 // 24 hour cache
        );
    }
}
