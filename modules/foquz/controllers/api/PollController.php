<?php


namespace app\modules\foquz\controllers\api;

use app\helpers\DateTimeHelper;
use app\models\DictionaryElement;
use app\models\User;
use app\modules\foquz\models\AutoPollRequest;
use app\modules\foquz\models\EditorFolder;
use app\modules\foquz\models\FoquzAutoPollForm;
use app\modules\foquz\models\FoquzCompanyTag;
use app\modules\foquz\models\FoquzCompanyTagSearch;
use app\modules\foquz\models\FoquzContactTag;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollDesign;
use app\modules\foquz\models\FoquzPollDisplaySetting;
use app\modules\foquz\models\FoquzPollLang;
use app\modules\foquz\models\FoquzPollMailingCondition;
use app\modules\foquz\models\FoquzPollMailingList;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzPollQuestionsLogic;
use app\modules\foquz\models\FoquzPollQuestionViewLogic;
use app\modules\foquz\models\FoquzPollStatFilterSettings;
use app\modules\foquz\models\FoquzPollStatsLink;
use app\modules\foquz\models\FoquzPollStatWidgetKey;
use app\modules\foquz\models\FoquzPollWidget;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetailLang;
use app\modules\foquz\models\FoquzQuestionFileLang;
use app\modules\foquz\models\FoquzQuestionFirstClick;
use app\modules\foquz\models\FoquzQuestionFirstClickArea;
use app\modules\foquz\models\FoquzQuestionFirstClickAreaLang;
use app\modules\foquz\models\FoquzQuestionFirstClickLang;
use app\modules\foquz\models\FoquzQuestionFormFieldLang;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSetting;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSettingLang;
use app\modules\foquz\models\FoquzQuestionLang;
use app\modules\foquz\models\FoquzQuestionMatrixElement;
use app\modules\foquz\models\FoquzQuestionMatrixElementLang;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariant;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariantLang;
use app\modules\foquz\models\FoquzQuestionCardSortingCategory;
use app\modules\foquz\models\FoquzQuestionCardSortingCategoryLang;
use app\modules\foquz\models\MailingConditions;
use app\modules\foquz\models\PollLang;
use app\modules\foquz\models\polls\forms\PollJsonForm;
use app\modules\foquz\models\polls\PollJsonParser;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotes;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\models\ScoresInterpretationRange;
use app\modules\foquz\models\SettingTableFilters;
use app\modules\foquz\models\SettingTables;
use app\modules\foquz\queue\AnswerStatusJob;
use app\modules\foquz\services\Auth;
use app\modules\foquz\services\quotes\QuotaCache;
use app\modules\foquz\services\quotes\QuoteService;
use app\modules\foquz\services\translate\TranslateService;
use app\modules\foquz\services\AnswerTagService;
use app\modules\foquz\services\ValidateException;
use Yii;
use yii\base\InvalidConfigException;
use yii\base\InvalidRouteException;
use yii\caching\TagDependency;
use yii\data\Sort;
use yii\db\ActiveQuery;
use yii\db\Exception;
use yii\db\Expression;
use yii\db\Query;
use yii\filters\AccessControl;
use yii\filters\auth\QueryParamAuth;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\ForbiddenHttpException;
use yii\web\NotFoundHttpException;
use yii\web\ServerErrorHttpException;
use yii\web\UploadedFile;

/**
 * @OA\Schema(
 *     schema="answerTagAddRequest",
 *     type="array",
 *     @OA\Items(
 *         @OA\Property(
 *             property="answers",
 *             description="ID анкет(ы)",
 *             type="array",
 *             @OA\Items(
 *                 type="integer",
 *             ),
 *         ),
 *         @OA\Property(
 *             property="tags[][name]",
 *             description="Массив названий тегов",
 *             type="array",
 *             example="филиал",
 *             @OA\Items(
 *                type="string",
 *             ),
 *         ),
 *     ),
 *  ),
 * @OA\Schema(
 *     schema="answerTagRemoveRequest",
 *     type="array",
 *     @OA\Items(
 *         @OA\Property(
 *             property="answers",
 *             description="ID анкет(ы)",
 *             type="array",
 *             @OA\Items(
 *                 type="integer",
 *             ),
 *         ),
 *         @OA\Property(
 *             property="tags[]",
 *             description="Массив ID тегов",
 *             type="array",
 *             example=123,
 *             @OA\Items(
 *                type="integer",
 *             ),
 *         ),
 *     ),
 *  ),
 */
class PollController extends ApiController
{
    use Auth;
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['authenticator'] = [
            'class' => QueryParamAuth::class,
            'except' => ['get-translate', 'tags', 'folders', 'mailings-list', 'widget-poll-info'],
        ];
        $behaviors['verbs'] = [
            'class' => VerbFilter::className(),
            'actions' => [
                'mailings-list' => ['GET'],
                'mailing-conditions' => ['GET'],
                'set-mailing-conditions' => ['POST'],
                'index' => ['GET'],
                'view' => ['GET'],
                'folders' => ['GET'],
                'first-rating-question' => ['GET'],
                'clear-stats' => ['POST'],
                'mass-move' => ['POST'],
                'mass-archive' => ['POST'],
                'mass-delete' => ['POST'],
                'save-logic' => ['POST'],
                'save-display-settings' => ['POST'],
                'points-distribution' => ['GET'],
                'tags' => ['GET'],
                'delete-questions' => ['DELETE'],
                'mailing' => ['GET'],
                'save-settings' => ['POST'],
                'save-scores-interpretation-ranges' => ['POST'],
                'scores-interpretation-ranges-update-positions' => ['POST'],
                'delete-scores-interpretation-range' => ['DELETE'],
                'create-from-json' => ['POST'],
                'save-public-status' => ['POST'],
                'stat-widget-link' => ['GET'],
                'save-stat-filter-settings' => ['POST'],
                'reset-stat-filter-settings' => ['POST'],
                'stat-filter-questions' => ['GET'],
                'create-widget' => ['POST'],
                'update-widget' => ['PUT'],
                'delete-widget' => ['DELETE'],
                'widgets' => ['GET'],
                'new-widgets-count' => ['GET'],
                'add-tags' => ['POST'],
                'remove-tags' => ['POST'],
            ]
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'actions' => ['get-translate', 'tags', 'folders', 'mailings-list', 'widget-poll-info'],
                    'allow' => true,
                ],
                [
                    'actions' => ['save-stat-filter-settings', 'reset-stat-filter-settings', 'stat-filter-questions'],
                    'roles' => ['@'],
                    'allow' => true,
                ],
                [
                    'actions' => [
                        'set-mailing-conditions', 'clear-stats', 'publish', 'mass-move', 'mass-archive', 'mass-delete',
                        'save-view-logic', 'save-logic', 'clear-logic', 'save-display-settings',
                        'update-stats-link', 'copy-questions', 'delete-questions', 'change-active', 'save-public-status',
                        'save-settings', 'translate', 'save-scores-interpretation-ranges',
                        'scores-interpretation-ranges-update-positions', 'delete-scores-interpretation-range', 'send-request',
                        'create-from-json', 'mailing-settings', 'create-widget', 'update-widget', 'delete-widget', 'widgets',
                        'new-widgets-count',
                    ],
                    'allow' => true,
                    'roles' => ['foquz_admin', 'editor'],
                ],
                [
                    'actions' => [
                        'add-tags', 'remove-tags',
                    ],
                    'allow' => true,
                    'roles' => ['foquz_admin'],
                ],
                [
                    'actions' => [
                        'index', 'view', 'design', 'first-rating-question', 'list', 'widget-poll-info',
                        'mailing-conditions', 'points-distribution', 'get-questions', 'tags', 'interpretate-scores',
                        'mailing', 'poll-lang', 'generate-stats-link', 'stat-widget-link'

                    ],
                    'allow' => true,
                    'roles' => ['foquz_admin', 'editor', 'filial_employee', 'foquz_watcher'],
                ],
            ],
        ];
        return $behaviors;
    }

    public function actionIndex()
    {
        $company_id = Yii::$app->user->identity->company->id;

        if (Yii::$app->user->identity->superadmin) {
            $company_id = Yii::$app->request->get('companyId');
        }

        $itemsQuery = FoquzPoll::find()->select('foquz_poll.id, foquz_poll.name, foquz_poll.is_auto, foquz_poll.key, foquz_poll.trigger')->where([
            'foquz_poll.company_id' => $company_id,
            'foquz_poll.status' => FoquzPoll::STATUS_NEW,
            'is_folder' => false,
            'is_tmp' => false,
            'foquz_poll.deleted' => 0,
        ])->orderBy('foquz_poll.name');

        $user = Yii::$app->user->identity;
        if($user && $user->isFilialEmployee() && $user->getUserFilials()->count() > 0) {
            $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
            $itemsQuery
                ->leftJoin('foquz_poll_answer FPAF', 'FPAF.foquz_poll_id = foquz_poll.id')
                ->leftJoin('orders', 'orders.id = FPAF.order_id')
                ->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, FPAF.answer_filial_id)', $filials])
                ->groupBy('foquz_poll.id')
                ->having(new Expression("COUNT(FPAF.id) > 0"));
        }
        if ($user->isEditor() || $user->isWatcher()) {
            $editorFoldersIds = FoquzPoll::getEditorFolderIdsAll();
            $itemsQuery->andWhere(['foquz_poll.folder_id'=>$editorFoldersIds["folders"]]);
        }

        if(Yii::$app->request->get('isAuto') !== null) {
            $itemsQuery->andWhere(['is_auto' => Yii::$app->request->get('isAuto')]);
        }

        return $this->response(200, ['items' => $itemsQuery->asArray()->all()]);
    }

    public function actionView($id)
    {
        $company_id = Yii::$app->user->identity->company->id;

        $model = FoquzPoll::findOne(['id' => $id, 'company_id' => $company_id]);

        if ($model === null) {
            return $this->response(404, ['errors' => ['pollId' => 'Опрос не найден']]);
        }

        return [
            'success' => true,
            'model' => $model
        ];
    }

    /**
     * @OA\Get (
     *      path="/foquz/api/poll/design",
     *      tags={"Опрос"},
     *      summary="Дизайн опроса. Получить настройки.",
     *      security={ {"api_key": {}} },
     *      @OA\Parameter(
     *           name="access-token",
     *           in="query",
     *           description="Токен доступа",
     *           required=true,
     *           example="vZGkGCGTTDkxTlv",
     *           @OA\Schema(type="string")
     *       ),
     *      @OA\Parameter(
     *           name="id",
     *           in="query",
     *           description="ID опроса",
     *           required=true,
     *           example=42275,
     *           @OA\Schema(type="integer"),
     *       ),
     *     @OA\Response(
     *          response=200,
     *          description="Success",
     *          @OA\JsonContent(
     *             ref="#/components/schemas/pollDesignTemplate",
     *           ),
     *     ),
     * )
     * @throws NotFoundHttpException
     */
    public function actionDesign($id): array
    {
        $model = FoquzPoll::findOne($id);
        if (!$model) {
            throw new NotFoundHttpException('Poll not found');
        }

        return $model->design ? $model->design->toArray() : FoquzPollDesign::createDesignFromStandartTemplate($id);
    }

    public function actionFirstRatingQuestion($id)
    {
        $company_id = Yii::$app->user->identity->company->id;

        $model = FoquzPoll::findOne(['id' => $id, 'company_id' => $company_id]);


        if ($model === null) {
            return $this->response(404, ['errors' => ['pollId' => 'Опрос не найден']]);
        }

       $firstRatingQuestion = null;
        if ($model) {
            $firstRatingQuestion = $model->getFirstRatingQuestion();
        }

        if($firstRatingQuestion) {
            $npsRatingSetting = $firstRatingQuestion->npsRatingSetting;

            $result = [
                'questionName' => $firstRatingQuestion->description ? $firstRatingQuestion->description : $firstRatingQuestion->name,
                'questionType' => $firstRatingQuestion->main_question_type,
                'starsCount' => $firstRatingQuestion->starRatingOptions ? $firstRatingQuestion->starRatingOptions->count : null,
                'smilesCount' => $firstRatingQuestion->smiles_count,
                'smileType' => $firstRatingQuestion->smile_type,
                //ollLink' => $link,
                'assessmentType' => 0,
                'npsRating' => $npsRatingSetting ? [
                    'design' => $npsRatingSetting->design,
                    'end_point_color' => $npsRatingSetting->end_point_color,
                    'start_point_color' => $npsRatingSetting->start_point_color,
                ] : null,
            ];

            return $result;
        }

        return $this->response(404, ['errors' => ['question' => 'Первый вопрос не рейтинг']]);
    }

    public function actionMailingsList($pollId)
    {
        $company_id = $this->authByHashOrToken();
        if (!$pollId && $linkKey = Yii::$app->request->get('link') ?: Yii::$app->request->get('link-key')) {
            $pollId = FoquzPollStatsLink::find()
                ->select('foquz_poll_id')
                ->where(['right_level' => FoquzPollStatsLink::RIGHT_READ])
                ->andWhere(['like', 'link', '/stats/' . $linkKey])
                ->scalar();
        }
        $model = FoquzPoll::findOne(['id' => $pollId, 'deleted' => 0, 'company_id' => $company_id]);
        if(!$model) {
            throw new NotFoundHttpException();
        }

        return $model->getMailings()
            ->select('id, name')
            ->where(['in','status',[FoquzPollMailingList::STATUS_STARTED, FoquzPollMailingList::STATUS_STOPPED]])
            ->andWhere(['deleted' => 0])
            ->orderBy(['name' => SORT_ASC])
            ->asArray()->all();
    }

    public function actionList($onlyManual = false)
    {
        $company_id = Yii::$app->user->identity->company->id;

        $itemsQuery = FoquzPoll::find()->select('id, name')->where([
            'company_id' => $company_id,
            'status' => FoquzPoll::STATUS_NEW,
            'is_folder' => false,
            'is_tmp' => false,
            'deleted' => 0,
        ])->orderBy('name');
        if($onlyManual) {
            $itemsQuery->andWhere(['is_auto' => false]);
        }
        if (Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher()) {
            $folders = EditorFolder::find()
                ->andWhere(['user_id' => Yii::$app->user->identity->id])
                ->select('folder_id')
                ->column();
            $itemsQuery->andWhere(['folder_id' => $folders]);
        }

        $items = $itemsQuery->asArray()->all();

        return $this->response(200, ['items' => $items]);
    }

    public function actionWidgetPollInfo($id = null, $key = null, $new = false)
    {
        if ($id) {
            $company_id = $this->authByHashOrToken();
            $poll = FoquzPoll::findOne(['id' => $id, 'company_id' => $company_id]);
        } elseif ($key) {
            /** @var FoquzPollMailingListSend $sending */
            $sending = FoquzPollMailingListSend::find()->where(['key' => $key])->one();
            if (!$sending) {
                return $this->response(404, ['errors' => ['key' => 'Неверный ключ анкеты']]);
            }
            $poll = $sending->answer->foquzPoll;
        } else {
            return $this->response(400, ['errors' => ['id' => 'Не передан ID опроса или ключ анкеты']]);
        }

        if(!$poll) {
            $this->response->statusCode = 404;
            return ['errors' => ['Опрос не найден']];
        }

        return $poll->widgetPollInfo($new);
    }

    public function actionSendRequest()
    {
        $company_id = Yii::$app->user->identity->company->id;
        $post = Yii::$app->request->post();
        $request = new AutoPollRequest();

        $request->attributes = $post;
        $request->company_id = $company_id;

        if($request->save()) {
            return $this->response(201, []);
        }
        return $this->response(400, ['errors' => $request->errors]);
    }


    /**
     * @return array
     */
    public function actionMailingConditions($pollId)
    {
        $companyId = Yii::$app->user->identity->company->id;
        $poll = FoquzPoll::findOne($pollId);

        if(!$poll) {
            return $this->response(404, ['errors' => ['pollId' => 'Опрос не найден']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        return $this->response(200, ['items' => $poll->mailingConditions]);
    }

    public function actionSetMailingConditions($pollId)
    {
        $companyId = Yii::$app->user->identity->company->id;
        $poll = FoquzPoll::findOne($pollId);

        if(!$poll) {
            return $this->response(404, ['errors' => ['pollId' => 'Опрос не найден']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        FoquzPollMailingCondition::deleteAll(['foquz_poll_id' => $poll->id]);

        $post = Yii::$app->request->post();
        if(isset($post['conditions'])) {
            foreach($post['conditions'] as $condition) {
                if(!isset($condition['id']) || MailingConditions::findOne(['id' => $condition['id']]) === null) {
                    return $this->response(400, [
                        'errors' => ['condition' => 'Не передан ID условия или передан несуществующий']
                    ]);
                }
                $model = new FoquzPollMailingCondition();
                $model->foquz_poll_id = $poll->id;
                $model->mailing_condition_id = $condition['id'];
                try {
                    if($condition['id'] === MailingConditions::FAVORITE_DISH) {
                        $value = [
                            'categories' => $condition['categories'] ?? [],
                            'dishes' => $condition['dishes'] ?? [],
                            'minPrice' => $condition['minPrice'] ?? 0,
                        ];
                        $model->value =  json_encode($value, JSON_UNESCAPED_UNICODE);
                    } else {
                        $model->value = $model->collectValueByType($condition['id'], $condition);
                    }
                } catch(\Exception $e) {
                    return $this->response(400, [
                        'errors' => ['value' => $e->getMessage()]
                    ]);
                }
                $model->save();
            }
        }

        $poll->refresh();
        return $this->response(201, ['items' => $poll->mailingConditions]);
    }

    public function actionFolders($companyId = null)
    {
        if ($companyId) {
            $company_id = $companyId;
        } else {
            $company_id = $this->authByHashOrToken();
        }
        $folders = FoquzPoll::find()->where([
            'is_tmp' => 0,
            'is_folder' => 1,
            'company_id' => $company_id,
            'deleted' => false,
        ])->orderBy('name');
        if (!Yii::$app->user->isGuest && (Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher())) {
            $editorFolders = FoquzPoll::getEditorFolderIds();
            $folders->andWhere(['id' => $editorFolders['all']]);
        }
        $items = [];
        foreach($folders->all() as $folder) {
            $items[] = [
                'id' => $folder->id,
                'name' => $folder->name,
                'folder_id' => $folder->folder_id
            ];
        }
        return $this->response(201, ['items' => $items]);
    }

    public function actionClearStats($id)
    {
        $companyId = Yii::$app->user->identity->company->id;
        $poll = FoquzPoll::findOne($id);

        if(!$poll) {
            return $this->response(404, ['errors' => ['pollId' => 'Опрос не найден']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        if($poll->is_published) {
            return $this->response(403, ['errors' => ['pollId' => 'Нельзя очистить опубликованный опрос']]);
        }

        if (isset(Yii::$app->rabbit_queue)) {
            $IDs = FoquzPollAnswer::find()
                ->select('id')
                ->where(['foquz_poll_id' => $id])
                ->column();

            /** @var \yii\queue\amqp_interop\Queue $queue */
            $queue = Yii::$app->rabbit_queue;
            /** @var FoquzPollMailingListSend[] $answersFromWidget */
            $answersFromWidget = FoquzPollMailingListSend::find()
                ->where(['answer_id' => $IDs])
                ->andWhere(['NOT', ['widget_id' => null]])
                ->all();

            foreach ($answersFromWidget as $answer) {
                $queue->push(new AnswerStatusJob([
                    'id' => $answer->id,
                    'status' => 'delete',
                    'key' => $answer->key,
                    'widget_id' => $answer->widget_id,
                    'updated_at' => $answer->sended,
                ]));
            }
        }

        FoquzPollAnswer::deleteAll(['foquz_poll_id' => $id]);
        $poll->answers_count = 0;
        $poll->sent_answers_count = 0;
        $poll->opened_answers_count = 0;
        $poll->in_progress_answers_count = 0;
        $poll->filled_answers_count = 0;
        $poll->processing_new_answers_count = 0;
        $poll->processing_inprocess_answers_count = 0;
        $poll->processing_work_answers_count = 0;
        $poll->processing_delayed_answers_count = 0;
        $poll->processing_done_answers_count = 0;
        $poll->save();

        /** Обнуление счетчиков в квотах */
        FoquzPollLinkQuotes::updateAll(['answers_count' => 0], ['poll_id' => $id]);
        foreach ($poll->quotes as $link) {
            FoquzPollAnswerQuotes::updateAll(['answers_count' => 0], ['link_quote_id' => $link->id]);
            QuotaCache::resetCache($link->id);
        }

        $poll->clearStatCache();

        Yii::$app->cache->delete(['main_answers_statuses', 'company_id' => Yii::$app->user->identity->company->id]);

        if ($poll->isAnswerLimit !== (bool) $poll->is_answer_limited) {
            $poll->is_answer_limited = $poll->isAnswerLimit;
            $poll->save();
        }

        return $this->response(200, []);
    }

    public function actionPublish($id)
    {
        $poll = FoquzPoll::findOne($id);
        if(!$poll) {
            return $this->response(404, ['error' => 'Опрос не найден']);
        }
        TagDependency::invalidate(\Yii::$app->cache, 'quiz-poll-' . $poll->id);

        if (
            $poll->company_id != Yii::$app->user->identity->company->id ||
            (!Yii::$app->user->identity->isAdmin() && !Yii::$app->user->identity->isEditor() && !Yii::$app->user->identity->isWatcher())
        ) {
            return $this->response(403, []);
        }
        if($poll->is_tmp) {
            return $this->response(400, ['error' => 'Временный опрос не может быть опубликован']);
        }
        $poll->is_published = true;
        $poll->save();
        return $this->response(200, []);
    }

    public function actionMassMove()
    {
        $post = Yii::$app->request->post();
        if(isset($post['ids'])) {
            $polls = FoquzPoll::find()
                ->where(['in', 'id', $post['ids']])
                ->all();
        } else {
            $filter = $post['filter'];
            $polls = $this->getFilteredPolls($filter);
        }

        $to = $post['to'] == 0 ? null : $post['to'];

        foreach($polls as $poll) {
            if($poll->is_folder) {
                FoquzPoll::recursiveMove($poll->folders, $to);
            } else {
                $poll->folder_id = $to;
                $poll->save();
            }
        }
    }

    public function actionMassArchive()
    {
        $post = Yii::$app->request->post();

        if(isset($post['ids'])) {
            $polls = FoquzPoll::find()
                ->where(['in', 'id', $post['ids']])
                ->orWhere(['in', 'folder_id', $post['ids']])
                ->all();
        } else {
            $filter = $post['filter'];
            $polls = $this->getFilteredPolls($filter);
        }

        foreach($polls as $poll) {
            if($poll->is_folder) {
                FoquzPoll::recursiveArchive($poll->folders);
            } else {
                $poll->status = FoquzPoll::STATUS_ARCHIVE;
                $poll->save();
            }
        }
    }

    public function actionMassDelete()
    {
        $post = Yii::$app->request->post();

        if(isset($post['ids'])) {
            $polls = FoquzPoll::find()
                ->where(['in', 'id', $post['ids']])
                ->orWhere(['in', 'folder_id', $post['ids']])
                ->all();
        } else {
            $filter = $post['filter'];
            $polls = $this->getFilteredPolls($filter);
        }

        foreach($polls as $poll) {
            if($poll->is_folder) {
                FoquzPoll::recursiveDelete($poll->folders);
            } else {
                $poll->deleted = 1;
                $poll->deleted_at = date('Y-m-d H:i:s');
                $poll->deleted_by = Yii::$app->user->id;
                $poll->save();
            }
        }
    }

    private function getFilteredPolls($filter)
    {
        $polls = FoquzPoll::find()
            ->select('*')
            ->addSelect(new Expression('(SELECT COUNT(id) FROM foquz_poll_answer WHERE foquz_poll_id = foquz_poll.id AND status IN ("'.FoquzPollAnswer::STATUS_IN_PROGRESS.'", "'.FoquzPollAnswer::STATUS_DONE.'")) as countAnswers'))
            ->where(['company_id' => Yii::$app->user->identity->company->id, 'is_tmp' => 0]);

        switch($filter['id']) {
            case 'favorite':
                $polls->leftJoin('foquz_poll_favorite', 'foquz_poll_favorite.foquz_poll_id = foquz_poll.id')
                    ->andWhere(['foquz_poll_favorite.user_id' => Yii::$app->user->id]);
                break;
            case 'goalPolls':
                $polls
                    ->addSelect(new Expression('goals_count as goalsCount'))
                    ->andWhere(['is_folder' => false, 'status' => FoquzPoll::STATUS_NEW])
                    ->andWhere(['>', 'goals_count', 0])
                    ->andHaving(new Expression('countAnswers >= goalsCount'));
                break;
            case 'lastPolls':
                $polls->limit(12);
                break;
            case 'folders':
                $polls->andWhere(['is_folder' => 1]);
                break;
            case 'all':
                break;
            case 'archive':
                $polls->andWhere(['status' => FoquzPoll::STATUS_ARCHIVE]);
                break;
            default:
                $polls->andWhere(['folder_id' => $filter['id']]);

        }

        if(isset($filter['withAnswer']) && $filter['withAnswer'] == 1) {
            $polls->andHaving(new Expression('countAnswers > 0'));
        }
        if(isset($filter['folder'])) {
            $polls->andWhere(['folder_id' => $filter['folder']]);
        }
        if(isset($filter['author'])) {
            $polls->andWhere(['created_by' => $filter['author']]);
        }

        return $polls->all();
    }

    /**
     * Сохранение логики для отображения вопроса
     *
     * @param int $id
     * @return FoquzPollQuestionViewLogic[]
     * @throws ForbiddenHttpException
     * @throws InvalidConfigException
     * @throws NotFoundHttpException|Exception
     */
    public function actionSaveViewLogic(int $id)
    {
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new NotFoundHttpException('Вопрос не найден');
        }
        $companyId = Yii::$app->user->identity->company->id;
        if ($question->poll->company_id !== $companyId) {
            throw new ForbiddenHttpException('Опрос не принадлежит вашей компании');
        }

        FoquzPollQuestionViewLogic::deleteAll(['question_id' => $id]);
        $params = Yii::$app->request->getBodyParams();
        foreach ($params as $conditions) {
            $viewLogic = new FoquzPollQuestionViewLogic();
            $viewLogic->setAttribute('question_id', $id);
            $viewLogic->setAttributes($conditions);
            $viewLogic->checkSkippedBeforeSave();

            if (!$viewLogic->save()) {
                throw new Exception('Не удалось сохранить логику', ['errors' => $viewLogic->errors], 500);
            }
        }

        TagDependency::invalidate(\Yii::$app->cache, 'quiz-poll-' . $question->poll->id);

        $this->response(201, [
            'items' => FoquzPollQuestionViewLogic::findAll(['question_id' => $id])
        ]);

        return FoquzPollQuestionViewLogic::findAll(['question_id' => $id]);
    }

    /**
     * Сохранение логики для вопросов в опросе (работает на Оценку и Варианты ответов)
     * @param $id
     * @return array
     * @throws Exception
     */
    public function actionSaveLogic($id)
    {
        $companyId = Yii::$app->user->identity->company->id;
        $poll = FoquzPoll::findOne($id);

        if(!$poll) {
            return $this->response(404, ['errors' => ['pollId' => 'Опрос не найден']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        $post = Yii::$app->request->post('logic');
        $pollQuestionIds = ArrayHelper::getColumn($poll->foquzQuestions, 'id');

        FoquzPollQuestionsLogic::deleteAll(['question_id' => $pollQuestionIds]);
        TagDependency::invalidate(Yii::$app->cache, 'quiz-poll-' . $poll->id);


        if($post) {
            foreach($post as $questionId => $logics)
            {
                $position = 1;
                foreach($logics as $logic) {
                    $logicModel = new FoquzPollQuestionsLogic();
                    $logicModel->question_id = $questionId;
                    $logicModel->behavior = $logic['behavior'];
                    $logicModel->variants = isset($logic['variants']) ? json_encode(array_map('intval', $logic['variants'])) : null;
                    $logicModel->jump = $logic['jump'];
                    $logicModel->jump_question_id = $logic['jump_question_id'] ?? null;
                    $logicModel->jump_display_page_id = $logic['jump_display_page_id'] ?? null;
                    $logicModel->position = $position;
                    $logicModel->save();
                    $position++;
                }
            }
        }

        $poll->refresh();

//        if($poll->design->show_process == 2 and $poll->hasLogic() ) {
//            $poll->design->show_process = 1;
//            $poll->design->save();
//        }

        return $this->response(201, [
            'items' => FoquzPollQuestionsLogic::find()->where(['in', 'question_id', $pollQuestionIds])->all()
        ]);
    }

    /**
     * @param int $id id опроса
     * @return bool
     * @throws Exception
     * @throws NotFoundHttpException
     */
    public function actionClearLogic($id): bool
    {
        $poll = FoquzPoll::findOne($id);
        if (!$poll) {
            throw new NotFoundHttpException('Опрос не найден');
        }
        TagDependency::invalidate(\Yii::$app->cache, 'quiz-poll-' . $poll->id);

        foreach ($poll->foquzQuestions as $question) {
            $transaction = Yii::$app->getDb()->beginTransaction();
            try {
                $logics = FoquzPollQuestionsLogic::find()
                    ->where(['question_id' => $question->id])
                    ->select('question_id')
                    ->column();
                if ($logics) {
                    FoquzPollQuestionsLogic::deleteAll(['question_id' => $logics]);
                }

                $logics = FoquzPollQuestionViewLogic::find()
                    ->where(['question_id' => $question->id])
                    ->select('question_id')
                    ->column();
                if ($logics) {
                    FoquzPollQuestionViewLogic::deleteAll(['question_id' => $logics]);
                }

                $transaction->commit();
            } catch (Throwable $exception) {
                $transaction->rollBack();
                throw $exception;
            }
        }

        return true;
    }

    public function actionSaveDisplaySettings($id)
    {
        $companyId = Yii::$app->user->identity->company->id;
        $poll = FoquzPoll::findOne($id);

        if(!$poll) {
            return $this->response(404, ['errors' => ['id' => 'Опрос не найден']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['id' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        $displaySetting = $poll->displaySetting ?? new FoquzPollDisplaySetting(['poll_id' => $poll->id]);
        $displaySetting->load(Yii::$app->request->post(), '');
        $displaySetting->save();
        TagDependency::invalidate(Yii::$app->cache, 'quiz-poll-' . $poll->id);
        $poll->refresh();

        return $this->response(200, [
            'poll' => $poll,
        ]);
    }

    public function actionPointsDistribution($id, $userId = null)
    {
        $poll = FoquzPoll::findOne($id);
        if(!$poll || !$poll->point_system) {
            return $this->response(400, ['errors' => [
                'id' => 'Не удалось получить распределение баллов для опроса'
            ]]);
        }
        $noRanges = Yii::$app->request->get('noRanges');
        $countRanges = Yii::$app->request->get('ranges');

        if (!$userId) {
            $userId = Yii::$app->user->id;
        }
        $user = $userId ? User::findOne($userId) : null;

        if($user && $user->isFilialEmployee() && $user->getUserFilials()->count() > 0) {
            $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
            $query = (new Query())
                ->select(new Expression('points, COUNT(foquz_poll_answer.id) as count'))
                ->from('foquz_poll_answer')
                ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_poll_answer.foquz_poll_id')
                ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
                ->where(['foquz_poll_id' => $id])
                ->andWhere(['is not', 'points', null])
                ->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)', $filials])
                ->groupBy('points')
                ->all();
            $countAnswers = (new Query())
                ->select(new Expression('COUNT(foquz_poll_answer.id)'))
                ->from('foquz_poll_answer')
                ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_poll_answer.foquz_poll_id')
                ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
                ->where(['foquz_poll_id' => $id])
                ->andWhere(['is not', 'points', null])
                ->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)', $filials])
                ->scalar();
        } else {
            $query = (new Query())
                ->select(new Expression('points, COUNT(id) as count'))
                ->from('foquz_poll_answer')
                ->where(['foquz_poll_id' => $id])
                ->andWhere(['is not', 'points', null])
                ->groupBy('points')
                ->all();
            $countAnswers = (new Query())
                ->select(new Expression('COUNT(id)'))
                ->from('foquz_poll_answer')
                ->where(['foquz_poll_id' => $id])
                ->andWhere(['is not', 'points', null])
                ->scalar();
        }

        $points = [];
        foreach($query as $data) {
            $points[$data['points']] = $data['count'];
        }

        if($noRanges) {
            for($i = 0; $i <= $poll->max_points; $i++) {
                $count = $points[$i] ?? 0;
                $items[$i] = [
                    'value' => $i,
                    'count' => (int)$count,
                    'percent' => $count / $countAnswers * 100
                ];
            }
            return $this->response(200, [
                'items' => $items
            ]);
        } else {
            $start = $poll->minPoints;
            if (!$poll->max_points) {
                $poll->max_points = $poll->calculatePoints();
            }
            $perRange = round(($poll->max_points - $poll->minPoints) / $countRanges);
            $ranges = [];
            for($i = 0; $i < $countRanges; $i++) {
                if($i > 0 && $poll->max_points <= $perRange || $perRange == 0 && $i > 0)
                    break;
                $startRange = ($i > 0 ? ($start+1) : $start);
                $endRange = ($i+1 == $countRanges || $perRange == 0 ? $poll->max_points : $start+$perRange);
                if($startRange > $poll->max_points)
                    break;

                if($user && $user->isFilialEmployee() && $user->getUserFilials()->count() > 0) {

                    $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
                    $count = (new Query())
                        ->select(new Expression('COUNT(foquz_poll_answer.id)'))
                        ->from('foquz_poll_answer')
                        ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_poll_answer.foquz_poll_id')
                        ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
                        ->where(['foquz_poll_id' => $id])
                        ->andWhere(['is not', 'points', null])
                        ->andWhere(['between', 'points', $startRange, $endRange])
                        ->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)', $filials])
                        ->scalar();
                } else {
                    $count = (new Query())
                        ->select(new Expression('COUNT(id)'))
                        ->from('foquz_poll_answer')
                        ->where(['foquz_poll_id' => $id])
                        ->andWhere(['is not', 'points', null])
                        ->andWhere(['between', 'points', $startRange, $endRange])
                        ->scalar();
                }

                $ranges[] = [
                    'range' => [
                        $startRange,
                        $endRange
                    ],
                    'count' => $count,
                    'percents' => $countAnswers > 0 ? $count / $countAnswers * 100 : 0
                ];
                $start+= $perRange;
            }
            return $this->response(200, [
                'items' => $ranges
            ]);
        }
    }

    public function actionGenerateStatsLink($id)
    {
        $link = FoquzPollStatsLink::findOne([
            'foquz_poll_id' => $id,
            'created_by' => Yii::$app->user->id,
        ]);
        if($link === null) {
            $link = new FoquzPollStatsLink();
            $link->foquz_poll_id = $id;
            $link->right_level = FoquzPollStatsLink::RIGHT_BLOCK;
            $link->link = Url::to(['/stats/'.md5($id.time())], true);
            $link->created_by = Yii::$app->user->id;
            $link->setColumnsFromTable($id);
            $link->setFiltersFromTable($id);
            $link->save();
        }
        return $this->response(200, [
            'item' => $link
        ]);
    }

    /**
     * Возвращает ссылку на страницу с виджетом
     * @param $id
     * @return array|mixed
     * @throws ForbiddenHttpException
     */
    public function actionStatWidgetLink($id)
    {
        if (!FoquzPoll::find()->where(['id' => $id, 'company_id' => Yii::$app->user->identity->company->id])->exists()) {
            throw new NotFoundHttpException('Опрос не найден');
        }

        /** @var FoquzPollStatWidgetKey $key */
        $key = FoquzPollStatWidgetKey::find()->where(['poll_id' => $id])->one();
        if (!$key) {
            $key = new FoquzPollStatWidgetKey();
            $key->poll_id = $id;
            $key->key = bin2hex(random_bytes(16));
            $key->save();
        }
        $linkKey = $key->key;
        return $this->response(200, [
            'link' => Url::to(['/stat-widget/' . $linkKey], true)
        ]);
    }

    public function actionUpdateStatsLink($id)
    {
        $data = \Yii::$app->request->getBodyParams();
        $link = FoquzPollStatsLink::findOne([
            'foquz_poll_id' => $id,
            'created_by' => Yii::$app->user->id,
        ]);
        $link->right_level = $data['right_level'];
        $link->show_answers = (bool)$data['show_answers'];
        $link->setColumnsFromTable($id);
        $link->setFiltersFromTable($id);
        $link->save();
        return $this->response(200, [
            'item' => $link
        ]);
    }

    public function actionGetQuestions($id, $q = null)
    {
        $companyId = Yii::$app->user->identity->company->id;
        $poll = FoquzPoll::findOne($id);

        if(!$poll) {
            return $this->response(404, ['errors' => ['pollId' => 'Опрос не найден']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        $questionsQuery = (new Query())
            ->select('foquz_question.id, foquz_question.is_required, foquz_point_item.name as pointName, foquz_question.main_question_type, foquz_question_intermediate_block_setting.screen_type, foquz_question_intermediate_block_setting.show_question_number, foquz_question.donor, foquz_question.donor_rows, foquz_question.donor_columns')
            ->addSelect(new Expression('IF(foquz_question.main_question_type = '.FoquzQuestion::TYPE_INTERMEDIATE_BLOCK.', service_name, IF(foquz_question.description = "", foquz_question.name, foquz_question.description)) as questionName'))
            ->from('foquz_question')
            ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_question.poll_id')
            ->leftJoin('foquz_question_intermediate_block_setting', 'foquz_question.id = foquz_question_intermediate_block_setting.question_id')
            ->leftJoin('foquz_point_item', 'foquz_point_item.id=foquz_question.point_id')
            ->where([
                'foquz_poll.id' => $id,
                'foquz_question.is_tmp' => 0,
                'foquz_question.is_deleted' => 0,
            ])
            ->orderBy('foquz_question.position');

        if($q) {
            $questionsQuery->andWhere(['like', 'IF(foquz_question.main_question_type = '.FoquzQuestion::TYPE_INTERMEDIATE_BLOCK.', service_name, IF(foquz_question.description = "", foquz_question.name, foquz_question.description))', $q]);
        }

        $questions = $questionsQuery->all();

        foreach ($questions as $key => $question) {
            $questions[$key]['questionName'] = $question['questionName'];
            if (!$question['donor']) {
                $questions[$key]['donor'] = $question['donor_rows'] ?? $question['donor_columns'];
            }

        }

        return $this->response(200, [
            'pollName' => $poll->name,
            'items' => $questions
        ]);
    }

    public function actionCopyQuestions($to, $pageId = null)
    {
        $poll = FoquzPoll::findOne($to);
        $companyId = Yii::$app->user->identity->company->id;

        if(!$poll) {
            return $this->response(404, ['errors' => ['pollId' => 'Опрос не найден']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        foreach(Yii::$app->request->post('questions') as $questionId)
        {
            $sourceQuestion = FoquzQuestion::findOne($questionId);
            if(!$sourceQuestion) {
                return $this->response(400, ['errors' => ['id' => 'Не удалось получить данные вопроса']]);
            }
            $newQuestion = $sourceQuestion->copy([
                'created_at' => time(),
                'updated_at' => time(),
                'created_by' => Yii::$app->user->id,
                'updated_by' => Yii::$app->user->id,
                'dictionary_element_id' => $sourceQuestion->poll_id === (int) $to ? $sourceQuestion->dictionary_element_id : null,
                'poll_id' => $to,
                'position' => FoquzQuestion::find()->where(['poll_id' => $to])->max('position') + 1
            ]);
            if($pageId && ($newQuestion->main_question_type !== FoquzQuestion::TYPE_INTERMEDIATE_BLOCK || $newQuestion->intermediateBlock->screen_type === FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_TEXT)) {
                Yii::$app->db->createCommand()->insert('foquz_poll_display_page_questions', [
                    'display_page_id' => $pageId,
                    'question_id' => $newQuestion->id
                ])->execute();
            }
        }

        if($poll->point_system) {
            $poll->max_points = $poll->calculatePoints();
            $poll->save();
        }

        $poll->refresh();

        return $this->response(200, [
            'questions' => $poll->foquzQuestions
        ]);
    }

    public function actionTags()
    {
        $t = Yii::$app->request->get();

        $t['company_id'] = $this->authByHashOrToken();
        //print_r($t); exit;
        $tagsSearch = new FoquzCompanyTagSearch();
        $tags = $tagsSearch->search($t);

        return $this->response(200, ['items' => $tags]);
    }

    public function actionDeleteQuestions($id)
    {
        $poll = FoquzPoll::findOne($id);

        $companyId = Yii::$app->user->identity->company->id;

        if(!$poll) {
            return $this->response(404, ['errors' => ['pollId' => 'Опрос не найден']]);
        }

        TagDependency::invalidate(\Yii::$app->cache, 'quiz-poll-' . $poll->id);

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        $questions = Yii::$app->request->getBodyParam('question_ids');

        FoquzQuestion::deleteAll(['id' => $questions, 'poll_id' => $id]);

        $poll->refresh();

        return $this->response(200, [
            'items' => $poll->foquzQuestions
        ]);
    }

    public function actionInterpretateScores($id, $scores)
    {
        $poll = FoquzPoll::findOne($id);
        if($poll and $poll->scoresInterpretationRanges) {
            foreach($poll->scoresInterpretationRanges as $range) {
                if($scores >= $range->min and $scores <= $range->max) {
                    return [
                        'result' => $range->result,
                        'description' => $range->description,
                    ];
                }
            }
            return [
                'result' => '',
                'description' => '',
            ];
        }

        $this->response->statusCode = 404;
        return [];
    }

    public function actionMailing($id)
    {
        $mailing = FoquzPollMailingList::findOne($id);
        if(!$mailing || $mailing->deleted)
            return $this->response(400, ['errors' => ['id' => 'Не удалось получить данные о рассылке']]);
        return $this->response(200, [
            'mailing' => $mailing
        ]);
    }

    public function actionChangeActive($id)
    {
        $poll = $this->findModel($id);
        $poll->is_active = !$poll->is_active;
        TagDependency::invalidate(\Yii::$app->cache, 'quiz-poll-' . $poll->id);

        return $poll->save(false);
    }

    public function actionSavePublicStatus($id)
    {
        $poll = $this->findModel($id);
        $json = json_decode(Yii::$app->request->getRawBody(), false);
        if (
            isset($json->status)
            &&
            is_int($json->status)
            &&
            in_array($json->status, [FoquzPoll::IS_PUBLUSHED, FoquzPoll::NOT_PUBLUSHED])
        ) {
            $poll->is_published = $json->status;
            $poll->save();
            $poll->refresh();

            return $this->response(200, ['status' => $poll->is_published]);
        }

        return $this->response(400, ['errors' => ['id' => 'Не удалось сохранить данные']]);
    }

    /**
     * @OA\Schema(
     *     schema="intermediateSetting",
     *     type="object",
     *     @OA\Property(
     *         property="id",
     *         description="ID настройки",
     *         nullable=false,
     *         type="integer",
     *         example=1,
     *     ),
     *     @OA\Property(
     *         property="text",
     *         description="Текст в блоке",
     *         nullable=true,
     *         type="string",
     *         example="Здесь какой-то текст",
     *     ),
     *     @OA\Property(
     *         property="pool_id",
     *         description="ID пула купонов (промокодов)",
     *         nullable=true,
     *         type="integer",
     *         example=123,
     *     ),
     *     @OA\Property(
     *         property="scores_button_text",
     *         description="Текст перевода кнопки «Отчет о тестировании»",
     *         maxLength=255,
     *         nullable=true,
     *         type="string",
     *         example="Test report"
     *     ),
     *     @OA\Property(
     *         property="start_over_button_text",
     *         description="Текст перевода кнопки «Начать заново»",
     *         maxLength=255,
     *         nullable=true,
     *         type="string",
     *         example="Start over"
     *     ),
     *     @OA\Property(
     *         property="unsubscribe_button_text",
     *         description="Текст перевода ссылки «Отписаться от рассылки»",
     *         maxLength=255,
     *         nullable=true,
     *         type="string",
     *         example="Unsubscribe"
     *     ),
     *     @OA\Property(
     *         property="complaint_button_text",
     *         description="Текст перевода ссылки «Пожаловаться»",
     *         maxLength=255,
     *         nullable=true,
     *         type="string",
     *         example="Complaint"
     *     ),
     *     @OA\Property(
     *         property="ready_button_text",
     *         description="Текст перевода кнопки «Готово»",
     *         maxLength=255,
     *         nullable=true,
     *         type="string",
     *         example="Ready"
     *     ),
     *     @OA\Property(
     *         property="close_widget_button_text",
     *         description="Текст перевода кнопки «Закрыть виджет»",
     *         maxLength=255,
     *         nullable=true,
     *         type="string",
     *         example="Close"
     *     ),
     * ),
     * @OA\Post (
     *     path="/foquz/api/poll/save-settings",
     *     tags={"Опрос"},
     *     summary="Сохранение настроек опроса",
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="ID опроса",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *    @OA\RequestBody(
     *        @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            encoding="text/plain",
     *            @OA\Schema(
     *                @OA\Property(
     *                    property="datetime_start",
     *                    description="Дата и время начала опроса",
     *                    type="string",
     *                    format="date-time",
     *                    nullable=true,
     *                    example="Wed Sep 06 2023 10:00:00 GMT+0300"
     *                ),
     *                @OA\Property(
     *                    property="datetime_end",
     *                    description="Дата и время окончания опроса",
     *                    type="string",
     *                    format="date-time",
     *                    nullable=true,
     *                    example="Wed Sep 26 2023 10:00:00 GMT+0300"
     *                ),
     *                @OA\Property(
     *                    property="defaultLang",
     *                    description="ID языка по умолчанию: 1 - Русский, 2 - Английский, 3 - Французский, 4 - Немецкий",
     *                    type="integer",
     *                    nullable=true,
     *                    example=1
     *                ),
     *                @OA\Property(
     *                    property="dont_send_if_passed",
     *                    description="Не отправлять опрос в приглашениях, если он уже был пройден контактом",
     *                    type="integer",
     *                    enum={0,1},
     *                    example=1
     *                ),
     *                @OA\Property(
     *                    property="dont_send_if_passed_link",
     *                    description="Не отправлять опрос по ссылке, если он уже был пройден клиентом",
     *                    type="integer",
     *                    enum={0,1},
     *                    example=1
     *                ),
     *                @OA\Property(
     *                    property="dont_send_if_promocode_used",
     *                    description="Останавливать сценарий повторов, если был использован промокод из сообщения",
     *                    type="integer",
     *                    enum={0,1},
     *                    example=1
     *                ),
     *                @OA\Property(
     *                    property="point_system",
     *                    description="Подключить систему баллов за ответ",
     *                    type="integer",
     *                    enum={0,1},
     *                    example=1
     *                ),
     *                @OA\Property(
     *                    property="personal_data",
     *                    description="Согласие респондента на передачу и обработку персональных данных",
     *                    type="integer",
     *                    enum={0,1},
     *                    example=1
     *                ),
     *                @OA\Property(
     *                    property="show_foquz_link",
     *                    description="В анкете прохождения опроса показать ссылку «Создано в Foquz»",
     *                    type="integer",
     *                    enum={0,1},
     *                    example=1
     *                ),
     *                @OA\Property(
     *                    property="stopSendingCondition",
     *                    description="Не отправлять повторно опрос контакту в приглашениях: double - Если он уже был отправлен, period - В течение периода (кол-во дней указывается в параметре stopSendingPeriod)",
     *                    type="string",
     *                    enum={"double","period"},
     *                    example="double"
     *                ),
     *                @OA\Property(
     *                    property="stopSendingPeriod",
     *                    description="Кол-во дней (для параметра stopSendingCondition = period)",
     *                    type="integer",
     *                    example=10
     *                ),
     *                @OA\Property(
     *                    property="stopSendingLinkCondition",
     *                    description="Не отправлять повторно опрос контакту по ссылке: double - Если он уже был отправлен, period - В течение периода (кол-во дней указывается в параметре stopSendingLinkPeriod)",
     *                    type="string",
     *                    enum={"double","period"},
     *                    example="double"
     *                ),
     *                @OA\Property(
     *                    property="stopSendingLinkPeriod",
     *                    description="Кол-во дней (для параметра stopSendingLinkCondition = period)",
     *                    type="integer",
     *                    example=10
     *                ),
     *                @OA\Property(
     *                     property="stopSendingLinkPeriodTime",
     *                     description="чч:мм (добавление к количеству дней stopSendingLinkPeriod)",
     *                     type="string",
     *                     example="12:30"
     *                 ),
     *                @OA\Property(
     *                    property="widget_display_ignore_limit",
     *                    description="Игнорировать ограничения показа виджета для одного посетителя сайта и показывать ему виджет, если с момента показа любого виджета прошло N дней",
     *                    type="integer",
     *                    example=10
     *                ),
     *                @OA\Property(
     *                    property="widget_display_limit_type",
     *                    description="Не показывать повторно опрос в виджете посетителю сайта (0 - выключено, 1 - если он уже был показан, 2 - в течение периода)",
     *                    type="integer",
     *                    nullable=false,
     *                    enum={0,1,2},
     *                    example=1
     *                ),
     *                @OA\Property(
     *                    property="widget_display_limit_days",
     *                    description="Не показывать повторно опрос в виджете посетителю сайта в течение N дней (для параметра widget_display_limit_type = 2)",
     *                    type="integer",
     *                    nullable=true,
     *                    example=10
     *                ),
     *                @OA\Property(
     *                    property="widget_display_limit_time",
     *                    description="Не показывать повторно опрос в виджете посетителю сайта в течение времени (чч:мм) (для параметра widget_display_limit_type = 2)",
     *                    type="string",
     *                    nullable=true,
     *                    example="10:00"
     *                ),
     *                @OA\Property(
     *                    property="widget_create_new_answer_limit",
     *                    description="Создавать новую анкету для респондента, если с момента прохождения предыдущей прошло N дней",
     *                    type="integer",
     *                    nullable=true,
     *                    example=10
     *                ),
     *                @OA\Property(
     *                    property="notificationScript",
     *                    description="ID сценария уведомления",
     *                    type="integer",
     *                    example=123
     *                ),
     *                @OA\Property(
     *                    property="time_to_pass",
     *                    description="Время прохождения анкеты (в формате ЧЧ:ММ:СС)",
     *                    type="string",
     *                    example="15:00:00"
     *                ),
     *                @OA\Property(
     *                    property="time_to_restart",
     *                    description="Начать заново опрос для планшета в торговой точке через (в секундах) для вопросов",
     *                    type="integer",
     *                    example=15
     *                ),
     *                @OA\Property(
     *                    property="time_to_restart_screen_type_end",
     *                    description="Начать заново опрос для планшета в торговой точке через (в секундах) для конечного экрана",
     *                    type="integer",
     *                    example=15
     *                ),
     *                @OA\Property(
     *                    property="processing_time_in_minutes",
     *                    description="Время на обработку анкеты (общее, в минутах)",
     *                    type="integer",
     *                    example=14400
     *                ),
     *                @OA\Property(
     *                    property="processing_time_by_link_in_minutes",
     *                    description="Время на обработку анкеты (для пройденных по ссылке, в минутах)",
     *                    type="integer",
     *                    example=14400
     *                ),
     *                @OA\Property(
     *                    property="processing_time_for_client_in_minutes",
     *                    description="Время на обработку анкеты (для пройденных за клиента, в минутах)",
     *                    type="integer",
     *                    example=14400
     *                ),
     *                @OA\Property(
     *                    property="expiration_in_minutes",
     *                    description="Время для заполнения анкеты от момента отправки респонденту (в минутах)",
     *                    type="integer",
     *                    example=54000
     *                ),
     *                @OA\Property(
     *                    property="title",
     *                    description="Заголовок (title) страницы",
     *                    type="string",
     *                    maxLength=255,
     *                    example="Заголовок опроса"
     *                ),
     *                @OA\Property(
     *                    property="goal",
     *                    description="Описание опроса",
     *                    type="string",
     *                    maxLength=65535,
     *                    example="Описание для опроса"
     *                ),
     *                @OA\Property(
     *                    property="goalCount",
     *                    description="Цель (кол-во ответов)",
     *                    type="integer",
     *                    example=132
     *                ),
     *                @OA\Property(
     *                    property="limit_count",
     *                    description="Лимит кол-ва ответов",
     *                    type="integer",
     *                    example=132
     *                ),
     *                @OA\Property(
     *                    property="default_moderator_id",
     *                    description="Модератор по умолчанию (ID пользователя)",
     *                    type="integer",
     *                    example=132
     *                ),
     *                @OA\Property(
     *                    property="default_executor_id",
     *                    description="Исполнитель по умолчанию (ID пользователя)",
     *                    type="integer",
     *                    example=132
     *                ),
     *                @OA\Property(
     *                    property="dictionary_id",
     *                    description="ID справочника для опроса",
     *                    type="integer",
     *                    example=132
     *                ),
     *                @OA\Property(
     *                    property="kioskMode",
     *                    description="Режим «киоска» по анонимной ссылке",
     *                    type="integer",
     *                    enum={0,1},
     *                    example=1
     *                ),
     *                @OA\Property(
     *                    property="lang",
     *                    description="ID языков для прохождения опроса",
     *                    type="array",
     *                    @OA\Items(anyOf={@OA\Schema(type="integer")})
     *                ),
     *                @OA\Property(
     *                    property="utm_source",
     *                    description="utm_source",
     *                    type="string",
     *                    maxLength=255,
     *                    example="main_site"
     *                ),
     *                @OA\Property(
     *                    property="utm_medium",
     *                    description="utm_medium",
     *                    type="string",
     *                    maxLength=255,
     *                    example="display"
     *                ),
     *                @OA\Property(
     *                    property="utm_campaign",
     *                    description="utm_campaign",
     *                    type="string",
     *                    maxLength=255,
     *                    example="summer_sale"
     *                ),
     *                @OA\Property(
     *                    property="editing_duration",
     *                    description="Кол-во дней после опроса, в течение которых может быть отредактирована анкета респондентом",
     *                    type="integer",
     *                    example=25
     *                ),
     *                @OA\Property(
     *                    property="need_auth",
     *                    description="Требовать авторизацию для прохождения опроса",
     *                    type="integer",
     *                    enum={0,1},
     *                    example=0
     *                ),
     *                @OA\Property(
     *                    property="css_self_type",
     *                    description="выбор источника css 0-url, 1-текст",
     *                    type="integer",
     *                    enum={0,1},
     *                    example=0
     *                 ),
     *                @OA\Property(
     *                    property="css_self_url",
     *                    description="Url своего css-файла",
     *                    type="string",
     *                    maxLength=255,
     *                    example="http://test/style.css"
     *                 ),
     *                @OA\Property(
     *                    property="css_self_text",
     *                    description="Текст своего css",
     *                    type="string",
     *                 ),
     *            ),
     *        ),
     *    ),
     *    @OA\Response(
     *        response=200,
     *        description="Success",
     *        @OA\MediaType(
     *            mediaType="application/json",
     *            @OA\Schema(
     *                type="object",
     *                example={"poll":{"id":27095,"created_at":1694532295,"updated_at":1695109249,"created_by":44,"updated_by":44,"name":"12.09 Изображение в старовом","description":null,"is_tmp":0,"status":0,"key":null,"company_id":1,"is_qr_code":0,"is_auto":0,"date_start":"2023-09-14","date_end":"2023-09-14","goals_count":null,"limit_count":null,"end_of_question":null,"trigger":null,"trigger_time":"","goal_text":"","is_folder":0,"folder_id":null,"trigger_days":null,"notification_script_id":37,"kiosk_mode":0,"is_template":0,"send_time":null,"title":"","point_system":1,"max_points":0,"dont_send_if_passed":0,"dont_send_if_passed_link":0,"dont_send_if_promocode_used":0,"deleted":0,"deleted_at":null,"deleted_by":null,"personal_data":0,"is_published":0,"time_to_pass":"15:00:00","datetime_start":null,"datetime_end":null,"show_foquz_link":0,"time_to_restart":15,"trigger_orders":null,"expiration_in_minutes":0,"utm_source":null,"utm_medium":null,"utm_campaign":null,"processing_time_in_minutes":0,"processing_time_by_link_in_minutes":0,"processing_time_for_client_in_minutes":0,"code":null,"is_short_link":0,"time_to_restart_screen_type_end":null,"stop_sending":null,"stop_sending_link":null,"stop_sending_link_minutes":null,"is_active":1,"mailing_limit":1,"mailing_frequency":20,"widget_display_ignore_limit":0,"widget_display_limit_type":2,"widget_display_limit_days":10,"widget_display_limit_days":10,"widget_display_limit_time":"10:00","default_executor_id":8,"default_moderator_id":7,"image":null,"avg_points":null,"min_points":0,"countAnswers":null,"apiAnswers":1,"statsLink":null,"triggerSetting":{},"displaySetting":null,"displayPages":{},"stopSendingCondition":null,"stopSendingPeriod":null,"need_auth":0,"foquzPollLangs":{{"id":2373,"foquz_poll_id":27095,"poll_lang_id":2,"checked":1,"default":0,"back_text":"Back","next_text":"Next","finish_text":"Finish","unrequired_text":"Optional"},{"id":2374,"foquz_poll_id":27095,"poll_lang_id":1,"checked":1,"default":1,"back_text":null,"next_text":null,"finish_text":null,"unrequired_text":"Необязательный"}},"scoresInterpretationRanges":{}}},
     *            ),
     *         ),
     *     ),
     * ),
     */
    public function actionSaveSettings($id)
    {
        $poll = $this->findModel($id);
        if (!($poll instanceof FoquzPoll)) {
            return $poll;
        }
        $form = new FoquzAutoPollForm(['poll' => $poll]);
        $post = Yii::$app->getRequest()->post();
        if(isset($post['datetime_start'], $post['datetime_end']) && !$poll->is_auto) {
            if ($post['datetime_start'] !== '' && strtotime($post['datetime_start']) === false) {
                return $this->response(400, ['errors' => ['datetime_start' => 'Неверный формат даты и времени запуска опроса']]);
            }
            if ($post['datetime_end'] !== '' && strtotime($post['datetime_end']) === false) {
                return $this->response(400, ['errors' => ['datetime_end' => 'Неверный формат даты и времени окончания опроса']]);
            }
            $post['datetime_start'] = DateTimeHelper::timeZoneDateToGMT3($post['datetime_start']);
            $post['datetime_end'] = DateTimeHelper::timeZoneDateToGMT3($post['datetime_end']);
        }

        if (!$form->validateData($post)) {
            return $this->response(400, $form->getFirstErrors());
        }

        TagDependency::invalidate(\Yii::$app->cache, 'quiz-poll-' . $poll->id);


        if ($form->load($post, '') && $form->validate() && $form->handle()) {
            $poll->refresh();
            $poll->datetime_start = DateTimeHelper::addGMT3($poll->datetime_start);
            $poll->datetime_end = DateTimeHelper::addGMT3($poll->datetime_end);
            $poll->hasDictionaryLinks = $poll->hasDictionaryLinks();
            return $this->response(200, ['poll' => $poll]);
        }

        return $this->response(400, ['errors' => ['id' => 'Не удалось сохранить данные']]);
    }

    private function findModel($id)
    {
        $poll = FoquzPoll::findOne($id);
        if (!$poll) {
            return $this->response(404, ['errors' => ['pollId' => 'Опрос не найден']]);
        }
        if ($poll->company_id !== Yii::$app->user->identity->company->id) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        return $poll;
    }

    public function actionPollLang()
    {
        return PollLang::find()->all();
    }

    /**
     * @OA\Post (
     *     path="/foquz/api/poll/translate",
     *     tags={"Опрос"},
     *     summary="Обновление перевода опроса",
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="ID опроса",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="langId",
     *         in="query",
     *         description="ID языка:
     *            1 - Русский,
     *            2 - Английский,
     *            3 - Французский,
     *            4 - Немецкий
     *         ",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             ref="#/components/schemas/intermediateSetting",
     *             example={"q":{"76870":{"cardSortingCategory": {{"id": 1,"name": "fruits"},{"id": 2,"name": "furniture"}},"intermediateSetting":{"id":9842,"text":"<p>Intermediate block text</p>","pool_id":123,"scores_button_text":"Test report","start_over_button_text":"Start over","unsubscribe_button_text":"Unsubscribe","complaint_button_text":"Complaint","ready_button_text":"Ready","close_widget_button_text":"Close"},"gallery":{{"logoId":666,"description":"Image description"}}},"76871":{"name":"Name","description":"<p>Question text</p>","sub_description":"Sub description","labels":{"1 star","2 stars","3 stars","4 stars","5 stars"}}},"back_text":"Back","next_text":"Next","finish_text":"Finish","unrequired_text":"Optional"},
     *         )
     *     ),
     *     @OA\Response(
     *           response=200,
     *           description="Success",
     *           @OA\MediaType(
     *               mediaType="application/json",
     *               @OA\Schema(
     *                   type="object",
     *                   @OA\Property(
     *                       property="id",
     *                       description="ID настройки",
     *                       nullable=false,
     *                       type="integer",
     *                       example=123,
     *                   ),
     *                   @OA\Property(
     *                       property="foquz_poll_id",
     *                       description="ID опроса",
     *                       nullable=false,
     *                       type="integer",
     *                       example=123,
     *                   ),
     *                   @OA\Property(
     *                       property="poll_lang_id",
     *                       description="ID языка: 1 - Русский, 2 - Английский, 3 - Французский, 4 - Немецкий",
     *                       nullable=false,
     *                       type="integer",
     *                       example=1,
     *                   ),
     *                   @OA\Property(
     *                       property="checked",
     *                       description="ID языка: 1 - Русский, 2 - Английский, 3 - Французский, 4 - Немецкий",
     *                       nullable=false,
     *                       type="integer",
     *                       example=1,
     *                   ),
     *               ),
     *               example={"id":2373,"foquz_poll_id":27095,"poll_lang_id":2,"checked":1,"default":0,"back_text":"Back","next_text":"Next","finish_text":"Finish","unrequired_text":"Optional","questions":{{"id":1862,"foquz_question_id":76870,"foquz_poll_lang_id":2373,"name":null,"description":"","description_html":"","sub_description":null,"placeholder_text":null,"select_placeholder_text":null,"comment_label":"Ваш комментарий","skip_text":null,"detail_question":null,"self_variant_text":null,"labels":null,"text":null,"fileLangs":{{"id":2992,"foquz_question_file_id":null,"end_screen_logo_id":666,"foquz_poll_lang_id":2373,"file_path":null,"file_full_path":null,"link":null,"description":"Image description"}},"settingLangs":{{"id":23,"setting_id":9842,"lang_id":2373,"text":"<p>Intermediate block text</p>","complaint_button_text":"Complaint","unsubscribe_button_text":"Unsubscribe","poll_button_text":null,"close_widget_button_text":null,"ready_button_text":"Ready","external_link":null,"scores_button_text":"Test report","start_over_button_text":"Start over","agreement_text":null,"pool_id":null}}},{"id":1863,"foquz_question_id":76871,"foquz_poll_lang_id":2373,"name":"Name","description":"<p>Question text</p>","description_html":"<p>Question text</p>","sub_description":"Sub description","placeholder_text":null,"select_placeholder_text":null,"comment_label":"Ваш комментарий","skip_text":null,"detail_question":null,"self_variant_text":null,"labels":{"1 star","2 stars","3 stars","4 stars","5 stars"},"text":null}}},
     *           ),
     *     ),
     *     @OA\Response(
     *             response=400,
     *             description="Error",
     *             @OA\JsonContent(
     *                type="object",
     *                @OA\Property(
     *                    property="errors",
     *                    description="Ошибки",
     *                    type="array",
     *                    @OA\Items(
     *                        type="string",
     *                        description="Текст ошибки",
     *                    ),
     *                ),
     *            ),
     *       ),
     * ),
     */
    public function actionTranslate(int $id, int $langId): array
    {
        $translateService = new TranslateService(Yii::$app->user->identity->company->id);
        TagDependency::invalidate(\Yii::$app->cache, 'quiz-poll-' . $id);

            try {
            $model = FoquzPollLang::findOne(['foquz_poll_id' => $id, 'poll_lang_id' => $langId]);
            if (!$model) {
                $model = new FoquzPollLang(['foquz_poll_id' => $id, 'poll_lang_id' => $langId]);
            }
            // перевод общих настроек
            $model->setAttributes(Yii::$app->request->post());
            if (!$model->save()) {
                throw new Exception('Не удалось сохранить перевод для опроса', $model->errors);
            }
            $result = $model->getAttributes();
            // перевод вопросов
            foreach (Yii::$app->request->post('q') as $questionId => $params) {
                $result['questions'][] = $translateService->translateQuestion($params, (int)$questionId, $langId);
            }
            return $result;
        } catch (\Throwable $e) {
            return $this->response('400', ['error' => $e->getMessage()]);
        }
    }

    public function actionGetTranslate($id, $langId, $vue = false)
    {
        $poll = FoquzPoll::findOne($id);
        if (!$poll) {
            if ($vue) {
                return null;
            }
            throw new NotFoundHttpException('Опрос не найден');
        }
        $pollLang = FoquzPollLang::findOne(['foquz_poll_id' => $poll->id, 'poll_lang_id' => $langId]);
        if (!$pollLang) {
            if ($vue) {
                return null;
            }
            throw new NotFoundHttpException('Запись о языке опроса не найдена');
        }

        return Yii::$app->cache->getOrSet('foquz_poll_' . $poll->id . '_lang_' . $langId,
            function () use ($pollLang, $poll, $langId) {
                $result = ArrayHelper::toArray($pollLang);

                $questions = FoquzQuestion::findAll(['poll_id' => $poll->id, 'is_deleted' => 0]);
                foreach ($questions as $question) {
                    $questionLang = FoquzQuestionLang::findOne(['foquz_question_id'  => $question->id,
                                                                'foquz_poll_lang_id' => $pollLang->id
                    ]);

                    if (!$questionLang) {
                        continue;
                    }
                    if ($questionLang->labels && is_string($questionLang->labels)) {
                        //  $questionLang->labels = @json_decode($questionLang->labels, true);
                    }
                    $resultQuestion = ArrayHelper::toArray($questionLang);
                    $resultFiles = $resultDetails = $resultSettings = $resultForms = [];
                    foreach ($question->questionFiles as $questionFile) {
                        $fileLang = FoquzQuestionFileLang::findOne([
                            'foquz_question_file_id' => $questionFile->id,
                            'foquz_poll_lang_id'     => $pollLang->id
                        ]);
                        if ($fileLang) {
                            $resultFiles[] = $fileLang;
                        }
                    }
                    foreach ($question->foquzQuestionEndScreenLogos as $endScreenLogo) {
                        $fileLang = FoquzQuestionFileLang::findOne([
                            'end_screen_logo_id' => $endScreenLogo->id,
                            'foquz_poll_lang_id' => $pollLang->id
                        ]);
                        if ($fileLang) {
                            $resultFiles[] = $fileLang;
                        }
                    }
                    if ($question->getMainDonor() && $question->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                        $dictionaryElementsID = ArrayHelper::getColumn($question->recipientQuestionDetails,
                            'dictionary_element_id');
                        /** @var FoquzQuestionDetailLang[] $detailLangs */
                        $detailLangs = FoquzQuestionDetailLang::find()->where([
                            'foquz_question_id'  => $question->id,
                            'detail_id'          => $dictionaryElementsID,
                            'foquz_poll_lang_id' => $pollLang->id,
                        ])->all();
                        $detailLangs = array_map(static function ($item) {
                            $item->foquz_question_detail_id = $item->detail_id;
                            return $item;
                        }, $detailLangs);
                        if ($detailLangs) {
                            $resultDetails = ArrayHelper::merge($resultDetails, $detailLangs);
                        }
                    } else {
                        foreach ($question->questionDetails as $questionDetail) {
                            $detailLang = FoquzQuestionDetailLang::findOne([
                                'foquz_question_id'        => $question->id,
                                'foquz_question_detail_id' => $questionDetail->id,
                                'foquz_poll_lang_id'       => $pollLang->id,
                            ]);
                            if ($detailLang) {
                                $resultDetails[] = $detailLang;
                            }
                        }
                    }
                    if ($question->main_question_type === FoquzQuestion::TYPE_FILIAL) {
                        foreach (json_decode($question->detail_question) as $value) {
                            $detailLang = FoquzQuestionDetailLang::findOne([
                                'foquz_question_id'  => $question->id,
                                'detail_id'          => $value,
                                'foquz_poll_lang_id' => $pollLang->id,
                            ]);
                            if ($detailLang) {
                                $resultDetails[] = $detailLang;
                            }
                        }
                    }
                    if ($question->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                        $langSet = [];
                        foreach (json_decode($question->detail_question) as $value) {
                            $dictionaryElement = DictionaryElement::findOne($value);
                            while ($dictionaryElement && $dictionaryElement->parent_id && !isset($langSet[$dictionaryElement->parent_id])) {
                                $detailLang = FoquzQuestionDetailLang::findOne([
                                    'foquz_question_id'  => $question->id,
                                    'detail_id'          => $dictionaryElement->parent_id,
                                    'foquz_poll_lang_id' => $pollLang->id,
                                ]);
                                if ($detailLang) {
                                    $langSet[$detailLang->id] = $detailLang;
                                }
                                $dictionaryElement = DictionaryElement::findOne($dictionaryElement->parent_id);
                            }
                            $detailLang = FoquzQuestionDetailLang::findOne([
                                'foquz_question_id'  => $question->id,
                                'detail_id'          => $value,
                                'foquz_poll_lang_id' => $pollLang->id,
                            ]);
                            if ($detailLang) {
                                $langSet[$detailLang->id] = $detailLang;
                            }
                        }
                        $resultDetails = ArrayHelper::merge($resultDetails, array_values($langSet));
                    }
                    foreach ($question->recipientQuestionDetails as $recipientDetail) {
                        $detailLang = FoquzQuestionDetailLang::findOne([
                            'foquz_question_id'        => $question->id,
                            'foquz_question_detail_id' => $recipientDetail->question_detail_id,
                            'foquz_poll_lang_id'       => $pollLang->id,
                        ]);
                        if ($detailLang) {
                            $resultDetails[] = $detailLang;
                        }
                    }
                    if ($question->intermediateBlock) {
                        $settingLang = FoquzQuestionIntermediateBlockSettingLang::findOne([
                            'setting_id' => $question->intermediateBlock->id,
                            'lang_id'    => $pollLang->id,
                        ]);
                        if ($settingLang) {
                            $resultSettings[] = $settingLang;
                        }
                    }
                    foreach ($question->formFields as $formField) {
                        $formLang = FoquzQuestionFormFieldLang::findOne([
                            'form_field_id'      => $formField->id,
                            'foquz_poll_lang_id' => $pollLang->id,
                        ]);
                        if ($formField) {
                            $resultForms[] = $formLang;
                        }
                    }
                    if ($question->main_question_type === FoquzQuestion::TYPE_3D_MATRIX ||
                        $question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
                        $matrixElements = FoquzQuestionMatrixElementLang::find()->where([
                            'matrix_element_id' => FoquzQuestionMatrixElement::find()->select('id')->where([
                                'foquz_question_id' => $question->id,
                                'is_deleted'        => 0
                            ]),
                            'poll_lang_id'      => $pollLang->id,
                        ])->all();
                        $matrixElementsIDs = ArrayHelper::getColumn($matrixElements, 'matrix_element_id');
                        $resultQuestion['matrixElements'] = $matrixElements;
                        $resultQuestion['matrixVariants'] = FoquzQuestionMatrixElementVariantLang::find()->where([
                            'variant_id'   => FoquzQuestionMatrixElementVariant::find()->select('id')->where([
                                'matrix_element_id' => $matrixElementsIDs,
                                'is_deleted'        => 0
                            ]),
                            'poll_lang_id' => $pollLang->id,
                        ])->all();
                    }
                    if ($question->main_question_type === FoquzQuestion::TYPE_CARD_SORTING_CLOSED) {
                        foreach ($question->cardSortingCategories as $cardSortingCategory) {
                            /** @var array $resultQuestion ['cardSortingCategorylangs'] */
                            $resultQuestion['cardSortingCategorylangs'][] = $cardSortingCategory->getLangs($pollLang->id)->one();
                        }
                    }

                    if ($question->main_question_type === FoquzQuestion::TYPE_FIRST_CLICK) {
                        $resultQuestion['firstClickLang'] = FoquzQuestionFirstClickLang::find()->where([
                            'setting_id'   => FoquzQuestionFirstClick::find()->select('id')->where([
                                'foquz_question_id' => $question->id,
                            ]),
                            'poll_lang_id' => $langId,
                        ])->one();
                        $resultQuestion['firstClickAreaLang'] = FoquzQuestionFirstClickAreaLang::find()->where([
                            'setting_id'   => FoquzQuestionFirstClickArea::find()->select('id')->where([
                                'question_id' => $question->id,
                            ]),
                            'poll_lang_id' => $langId,
                        ])->all();
                    }

                    if ($resultFiles) {
                        $resultQuestion['fileLangs'] = $resultFiles;
                    }
                    if ($resultDetails) {
                        $resultQuestion['detailLangs'] = $resultDetails;
                    }
                    if ($resultSettings) {
                        $resultQuestion['settingLangs'] = $resultSettings;
                    }
                    if ($resultForms) {
                        $resultQuestion['formLangs'] = $resultForms;
                    }
                    $result['questions'][] = $resultQuestion;
                }

                return $result;
            }, 60*60, new TagDependency(['tags' => ['quiz', 'quiz-poll-' . $poll->id]]));
    }

    public function actionSaveScoresInterpretationRanges($pollId)
    {
        $poll = FoquzPoll::find()->where([
            'id' => $pollId,
            'company_id' => Yii::$app->user->identity->company->id,
            'point_system' => 1
        ])->one();

        if($poll === null) {
            $this->response->statusCode = 404;
            return ['errors' => ['pollId' => 'Опрос не найден']];
        }

        $ranges = $this->request->post('ranges');

        $scopesIds = ArrayHelper::getColumn($ranges, 'id');
        $scopes = $poll->getScoresInterpretationRanges()->all();

        $transaction = FoquzPoll::getDb()->beginTransaction();
        TagDependency::invalidate(\Yii::$app->cache, 'quiz-poll-' . $poll->id);

        try {
            foreach ($scopes as $scope) {
                if (!in_array($scope->id, $scopesIds, false)) {
                    $scope->delete();
                }
            }

            if (ScoresInterpretationRange::insertOrUpdate($poll, $ranges, $errors) === false) {
                $this->response->statusCode = 400;
                return $errors;
            }

            $transaction->commit();

        } catch(\Exception $e) {
            $transaction->rollBack();
            throw new ServerErrorHttpException($e->getMessage());
        }

        return $poll->getScoresInterpretationRanges()->all();
    }

    public function actionScoresInterpretationRangesUpdatePositions($pollId)
    {
        if(!($poll = FoquzPoll::find()->where([
            'id' => $pollId,
            'company_id' => Yii::$app->user->identity->company->id,
        ])->one())) {
            $this->response->statusCode = 404;
            return [];
        }
        if(!$poll) {
            $this->response->statusCode = 404;
            return [];
        }
        $post = Yii::$app->request->post('ids');
        foreach($post as $k => $v) {
            $range = $poll->getScoresInterpretationRanges()->where(['id' => $v])->one();
            if($range) {
                $range->position = $k + 1;
                $range->save();
            }
        }
        TagDependency::invalidate(\Yii::$app->cache, 'quiz-poll-' . $poll->id);

        return $poll->scoresInterpretationRanges;
    }

    public function actionDeleteScoresInterpretationRange($id)
    {
        $range = ScoresInterpretationRange::findOne($id);
        if(!$range->getFoquzPoll()->where([
            'company_id' => Yii::$app->user->identity->company->id,
        ])->one()) {
            $this->response->statusCode = 404;
            return [];
        }
        ScoresInterpretationRange::deleteAll(['id' => $id]);
        TagDependency::invalidate(\Yii::$app->cache, 'quiz-poll-' . $range->foquz_poll_id );

        return [];
    }

    public function actionCreateFromJson()
    {
        ini_set('memory_limit', '1024M');
        $model = new PollJsonForm();
        $model->file = UploadedFile::getInstanceByName('file');
        $model->companyId = Yii::$app->user->identity->company->id;


        if (!$model->validate()) {
            $this->response->statusCode = 400;
            return [
                'success' => false,
                'errors' => $model->errors
            ];
        }

        $db = Yii::$app->db;
        $transaction = $db->beginTransaction();

        try {
            $model->save();
            $transaction->commit();
        } catch(\Throwable $e) {
            $transaction->rollBack();
            Yii::error($e->getTraceAsString());
            $this->response->statusCode = 400;
            return [
                'success' => false,
                'errors' => $e->getMessage()
            ];
        }

        $poll = $model->getPoll();
        $questionId = !empty($poll->foquzQuestions) ? $poll->foquzQuestions[0]->id : '';

        if ((Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher()) && $folder_id = Yii::$app->request->getBodyParam('folder_id')) {
            $editorFolder = new EditorFolder();
            $editorFolder->user_id = Yii::$app->user->identity->id;
            $editorFolder->folder_id = $folder_id;
            $editorFolder->save();
        }

        return [
            'success' => true,
            'name' => $poll->name,
            'url' => Url::toRoute(['foquz-question/update', 'id' => $questionId, 'pollId' => $poll->id], true)
        ];
    }

    /**
     * @throws Exception
     */
    public function actionMailingSettings($id)
    {
        $model = $this->findModel($id);
        $model->mailing_limit = Yii::$app->request->post('mailing_limit') ?? 0;
        $model->mailing_frequency = Yii::$app->request->post('mailing_frequency');

        if ((int)$model->mailing_frequency === 0) {
            $model->mailing_frequency = null;
        }

        if ($model->save()) {
            return $model;
        }

        throw new Exception('Не удалось сохранить настройки', $model->errors);
    }

    public function actionSaveStatFilterSettings($poll_id)
    {
        $userID = Yii::$app->user->id;
        $model = FoquzPollStatFilterSettings::findOne(['foquz_poll_id' => $poll_id, 'user_id' => $userID]);
        if (!$model) {
            $model = new FoquzPollStatFilterSettings();
            $model->foquz_poll_id = $poll_id;
            $model->user_id = $userID;
        }
        $filterSettings = [];
        foreach (Yii::$app->request->post('questions', []) as $key => $value) {
            if (empty($value['question_id']) || empty($value['action'])) {
                continue;
            }
            $filterSettings[$key]['active'] = (int) ($value['active'] ?? 0);
            $filterSettings[$key]['question_id'] = (int) $value['question_id'];
            $filterSettings[$key]['action'] = (int) $value['action'];
            $filterSettings[$key]['variants'] = $value['variants'] ?? [];
            $filterSettings[$key]['condition'] = $value['condition'] ?? FoquzPollStatFilterSettings::CONDITION_OR;
        }
        $model->filter_settings = $filterSettings;
        if ($model->save()) {
            return $this->response(200, ['success' => true, 'filter_settings' => $model->filter_settings]);
        }
        return $this->response(400, ['errors' => $model->errors]);
    }

    public function actionResetStatFilterSettings($poll_id)
    {
        $userID = Yii::$app->user->id;
        $model = FoquzPollStatFilterSettings::findOne(['foquz_poll_id' => $poll_id, 'user_id' => $userID]);
        if (!$model) {
            $this->response(200, ['success' => true]);
        }
        $this->response(200, ['success' => (bool)$model->delete()]);
    }

    public function actionFilterQuestions($poll_id)
    {
        if (!FoquzPoll::find()->where(['id' => $poll_id, 'company_id' => Yii::$app->user->identity->company->id])->exists()) {
            return $this->response(404, ['errors' => ['poll_id' => 'Опрос не найден']]);
        }
        /** @var FoquzQuestion[] $questions */
        $questions = FoquzQuestion::find()
            ->select(['id', 'description', 'position'])
            ->where(['poll_id' => $poll_id, 'is_deleted' => 0, 'is_tmp' => 0])
            ->andWhere(['main_question_type' => FoquzQuestion::TYPE_VARIANTS])
            ->orderBy(['position' => SORT_ASC])
            ->all();

        $result = [];
        foreach ($questions as $question) {
            $result[] = [
                'id' => $question->id,
                'name' => $question->position. '. ' . $question->description,
                'variants' => $question->collectVariants(),
            ];
        }

        return $this->response(200, ['questions' => $result]);
    }

    /**
     * @OA\Post (
     *     path="/foquz/api/poll/create-widget",
     *     tags={"Опрос"},
     *     summary="Создание виджета для опроса",
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 ref="#/components/schemas/pollWidgetItem"
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Success",
     *         @OA\JsonContent(
     *             ref="#/components/schemas/pollWidgetItem"
     *         ),
     *     ),
     *     @OA\Response(
     *            response=400,
     *            description="Неверный запрос",
     *            @OA\JsonContent(
     *                type="object",
     *                @OA\Property(
     *                    property="errors",
     *                    description="Ошибки",
     *                    type="array",
     *                    @OA\Items(
     *                        type="string",
     *                        description="Текст ошибки",
     *                        example="Неправильное значение"
     *                    ),
     *                ),
     *            ),
     *       ),
     * ),
     */
    public function actionCreateWidget()
    {
        $model = new FoquzPollWidget();
        $model->load(Yii::$app->request->post(), '');
        if ($model->save()) {
            $model->refresh();
            return $this->response(200, ['success' => true, 'model' => $model]);
        }
        return $this->response(400, ['errors' => $model->errors]);
    }

    /**
     * @OA\Post (
     *     path="/foquz/api/poll/update-widget",
     *     tags={"Опрос"},
     *     summary="Редактирование виджета",
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="ID виджета",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  ref="#/components/schemas/pollWidgetItem"
     *              ),
     *          ),
     *      ),
     *     @OA\Response(
     *          response=200,
     *          description="Success",
     *          @OA\JsonContent(
     *              ref="#/components/schemas/pollWidgetItem"
     *          ),
     *      ),
     *     @OA\Response(
     *           response=400,
     *           description="Неверный запрос",
     *           @OA\JsonContent(
     *               type="object",
     *               @OA\Property(
     *                   property="errors",
     *                   description="Ошибки",
     *                   type="array",
     *                   @OA\Items(
     *                       type="string",
     *                       description="Текст ошибки",
     *                       example="Неправильное значение"
     *                   ),
     *               ),
     *           ),
     *      ),
     *  ),
     */
    public function actionUpdateWidget($id)
    {
        $model = FoquzPollWidget::findOne($id);
        if (!$model) {
            return $this->response(404, ['errors' => ['id' => 'Виджет не найден']]);
        }
        $model->load(Yii::$app->request->post(), '');
        if ($model->save()) {
            $model->refresh();
            return $this->response(200, ['success' => true, 'model' => $model]);
        }
        return $this->response(400, ['errors' => $model->errors]);
    }

    public function actionDeleteWidget($id)
    {
        $model = FoquzPollWidget::find()
            ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_poll_widget.poll_id')
            ->where(['foquz_poll_widget.id' => $id, 'foquz_poll.company_id' => Yii::$app->user->identity->company->id])
            ->one();

        if (!$model) {
            return $this->response(404, ['errors' => ['id' => 'Виджет не найден']]);
        }

        return $this->response(200, ['success' => (bool)$model->delete()]);
    }

    public function actionWidgets($poll_id)
    {
        if (!FoquzPoll::find()->where(['id' => $poll_id, 'company_id' => Yii::$app->user->identity->company->id])->exists()) {
            return $this->response(404, ['errors' => ['poll_id' => 'Неверный ID опроса']]);
        }

        $sort = new Sort([
            'defaultOrder' => [
                'created_at' => SORT_DESC,
            ],
            'attributes' => [
                'created_at',
                'is_active' => [
                    'asc' => ['is_active' => SORT_ASC,],
                    'desc' => ['is_active' => SORT_DESC,],
                    'default' => SORT_DESC,
                ],
            ],
        ]);

        FoquzPollLinkQuotes::getFirstActiveQuote($poll_id);
        $widgets = FoquzPollWidget::find()
            ->with('filial', 'pollLinks')
            ->where(['poll_id' => $poll_id])
            ->orderBy($sort->orders)
            ->all();

        $pollLinks = FoquzPollLinkQuotes::find()->where(['poll_id' => $poll_id])->all();
        return $this->response(200, ['widgets' => $widgets, 'poll_links' => $pollLinks]);
    }

    public function actionNewWidgetsCount($poll_id)
    {
        return (int) FoquzPollWidget::find()
            ->where(['poll_id' => $poll_id])
            ->andWhere(['OR',
                ['name' => 'Новый виджет'],
                ['REGEXP', 'name', '^Новый виджет [[:digit:]]$']
            ])
            ->count();
    }

    /**
     * @OA\Post (
     *      path="/foquz/api/poll/add-tags",
     *      tags={"Теги анкеты"},
     *      summary="Добавление тегов к анкетам",
     *      @OA\RequestBody(
     *          @OA\MediaType(
     *              mediaType="multipart/form-data",
     *              encoding="text/plain",
     *              @OA\Schema(
     *                  ref="#/components/schemas/answerTagAddRequest"
     *              ),
     *          ),
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Теги добавлены",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="array",
     *                 @OA\Items(oneOf={
     *                     @OA\Schema(
     *                         type="object",
     *                         @OA\Property(
     *                             property="success",
     *                             description="Результат",
     *                             type="boolean",
     *                             example=true,
     *                             enum={true},
     *                          ),
     *                      ),
     *                  }),
     *              ),
     *          ),
     *     ),
     *     @OA\Response(
     *          response=400,
     *          description="Ошибка",
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  type="array",
     *                  @OA\Items(oneOf={
     *                      @OA\Schema(
     *                          type="object",
     *                          @OA\Property(
     *                              property="success",
     *                              description="Результат",
     *                              type="boolean",
     *                              example=false,
     *                              enum={false},
     *                          ),
     *                          @OA\Property(
     *                              property="errors",
     *                              description="Ошибки",
     *                              type="array",
     *                              example={"answer_id":"Ответа с ID=123456 не существует"},
     *                              @OA\Items(oneOf={
     *                                  @OA\Schema(
     *                                      type="object",
     *                                      @OA\AdditionalProperties(
     *                                          type="string",
     *                                          title="Название поля и ошибка",
     *                                      ),
     *                                  ),
     *                              }),
     *                          ),
     *                      ),
     *                  }),
     *              ),
     *          ),
     *      ),
     * ),
     */
    public function actionAddTags()
    {
        $data = Yii::$app->getRequest()->getBodyParams();

        try {
            if (empty($data['answers']) || !is_array($data['answers'])) {
                throw ValidateException::make('answers', 'Неправильно задан параметр');
            }

            if (empty($data['tags']) || !is_array($data['tags'])) {
                throw ValidateException::make('tags', Yii::t('main', 'Не заданы теги'));
            }

            $answerTagService = new AnswerTagService();
            /** @var User $user */
            $user = Yii::$app->user->identity;
            $answerTagService->addTags(
                $data['answers'],
                $data['tags'],
                $user,
            );

            return ['success' => true];

        } catch (ValidateException $e) {
            $this->response->statusCode = $e->getCode();
            return ['success' => false, 'errors' => $e->getError()];
        } catch (\Throwable $e) {
            $this->response->statusCode = 500;
            Yii::error($e->getMessage(), __METHOD__);
            return ['success' => false, 'errors' => $e->getMessage()];
        }
    }

    /**
     * @OA\Post (
     *      path="/foquz/api/poll/remove-tags",
     *      tags={"Теги анкеты"},
     *      summary="Удаление тегов из анкет(ы)",
     *      @OA\RequestBody(
     *          @OA\MediaType(
     *              mediaType="multipart/form-data",
     *              encoding="text/plain",
     *              @OA\Schema(
     *                   ref="#/components/schemas/answerTagRemoveRequest"
     *               ),
     *          ),
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Теги удалены",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="array",
     *                 @OA\Items(oneOf={
     *                     @OA\Schema(
     *                         type="object",
     *                         @OA\Property(
     *                             property="success",
     *                             description="Результат",
     *                             type="boolean",
     *                             example=true,
     *                             enum={true},
     *                          ),
     *                      ),
     *                  }),
     *              ),
     *          ),
     *     ),
     *     @OA\Response(
     *          response=400,
     *          description="Ошибка",
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  type="array",
     *                  @OA\Items(oneOf={
     *                      @OA\Schema(
     *                          type="object",
     *                          @OA\Property(
     *                              property="success",
     *                              description="Результат",
     *                              type="boolean",
     *                              example=false,
     *                              enum={false},
     *                          ),
     *                          @OA\Property(
     *                              property="errors",
     *                              description="Ошибки",
     *                              type="array",
     *                              example={"answer_id":"Ответа с ID=123456 не существует"},
     *                              @OA\Items(oneOf={
     *                                  @OA\Schema(
     *                                      type="object",
     *                                      @OA\AdditionalProperties(
     *                                          type="string",
     *                                          title="Название поля и ошибка",
     *                                      ),
     *                                  ),
     *                              }),
     *                          ),
     *                      ),
     *                  }),
     *              ),
     *          ),
     *      ),
     * ),
     */
    public function actionRemoveTags()
    {
        $data = Yii::$app->getRequest()->getBodyParams();

        try {
            if (empty($data['answers']) || !is_array($data['answers'])) {
                throw ValidateException::make('answers', 'Неправильно задан параметр');
            }

            if (empty($data['tags']) || !is_array($data['tags'])) {
                throw ValidateException::make('tags', Yii::t('main', 'Не заданы теги'));
            }

            $answerTagService = new AnswerTagService();
            /** @var User $user */
            $user = Yii::$app->user->identity;
            $answerTagService->removeTags(
                $data['answers'],
                $data['tags'],
                $user
            );

            return ['success' => true];

        } catch (ValidateException $e) {
            $this->response->statusCode = $e->getCode();
            return ['success' => false, 'errors' => $e->getError()];
        } catch (\Throwable $e) {
            $this->response->statusCode = 500;
            Yii::error($e->getMessage(), __METHOD__);
            return ['success' => false, 'errors' => $e->getMessage()];
        }

    }
}
