<?php

namespace app\modules\foquz\controllers\api\v1;

use app\modules\foquz\controllers\api\ApiController;
use app\modules\foquz\exceptions\FolderNotFoundException;
use app\modules\foquz\models\FoquzPoll;
use Yii;
use yii\filters\auth\QueryParamAuth;
use yii\filters\VerbFilter;
use yii\helpers\Json;

class FolderController extends ApiController
{
    /**
     * @return array
     */
    public function behaviors(): array
    {
        $behaviors = parent::behaviors();
        $behaviors['authenticator'] = [
            'class' => QueryParamAuth::className()
        ];
        $behaviors['verbs'] = [
            'class' => VerbFilter::className(),
            'actions' => [
                'update' => ['POST'],
                'delete' => ['DELETE'],
                'stats' => ['GET'],
            ]
        ];
        return $behaviors;
    }

    /**
     * @param $id
     * @return void
     * @throws \app\modules\foquz\exceptions\FolderNotFoundException
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        $data = Json::decode(Yii::$app->getRequest()->getRawBody(), false);

        try {
            // TODO: Потом бы убрать это все от сюда....
            if (!empty($data->name)) {
                $model->name = $data->name;
            }

            if (!empty($data->description)) {
                $model->description = $data->description;
            }

            if (isset($data->location) && strlen($data->location) > 0) {
                if ($data->location == 0) {
                    $model->folder_id = null;
                } else {
                    $model->folder_id = $data->location;
                }
            }

            $model->save();
            $model->refresh();

            // TODO: Response Class!!!
            $returnData = [
                'id' => $model->id,
                'name' => $model->name,
                'description' => $model->description,
                'location' => $model->folder_id,
            ];

            return $this->response(200, [
                'folder' => $returnData
            ]);
        } catch (\Exception $exception) {
            // TODO: Унифицировать и Логировать бы ошибки....
            return $this->response(400, [
                'errors' => [
                    'id' => $exception->getMessage()
                ]
            ]);
        }
    }

    /**
     * @OA\Delete (
     *     path="/foquz/api/v1/folder/delete",
     *     tags={"Папки"},
     *     summary="Удаление пустой папки",
     *     security={ {"api_key": {}} },
     *     @OA\Parameter(
     *         name="access-token",
     *         in="query",
     *         description="Токен доступа",
     *         required=true,
     *         example="vZGkGCGTTDkxTlv",
     *         @OA\Schema(
     *             type="string",
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="ID папки",
     *         required=true,
     *         example=42288,
     *         @OA\Schema(
     *             type="integer",
     *         ),
     *     ),
     *     @OA\Response(
     *         response=204,
     *         description="Success",
     *      ),
     * ),
     */
    public function actionDelete(int $id)
    {
        try {
            $model = $this->findModel($id);
            if ($this->includedCount($id)) {
                throw new \InvalidArgumentException('У папки есть вложенные опросы');
            }
            if ($this->includedCount($id, 1)) {
                throw new \InvalidArgumentException('У папки есть вложенные папки');
            }
            $model->deleted = 1;
            $model->deleted_at = date('Y-m-d H:i:s');
            $model->deleted_by = \Yii::$app->user->id;
            $model->save();

            return $this->response(204);
        } catch (\Exception $e) {
            return $this->response(400, [
                'errors' => [
                    'id' => $e->getMessage()
                ]
            ]);
        }
    }

    /**
     * @OA\Get (
     *     path="/foquz/api/v1/folder/stats",
     *     tags={"Папки"},
     *     summary="Получение информации о папке",
     *     security={ {"api_key": {}} },
     *     @OA\Parameter(
     *         name="access-token",
     *         in="query",
     *         description="Токен доступа",
     *         required=true,
     *         example="vZGkGCGTTDkxTlv",
     *         @OA\Schema(
     *             type="string",
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="ID папки",
     *         required=true,
     *         example=42288,
     *         @OA\Schema(
     *             type="integer",
     *         ),
     *     ),
     *     @OA\Response(
     *            response=200,
     *            description="Success",
     *            @OA\JsonContent(
     *                @OA\Property(
     *                    title="Флаг удаления",
     *                    property="deleted",
     *                    type="boolean",
     *                    example=false,
     *               ),
     *               @OA\Property(
     *                    title="Количество вложенных опросов",
     *                    property="included_polls",
     *                    type="integer",
     *                    example=1,
     *                ),
     *               @OA\Property(
     *                    title="Количество вложенных папок",
     *                    property="included_folders",
     *                    type="integer",
     *                    example=1,
     *                 ),
     *           ),
     *     ),
     * ),
     */
    public function actionStats(int $id)
    {
        try {
            $model = $this->findModel($id);
            return [
                'deleted' => $model->deleted,
                'included_polls' => $this->includedCount($id),
                'included_folders' => $this->includedCount($id, 1)
            ];
        } catch (\Exception $e) {
            return $this->response(400, [
                'errors' => [
                    'id' => $e->getMessage()
                ]
            ]);
        }
    }

    /**
     * Инфа о количестве вложенных в папку ресурсов
     * @param int $id Id папки
     * @param int $folder 0-кол-во вложенных опросов, 1 -кол-во вложенных папок
     * @return int
     */
    private function includedCount($id, $folder = 0): int
    {
        return FoquzPoll::find()->where([
            'folder_id' => $id,
            'is_folder' => $folder,
            'deleted' => 0,
            'status' => FoquzPoll::STATUS_NEW
        ])->count();
    }

    /**
     * @param $id
     * @return \app\modules\foquz\models\FoquzPoll|array|\yii\db\ActiveRecord
     * @throws \app\modules\foquz\exceptions\FolderNotFoundException
     */
    private function findModel($id)
    {
        $model = FoquzPoll::find()
            ->where(['=', 'id', $id])
            ->andWhere(['=', 'is_folder', 1])
            ->andWhere(['=', 'company_id', Yii::$app->user->identity->company->id])
            ->one();

        if (null === $model) {
            throw new FolderNotFoundException('Not Found');
        }
        return $model;
    }
}
