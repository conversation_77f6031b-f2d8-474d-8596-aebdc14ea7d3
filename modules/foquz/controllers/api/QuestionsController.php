<?php

namespace app\modules\foquz\controllers\api;

use app\components\helpers\DictionariesHelper;
use app\models\DictionaryElement;
use app\modules\foquz\models\FoquzFile;
use app\modules\foquz\models\FoquzPointItem;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollDisplayPageQuestion;
use app\modules\foquz\models\FoquzPollLang;
use app\modules\foquz\models\FoquzPollQuestionViewLogic;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionAddressCodes;
use app\modules\foquz\models\FoquzQuestionCardSortingCategory;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionDetailLang;
use app\modules\foquz\models\FoquzQuestionDifferentialRow;
use app\modules\foquz\models\FoquzQuestionEndScreenLogo;
use app\modules\foquz\models\FoquzQuestionFile;
use app\modules\foquz\models\FoquzQuestionFileLang;
use app\modules\foquz\models\FoquzQuestionFormField;
use app\modules\foquz\models\FoquzQuestionFormFieldLang;
use app\modules\foquz\models\FoquzQuestionImage;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSetting;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSettingLang;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSettingSocNetworks;
use app\modules\foquz\models\FoquzQuestionLang;
use app\modules\foquz\models\FoquzQuestionMatrixElement;
use app\modules\foquz\models\FoquzQuestionMatrixElementLang;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariant;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariantLang;
use app\modules\foquz\models\FoquzQuestionNpsRatingSetting;
use app\modules\foquz\models\FoquzQuestionPrioritySettings;
use app\modules\foquz\models\FoquzQuestionRightAnswer;
use app\modules\foquz\models\FoquzQuestionScaleRatingSetting;
use app\modules\foquz\models\FoquzQuestionSemDifSetting;
use app\modules\foquz\models\FoquzQuestionSmile;
use app\modules\foquz\models\FoquzQuestionStarRatingOptions;
use app\modules\foquz\models\RecipientQuestionDetail;
use app\modules\foquz\models\UploadForm;
use app\modules\foquz\models\VideoUploadForm;
use app\modules\foquz\models\YouTubeVideoForm;
use app\modules\foquz\services\api\ExtraQuestionService;
use app\modules\foquz\services\FileService;
use app\modules\foquz\services\QuestionEndScreenLogoService;
use app\modules\foquz\services\QuestionFileService;
use app\modules\foquz\services\translate\TranslateService;
use app\modules\foquz\services\questions\QuestionSaveService;
use FFMpeg\Coordinate\TimeCode;
use FFMpeg\FFMpeg;
use RuntimeException;
use Yii;
use yii\caching\TagDependency;
use yii\db\Exception;
use yii\filters\AccessControl;
use yii\filters\auth\QueryParamAuth;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\web\ServerErrorHttpException;
use yii\web\UploadedFile;

/**
 * @OA\Schema(
 *     schema="Question",
 *     type="object",
 *     example={"question":{"id":76870,"created_at":1694602219,"updated_at":1695024389,"created_by":44,"updated_by":44,"poll_id":27095,"name":"Имя вопроса","description":"Текст вопроса","description_html":"Текст вопроса","sub_description":"Дополнительное описание вопроса","text":null,"rating_type":1,"detail_question":null,"is_self_answer":0,"type":"text","position":1,"is_tmp":0,"service_name":"Служебное имя вопроса","is_system":0,"is_condition":0,"is_source":0,"show_category":0,"show_name":0,"show_portion":0,"value_all":0,"min_sum":0,"is_updated":1,"point_id":null,"is_deleted":0,"main_question_type":16,"is_required":1,"comment_minlength":0,"comment_maxlength":255,"self_variant_minlength":0,"self_variant_maxlength":255,"comment_label":"","variants_element_type":1,"mask":null,"date_type":null,"file_type":null,"files_length":4,"comment_enabled":0,"comment_required":0,"company_id":null,"for_all_rates":0,"link_with_client_field":0,"linked_client_field":null,"rewrite_linked_field":0,"placeholder_text":"","select_placeholder_text":null,"self_variant_placeholder_text":null,"choose_type":null,"mask_config":null,"dont_show_if_answered":0,"answers_from":"2023-09-11 11:06:29","smile_type":null,"smiles_count":null,"matrix_settings":null,"min_choose_variants":null,"max_choose_variants":null,"self_variant_text":null,"dropdown_variants":0,"only_date_month":0,"random_variants_order":0,"skip":0,"skip_text":null,"skip_variant":0,"donor":null,"donor_chosen":1,"show_labels":0,"show_numbers":0,"from_one":0,"dictionary_sort":"default","dictionary_list_type":"list","dictionary_id":null,"dictionary_element_id":123,"max_points_calc_method":0,"deleted_detail_question":null,"set_variants":0,"extra_required":1,"poll_is_auto":0,"detail_answers":{},"gallery":{},"enableGallery":false,"images":{},"smiles":{},"chooseMedia":{},"videos":{},"quizzes":{},"addressCodes":{"regions":{},"cities":{},"districts":{},"streets":{}},"countAnswers":"0","placeholderText":"","selectPlaceholderText":null,"maskConfig":null,"questionLogic":{},"questionViewLogic":{},"pointName":null,"foquzQuestionEndScreenLogos":{{"id":666,"foquz_question_id":76870,"logo":"/uploads/foquz/contact-points/1694602716-8EbNSFCjy1eTZLG4Wwqr.png","description":"","position":1,"width":null,"height":null,"link":"","external_logo":null}},"isHaveExtra":false,"langs":{{"id":1862,"foquz_question_id":76870,"foquz_poll_lang_id":2373,"name":null,"description":"","description_html":"","sub_description":null,"placeholder_text":null,"select_placeholder_text":null,"self_variant_placeholder_text":null,"comment_label":"","skip_text":null,"detail_question":null,"self_variant_text":null,"labels":null,"text":null}},"intermediateBlock":{"id":9842,"question_id":76870,"screen_type":3,"show_question_number":0,"text":"<p>пупу</p>","complaint_button":1,"unsubscribe_button":1,"complaint_button_text":"Пожаловаться","unsubscribe_button_text":"Отписаться","poll_button_text":"","code":null,"pool_id":null,"ready_button":1,"ready_button_text":"Готово","close_widget_button":1,"close_widget_button_text":"Закрыть","external_link":"http://ya.ru","scores_button":1,"scores_button_text":"","start_over_button":1,"start_over_button_text":"Начать","logos_backcolor":"rgba(255, 255, 255, 1)","agreement":0,"agreement_text":"","socNetworks":{"id":9707,"intermediate_block_id":9842,"social_networks_enabled":0,"social_networks":{"vk":"1","ok":"1"},"form":"round","style":"style1","substrate":0,"total_counter":0,"location_for_total_counter":"before","for_each_counter":0,"location_for_each_counter":null,"size":"24","socialNetworks":{"vk":"1","ok":"1"}},"langs":{{"id":23,"setting_id":9842,"lang_id":2373,"text":"<p>Intermediate block text</p>","complaint_button_text":"Complaint","unsubscribe_button_text":"Unsubscribe","poll_button_text":null,"ready_button_text":"Ready","external_link":null,"scores_button_text":"Test report","start_over_button_text":"Start over","agreement_text":null,"pool_id":null}}}}},
 * )
 */
class QuestionsController extends ApiController
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['authenticator'] = [
            'class' => QueryParamAuth::className()
        ];
        $behaviors['verbs'] = [
            'class' => VerbFilter::className(),
            'actions' => [
                'update' => ['POST'],
                'copy' => ['POST'],
                'upload-youtube' => ['POST'],
                'delete-first-click-area' => ['DELETE'],
            ]
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'rules' => [
                [
                    'actions' => [
                        'create', 'update', 'image-upload', 'image-delete', 'upload-end-screen-logo', 'delete-end-screen-logo',
                        'upload-end-screen-logo-by-link', 'video-upload', 'upload-youtube', 'upload-by-link', 'change-label',
                        'copy', 'translate', 'prepare-details', 'upload-detail-file', 'upload-detail-files', 'upload-self-variant-file',
                        'update-first-click-area', 'delete-first-click-area',
                    ],
                    'allow' => true,
                    'roles' => ['foquz_admin', 'editor'],
                ],
                [
                    'actions' => [
                        'view'
                    ],
                    'allow' => true,
                    'roles' => ['foquz_admin', 'editor', 'filial_employee', 'foquz_watcher'],
                ],
            ],
        ];
        return $behaviors;
    }

    /**
     * @OA\Post (
     *     path="/foquz/api/questions/update",
     *     tags={"Вопросы"},
     *     summary="Обновление вопроса",
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="ID вопроса",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             encoding="text/plain",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="FoquzQuestion[point_id]",
     *                     description="ID точки контакта",
     *                     type="integer",
     *                     nullable=true,
     *                     example="123"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[poll_id]",
     *                     description="ID опроса",
     *                     type="integer",
     *                     nullable=false,
     *                     example="123"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[name]",
     *                     description="Название вопроса",
     *                     type="string",
     *                     maxLength=255,
     *                     nullable=true,
     *                     example="Имя вопроса"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[description]",
     *                     description="Текст вопроса",
     *                     type="string",
     *                     maxLength=65535,
     *                     nullable=true,
     *                     example="Текст вопроса"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[sub_description]",
     *                     description="Дополнительное описание",
     *                     type="string",
     *                     maxLength=500,
     *                     nullable=true,
     *                     example="Дополнительное описание вопроса"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[service_name]",
     *                     description="Служебное имя",
     *                     type="string",
     *                     maxLength=255,
     *                     nullable=true,
     *                     example="Служебное имя вопроса"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[main_question_type]",
     *                     description="ID типа вопроса:
     *                       0 - Оценка,
     *                       1 - Варианты ответов,
     *                       2 - Текстовый ответ,
     *                       3 - Дата и время,
     *                       4 - Адрес,
     *                       5 - Загрузка файла,
     *                       6 - Анкета,
     *                       7 - Звездный рейтинг для вариантов,
     *                       8 - Приоритет,
     *                       9 - Выбор изображения/видео,
     *                       10 - Рейтинг фото/видео галереи,
     *                       11 - Смайл-рейтинг,
     *                       12 - Рейтинг NPS,
     *                       13 - Простая матрица,
     *                       14 - Семантический дифференциал,
     *                       15 - Звездный рейтинг,
     *                       16 - Промежуточный блок,
     *                       17 - Выбор филиала,
     *                       18 - Рейтинг,
     *                       19 - Классификатор,
     *                       20 - Шкала,
     *                       21 - 3D матрица,
     *                       22 - Закрытая карточная сортировка,
     *                     ",
     *                     type="integer",
     *                     nullable=false,
     *                     example="15",
     *                     enum={0,1,2,3,4,5,6,7,8,9, 10,11,12,13,14,15,16,17,18,19,20,21,22}
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[is_required]",
     *                     description="Вопрос обязательный?",
     *                     nullable=false,
     *                     type="integer",
     *                     example="1",
     *                     enum={0,1}
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[is_self_answer]",
     *                     description="Можно указать свой вариант?",
     *                     nullable=false,
     *                     type="integer",
     *                     example="1",
     *                     enum={0,1}
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[dont_show_if_answered]",
     *                     description="Не отображать вопрос, если ответ уже был получен?",
     *                     nullable=false,
     *                     type="integer",
     *                     example="1",
     *                     enum={0,1}
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[answers_from]",
     *                     description="Дата начала приема ответов (кажется, нигде не используется)",
     *                     nullable=true,
     *                     type="string",
     *                     format="date",
     *                     example="2023-09-11"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[donor]",
     *                     description="ID вопроса-донора",
     *                     nullable=true,
     *                     type="integer",
     *                     example="12345",
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[comment_enabled]",
     *                     description="Комментарий включен?",
     *                     nullable=false,
     *                     type="integer",
     *                     example="1",
     *                     enum={0,1}
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[comment_required]",
     *                     description="Комментарий обазятелен?",
     *                     nullable=false,
     *                     type="integer",
     *                     example="1",
     *                     enum={0,1}
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[placeholder_text]",
     *                     description="Текст подсказки для поля ввода",
     *                     type="string",
     *                     maxLength=255,
     *                     nullable=true,
     *                     example="Текст подсказки"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[select_placeholder_text]",
     *                     description="Текст подсказки селекта",
     *                     type="string",
     *                     maxLength=255,
     *                     nullable=true,
     *                     example="Текст подсказки"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[self_variant_placeholder_text]",
     *                     description="Текст подсказки для поля Свой вариант",
     *                     type="string",
     *                     maxLength=255,
     *                     nullable=true,
     *                     example="Текст подсказки"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[self_variant_text]",
     *                     description="Название для варианта ответа Свой вариант",
     *                     type="string",
     *                     maxLength=255,
     *                     nullable=true,
     *                     example="Название варианта"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[self_variant_comment_required]",
     *                     description="Комментарий обязателен для варианта ответа Свой вариант?",
     *                     type="integer",
     *                     nullable=true,
     *                     example=1,
     *                     enum={0,1}
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[self_variant_nothing]",
     *                     description="Свой вариант с опцией 'Ничего из перечисленного'",
     *                     type="integer",
     *                     nullable=true,
     *                     example=1,
     *                     enum={0,1}
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[self_variant_description]",
     *                     description="Текст тултпиа для Своего варианта",
     *                     type="string",
     *                     nullable=true,
     *                     example="Текст тултипа",
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[min_choose_variants]",
     *                     description="Минимальное количество выбранных вариантов (Минимальное количество карточек для ЗКС)",
     *                     type="integer",
     *                     nullable=true,
     *                     example=1,
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[max_choose_variants]",
     *                     description="Максимальное количество выбранных вариантов (Максимальное количество карточек для ЗКС)",
     *                     type="integer",
     *                     nullable=true,
     *                     example=1,
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[comment_minlength]",
     *                     description="Минимальная длина комментария",
     *                     type="integer",
     *                     minimum=0,
     *                     maximum=500,
     *                     nullable=true,
     *                     example="0"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[comment_maxlength]",
     *                     description="Максимальная длина комментария",
     *                     type="integer",
     *                     minimum=0,
     *                     maximum=500,
     *                     nullable=true,
     *                     example="255"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[self_variant_minlength]",
     *                     description="Минимальная длина своего варианта",
     *                     type="integer",
     *                     minimum=0,
     *                     maximum=500,
     *                     nullable=true,
     *                     example="0"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[self_variant_maxlength]",
     *                     description="Максимальная длина своего варианта",
     *                     type="integer",
     *                     minimum=0,
     *                     maximum=500,
     *                     nullable=true,
     *                     example="255"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[comment_label]",
     *                     description="Название поля с комментарием",
     *                     type="string",
     *                     maxLength=120,
     *                     nullable=true,
     *                     example="Комментарий"
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[show_tooltips]",
     *                     title="Показывать подсказки для элементов и категорий справочника",
     *                     description="Показывать подсказки для элементов и категорий справочника (0 - нет, 1 - да)",
     *                     type="integer",
     *                     example=0,
     *                     enum={0,1}
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[dictionary_element_id]",
     *                     description="ID элемента справочника для вопроса",
     *                     type="integer",
     *                     example=132
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[max_points_calc_method]",
     *                     description="Метод расчета максимального количества баллов (0 - не учитывать в максимальном количестве баллов, если вопрос скрыт, 1 - учитывать в максимальном количестве баллов даже если вопрос скрыт)",
     *                     type="integer",
     *                     enum={0,1},
     *                     example=1
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[max_points_calc_recipient]",
     *                     title="Считать максимум баллов за вопрос, исходя из фактически унаследованных вариантов",
     *                     description="Считать максимум баллов за вопрос, исходя из фактически унаследованных вариантов",
     *                     type="integer",
     *                     enum={0,1},
     *                     example=1
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[random_variants_order]",
     *                     title="Случайный порядок вариантов ответа",
     *                     description="Случайный порядок вариантов ответа (0 - нет, 1 - да) Для закрытой карточной сортировки - случайный порядок карточек",
     *                     type="integer",
     *                     enum={0,1},
     *                     example=1
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[random_categories_order]",
     *                     title="Случайный порядок категория для закрытой карточной сортировки",
     *                     description="Случайный порядок категорий для закрытой карточной сортировки (0 - нет, 1 - да)",
     *                     type="integer",
     *                     enum={0,1},
     *                     example=1
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[start_point_color]",
     *                     title="Начальный цвет шкалы (рейтинг NPS)",
     *                     description="Начальный цвет шкалы (рейтинг NPS)",
     *                     type="string",
     *                     example="#FF0000",
     *                     maxLength=7
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[end_point_color]",
     *                     title="Конечный цвет шкалы (рейтинг NPS)",
     *                     description="Конечный цвет шкалы (рейтинг NPS)",
     *                     type="string",
     *                     example="#FF0000",
     *                     maxLength=7
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[start_label]",
     *                     title="Метка начальной точки шкалы (рейтинг NPS)",
     *                     description="Метка начальной точки шкалы (рейтинг NPS)",
     *                     type="string",
     *                     example="Начало",
     *                     maxLength=255
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[end_label]",
     *                     title="Метка конечной точки шкалы (рейтинг NPS)",
     *                     description="Метка конечной точки шкалы (рейтинг NPS)",
     *                     type="string",
     *                     example="Конец",
     *                     maxLength=255
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[from_one]",
     *                     title="Начинать шкалу с единицы",
     *                     description="Начинать шкалу с единицы (0 - нет, 1 - да)",
     *                     type="integer",
     *                     enum={0,1},
     *                     example=1
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[file_types]",
     *                     title="Типы файлов",
     *                     description="Типы файлов (audio - аудио, picture - изображение, video - видео)",
     *                     type="array",
     *                     @OA\Items(oneOf={
     *                         @OA\Schema(
     *                             type="string",
     *                             enum={"audio", "picture", "video"}
     *                         ),
     *                     }),
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[disable_select_category]",
     *                     title="Запретить выбор категории/подкатегории до разворачивания вложенных уровней",
     *                     description="Запретить выбор категории/подкатегории до разворачивания вложенных уровней (0 - нет, 1 - да)",
     *                     type="integer",
     *                     enum={0,1},
     *                     example=1
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[variants_with_files]",
     *                     title="Варианты ответов с изображениями/видео (плиткой)",
     *                     description="Варианты ответов с изображениями/видео (плиткой) (0 - нет, 1 - да)",
     *                     type="integer",
     *                     enum={0,1},
     *                     example=1
     *                 ),
     *                 @OA\Property(
     *                     property="FoquzQuestion[enableGallery]",
     *                     description="Галерея фото/видео включена?",
     *                     nullable=true,
     *                     type="integer",
     *                     example="1",
     *                     enum={0,1}
     *                 ),
     *                 @OA\Property(
     *                      property="FoquzQuestion[endScreenLogos][0][id]",
     *                      description="ID Изображения для начального/конечного экранов, полученное от метода /foquz/api/questions/upload-end-screen-logo",
     *                      nullable=false,
     *                      type="integer",
     *                      example=123,
     *                  ),
     *                  @OA\Property(
     *                      property="FoquzQuestion[endScreenLogos][0][link]",
     *                      description="Внешняя ссылка",
     *                      nullable=true,
     *                      type="string",
     *                      example="https://ya.ru/img.jpg",
     *                  ),
     *                  @OA\Property(
     *                      property="FoquzQuestion[endScreenLogos][0][width]",
     *                      description="Ширина изображения",
     *                      nullable=true,
     *                      type="integer",
     *                      minimum=0,
     *                      example=123,
     *                  ),
     *                  @OA\Property(
     *                      property="FoquzQuestion[endScreenLogos][0][height]",
     *                      description="Высота изображения",
     *                      nullable=true,
     *                      type="integer",
     *                      minimum=0,
     *                      example=123,
     *                  ),
     *                  @OA\Property(
     *                      property="FoquzQuestion[endScreenLogos][0][description]",
     *                      description="Описание изображения",
     *                      nullable=true,
     *                      type="string",
     *                      example="Здесь какое-то описание",
     *                  ),
     *                  @OA\Property(
     *                      property="FoquzQuestion[endScreenLogos][0][logo]",
     *                      description="Относительный URL загруженного файла",
     *                      nullable=true,
     *                      type="string",
     *                      example="/uploads/foquz/contact-points/1694598800-BQmYJOXv9nD7RrpHyg1T.png",
     *                  ),
     *                  @OA\Property(
     *                      property="FoquzQuestion[card_column_text]",
     *                      title="Наименование колонки с карточками",
     *                      description="Наименование колонки с карточками",
     *                      type="string",
     *                      nullable=true,
     *                      example="Карточки",
     *                      maxLength=125
     *                  ),
     *                  @OA\Property(
     *                      property="FoquzQuestion[category_column_text]",
     *                      title="Наименование колонки с категориями",
     *                      description="Наименование колонки с категориями",
     *                      type="string",
     *                      nullable=true,
     *                      example="Категории",
     *                      maxLength=125
     *                   ),
     *                  @OA\Property(
     *                      property="FoquzQuestion[skip]",
     *                      title="Пропуск ответа",
     *                      description="Пропуск ответа",
     *                      type="boolean",
     *                      nullable=false,
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[screen_type]",
     *                      description="Тип промежуточного экрана:
     *                          1 - Текстовый блок,
     *                          2 - Стартовый экран,
     *                          3 - Конечный экран
     *                          4 - Тест 5 секунд
     *                      ",
     *                      nullable=false,
     *                      type="integer",
     *                      example=1,
     *                      enum={1,2,3}
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[show_question_number]",
     *                      description="Отображать номер вопроса",
     *                      nullable=true,
     *                      default=0,
     *                      type="integer",
     *                      example=1,
     *                      enum={0,1}
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[text]",
     *                      description="Текст на странице",
     *                      maxLength=65535,
     *                      nullable=false,
     *                      type="string",
     *                      example="Текст на странице"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[complaint_button]",
     *                      description="Отображать сссылку «Пожаловаться»",
     *                      nullable=true,
     *                      default=0,
     *                      type="integer",
     *                      example=0,
     *                      enum={0,1}
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[unsubscribe_button]",
     *                      description="Отображать сссылку «Отписаться от рассылки»",
     *                      nullable=true,
     *                      default=0,
     *                      type="integer",
     *                      example=0,
     *                      enum={0,1}
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[ready_button]",
     *                      description="Отображать кнопку «Готово»",
     *                      nullable=true,
     *                      default=0,
     *                      type="integer",
     *                      example=0,
     *                      enum={0,1}
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[start_over_button]",
     *                      description="Отображать кнопку «Начать заново»",
     *                      nullable=true,
     *                      default=0,
     *                      type="integer",
     *                      example=0,
     *                      enum={0,1}
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[scores_button]",
     *                      description="Отображать кнопку «Отчет о тестировании»",
     *                      nullable=true,
     *                      default=0,
     *                      type="integer",
     *                      example=0,
     *                      enum={0,1}
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[agreement]",
     *                      description="Отображать чекбокс для согласия респондента на продолжение (ПД)",
     *                      nullable=true,
     *                      default=0,
     *                      type="integer",
     *                      example=0,
     *                      enum={0,1}
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[complaint_button_text]",
     *                      description="Текст ссылки «Пожаловаться»",
     *                      maxLength=255,
     *                      nullable=true,
     *                      type="string",
     *                      example="Пожаловаться"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[unsubscribe_button_text]",
     *                      description="Текст ссылки «Отписаться от рассылки»",
     *                      maxLength=255,
     *                      nullable=true,
     *                      type="string",
     *                      example="Отписаться от рассылки"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[poll_button_text]",
     *                      description="Текст кнопки «пройти опрос»",
     *                      maxLength=255,
     *                      nullable=true,
     *                      type="string",
     *                      example="Пройти опрос"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[ready_button_text]",
     *                      description="Текст кнопки «Готово»",
     *                      maxLength=255,
     *                      nullable=true,
     *                      type="string",
     *                      example="Готово"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[close_widget_button_text]",
     *                      description="Текст кнопки «Закрыть» для виджета",
     *                      maxLength=255,
     *                      nullable=true,
     *                      type="string",
     *                      example="Закрыть"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[start_over_button_text]",
     *                      description="Текст кнопки «Начать заново»",
     *                      maxLength=255,
     *                      nullable=true,
     *                      type="string",
     *                      example="Начать заново"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[scores_button_text]",
     *                      description="Текст кнопки «Отчет о тестировании»",
     *                      maxLength=255,
     *                      nullable=true,
     *                      type="string",
     *                      example="Отчет о тестировании"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[external_link]",
     *                      description="Ссылка на внешний ресурс при клике на кнопку «Готово»",
     *                      maxLength=255,
     *                      nullable=true,
     *                      type="string",
     *                      example="Начать заново"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[agreement_text]",
     *                      description="Текст согласия респондента на продолжение (ПД)",
     *                      maxLength=255,
     *                      nullable=true,
     *                      type="string",
     *                      example="Я согласен/согласна на обработку персональных данных"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockSetting[logos_backcolor]",
     *                      description="Цвет подложки в формате RGBA",
     *                      nullable=false,
     *                      type="string",
     *                      example="rgba(255, 255, 255, 1)"
     *                  ),
     *                  @OA\Property(
     *                      property="IntermediateBlockNetworks",
     *                      title="Настройки иконок соцсетей для проомежуточного экрана",
     *                      type="object",
     *                      @OA\Property(
     *                          property="social_networks_enabled",
     *                          title="Отображать иконки соцсетей",
     *                          description="Отображать иконки соцсетей (0 - нет, 1 - да)",
     *                          type="integer",
     *                          nullable=false,
     *                          example=1,
     *                          enum={0,1}
     *                      ),
     *                      @OA\Property(
     *                          property="social_networks",
     *                          title="Список соцсетей",
     *                          type="object",
     *                          @OA\Property(
     *                              property="vk",
     *                              title="Отображать иконку ВКонтакте",
     *                              description="Отображать иконку ВКонтакте (0 - нет, 1 - да)",
     *                              type="integer",
     *                              nullable=false,
     *                              example=1,
     *                              enum={0,1}
     *                          ),
     *                          @OA\Property(
     *                              property="ok",
     *                              title="Отображать иконку Одноклассники",
     *                              description="Отображать иконку Одноклассники (0 - нет, 1 - да)",
     *                              type="integer",
     *                              nullable=false,
     *                              example=1,
     *                              enum={0,1}
     *                          ),
     *                      ),
     *                      @OA\Property(
     *                          property="form",
     *                          title="Форма иконок",
     *                          description="Форма иконок (square - квадратная, square_rounded - квадратная с закругленными краями, round - круглая)",
     *                          type="string",
     *                          nullable=false,
     *                          example="square",
     *                          enum={"square", "square_rounded", "round"}
     *                      ),
     *                      @OA\Property(
     *                          property="style",
     *                          title="Стиль иконок",
     *                          description="Стиль иконок (style1, style2, style3, style4)",
     *                          type="string",
     *                          nullable=false,
     *                          example="style1",
     *                          enum={"style1", "style2", "style3", "style4"}
     *                      ),
     *                      @OA\Property(
     *                          property="substrate",
     *                          title="Подложка",
     *                          description="Подложка (0 - нет, 1 - да)",
     *                          type="integer",
     *                          nullable=false,
     *                          example=0,
     *                          enum={0,1}
     *                      ),
     *                      @OA\Property(
     *                          property="total_counter",
     *                          title="Общий счетчик",
     *                          description="Отображать общий счетчик (0 - нет, 1 - да)",
     *                          type="integer",
     *                          nullable=false,
     *                          example=0,
     *                          enum={0,1}
     *                      ),
     *                      @OA\Property(
     *                          property="location_for_total_counter",
     *                          title="Расположение общего счетчика",
     *                          description="Расположение общего счетчика (before - перед, after - после)",
     *                          type="string",
     *                          nullable=false,
     *                          example="before",
     *                          enum={"before", "after"}
     *                      ),
     *                      @OA\Property(
     *                          property="for_each_counter",
     *                          title="Счетчик для каждой иконки",
     *                          description="Отображать счетчик для каждой иконки (0 - нет, 1 - да)",
     *                          type="integer",
     *                          nullable=false,
     *                          example=0,
     *                          enum={0,1}
     *                      ),
     *                      @OA\Property(
     *                          property="size",
     *                          title="Размер иконок",
     *                          type="integer",
     *                          nullable=false,
     *                          example=24,
     *                          enum={24, 32, 48}
     *                      ),
     *                  ),
     *                  @OA\Property(
     *                      property="FoquzQuestionDetail",
     *                      title="Варианты ответов на вопрос и уточняющий вопрос",
     *                      type="array",
     *                      @OA\Items(oneOf={
     *                          @OA\Schema(
     *                              type="object",
     *                              @OA\Property(
     *                                  property="id",
     *                                  title="ID варианта ответа",
     *                                  description="ID варианта ответа",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=123
     *                              ),
     *                              @OA\Property(
     *                                  property="value",
     *                                  title="Текст варианта ответа",
     *                                  description="Текст варианта ответа",
     *                                  type="string",
     *                                  maxLength=255,
     *                                  nullable=false,
     *                                  example="Текст варианта ответа"
     *                              ),
     *                              @OA\Property(
     *                                  property="points",
     *                                  title="Количество баллов за ответ",
     *                                  description="Количество баллов за ответ",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=5
     *                              ),
     *                              @OA\Property(
     *                                  property="without_points",
     *                                  title="Является ли вариантом ответа без балла",
     *                                  description="Является ли вариантом ответа без балла (0 - нет, 1 - да)",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=0,
     *                                  enum={0,1}
     *                              ),
     *                              @OA\Property(
     *                                  property="type",
     *                                  title="Тип варианта ответа",
     *                                  description="Тип варианта ответа: 0 - обычный, 1 - ничего из перечисленного",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=0,
     *                                  enum={0,1}
     *                              ),
     *                              @OA\Property(
     *                                  property="position",
     *                                  title="Вес сортировки",
     *                                  description="Вес сортировки",
     *                                  type="integer",
     *                                  nullable=false,
     *                                  example=1
     *                              ),
     *                              @OA\Property(
     *                                  property="random_exclusion",
     *                                  title="Исключить вариант из случайного порядка",
     *                                  description="Для вопросов типа Варианты ответов",
     *                                  type="integer",
     *                                  nullable=false,
     *                                  example=0,
     *                                  enum={0,1}
     *                              ),
     *                              @OA\Property(
     *                                  property="need_extra",
     *                                  title="Требуется ли уточняющий вопрос для этого ответа",
     *                                  description="Требуется ли уточняющий вопрос для этого ответа (0 - нет, 1 - да)",
     *                                  type="integer",
     *                                  nullable=false,
     *                                  example=1,
     *                                  enum={0,1}
     *                              ),
     *                              @OA\Property(
     *                                  property="extra_question",
     *                                  title="Является ли вариантом ответа на уточняющий вопрос",
     *                                  description="Является ли вариантом ответа на уточняющий вопрос (0 - нет, 1 - да)",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=1,
     *                                  enum={0,1}
     *                              ),
     *                              @OA\Property(
     *                                  property="dictionary_element_id",
     *                                  title="ID элемента справочника, связанного с вариантом ответа",
     *                                  description="ID элемента справочника, связанного с вариантом ответа",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=123
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[detail_question]",
     *                                  description="Текст уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)",
     *                                  type="string",
     *                                  maxLength=255,
     *                                  nullable=true
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[extra_question_rate_from]",
     *                                  description="Минимальная оценка для отображения уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=5
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[extra_question_rate_to]",
     *                                  description="Максимальная оценка для отображения уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=5
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[extra_required]",
     *                                  description="Уточняющий вопрос обязательный (для типа УВ Для каждого варианта свой вопрос)",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  default=1,
     *                                  example=1,
     *                                  enum={0,1}
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[min_choose_extra_variants]",
     *                                  description="Минимальное количество выбранных вариантов для уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=2,
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[max_choose_extra_variants]",
     *                                  description="Максимальное количество выбранных вариантов для уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=2,
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[variants_with_files]",
     *                                  description="Варианты ответов с изображениями/видео (плиткой) (для типа УВ Для каждого варианта свой вопрос)",
     *                                  type="integer",
     *                                  nullable=false,
     *                                  default=0,
     *                                  example=0,
     *                                  enum={0,1}
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[self_variant_text]",
     *                                  description="Текст своего варианта для уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)",
     *                                  maxLength=255,
     *                                  nullable=true,
     *                                  type="string",
     *                                  example="Начать заново"
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[self_variant_placeholder_text]",
     *                                  description="Плейсхолдер (для типа УВ Для каждого варианта свой вопрос)",
     *                                  maxLength=255,
     *                                  nullable=true,
     *                                  type="string",
     *                                  example="Начать заново"
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[variants_element_type]",
     *                                  description="Тип УВ (0 - один ответ, 1 - неск ответов, 2 - текстовый) (для типа УВ Для каждого варианта свой вопрос)",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  default=1,
     *                                  example=1,
     *                                  enum={0,1,2}
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionDetail[for_all_rates]",
     *                                  description="УВ для всех оценок (для типа УВ Для каждого варианта свой вопрос)",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  default=0,
     *                                  example=1,
     *                                  enum={0,1}
     *                              ),
     *                              @OA\Property(
     *                                   property="FoquzQuestionDetail[placeholder_text]",
     *                                   description="Текстовый вариант ответа на уточняющий вопрос (для типа УВ Для каждого варианта свой вопрос)",
     *                                   maxLength=255,
     *                                   nullable=true,
     *                                   type="string",
     *                                   example="Начать заново"
     *                               ),
     *                              @OA\Property(
     *                                   property="FoquzQuestionDetail[self_variant_minlength]",
     *                                   description="Минимальная длина своего ответа (для типа УВ Для каждого варианта свой вопрос)",
     *                                   type="integer",
     *                                   nullable=true,
     *                                   default=0
     *                               ),
     *                              @OA\Property(
     *                                    property="FoquzQuestionDetail[self_variant_maxlength]",
     *                                    description="Максимальная длина своего ответа (для типа УВ Для каждого варианта свой вопрос)",
     *                                    type="integer",
     *                                    nullable=true,
     *                                    default=250
     *                                ),
     *                              @OA\Property(
     *                                    property="FoquzQuestionDetail[text_variant_minlength]",
     *                                    description="Минимальная длина текстового ответа (для типа УВ Для каждого варианта свой вопрос)",
     *                                    type="integer",
     *                                    nullable=true,
     *                                    default=0
     *                                ),
     *                              @OA\Property(
     *                                     property="FoquzQuestionDetail[text_variant_manlength]",
     *                                     description="Максимальная длина текстового ответа (для типа УВ Для каждого варианта свой вопрос)",
     *                                     type="integer",
     *                                     nullable=true,
     *                                     default=250
     *                                 ),
     *                              @OA\Property(
     *                                     property="FoquzQuestionDetail[is_self_answer]",
     *                                     description="Можно указать свой вариант? (для типа УВ Для каждого варианта свой вопрос)",
     *                                     nullable=false,
     *                                     type="integer",
     *                                     example="1",
     *                                     enum={0,1}
     *                              ),
     *                              @OA\Property(
     *                                  property="detail_question_options",
     *                                  title="Варианты ответов на УВ для разных вопросов для каждого варианта",
     *                                  description="Варианты ответов на УВ для разных вопросов для каждого варианта",
     *                                  type="array",
     *                                  @OA\Items(allOf={
     *                                      @OA\Schema(
     *                                      type="object",
     *                                          @OA\Property(
     *                                              property="id",
     *                                              title="ID варианта ответа на УВ",
     *                                              description="ID варианта ответа на УВ",
     *                                              type="integer",
     *                                              nullable=true,
     *                                              example=123
     *                                          ),
     *                                          @OA\Property(
     *                                              property="value",
     *                                              title="Текст варианта ответа",
     *                                              description="Текст варианта ответа на УВ",
     *                                              type="string",
     *                                              maxLength=255,
     *                                              nullable=false,
     *                                              example="Текст варианта ответа на УВ"
     *                                          ),
     *                                          @OA\Property(
     *                                              property="position",
     *                                              title="Вес сортировки",
     *                                              description="Вес сортировки",
     *                                              type="integer",
     *                                              nullable=false,
     *                                              example=1
     *                                          ),
     *                                          @OA\Property(
     *                                              property="FoquzQuestionDetail[question_detail_id]",
     *                                              description="ID варианта опроса для варианта ответа на УВ",
     *                                              type="integer",
     *                                              nullable=true,
     *                                              example=97330,
     *                                          ),
     *                                      ),
     *                                  }),
     *                              ),
     *                          ),
     *                      }),
     *                  ),
     *                  @OA\Property(
     *                      property="starOptions",
     *                      title="Настройки рейтинга",
     *                      description="Настройки рейтинга (для вопросов типа Рейтинг, Звездный рейтинг)",
     *                      type="object",
     *                      @OA\Property(
     *                          property="color",
     *                          title="Цвет шкалы (звезд)",
     *                          description="Цвет шкалы (звезд)",
     *                          maxLength=7,
     *                          type="string",
     *                          nullable=false,
     *                          example="#FFD700"
     *                      ),
     *                      @OA\Property(
     *                          property="count",
     *                          title="Количество звезд (размер шкалы)",
     *                          description="Количество звезд (размер шкалы)",
     *                          type="integer",
     *                          nullable=false,
     *                          minimum=2,
     *                          maximum=10,
     *                          example=5
     *                      ),
     *                      @OA\Property(
     *                          property="labels",
     *                          title="Метки",
     *                          description="Метки (массив с метками для каждой звезды (оценки в рейтинге))",
     *                          type="array",
     *                          @OA\Items(oneOf={
     *                              @OA\Schema(
     *                                  type="string",
     *                                  maxLength=255,
     *                                  nullable=false,
     *                                  example="Метка"
     *                              ),
     *                          }),
     *                      ),
     *                  ),
     *                  @OA\Property(
     *                       property="FoquzQuestionCardSortingCategory",
     *                       title="Категории для опросов типа Закрытая карточная сортировка",
     *                       type="array",
     *                       @OA\Items(oneOf={
     *                           @OA\Schema(
     *                               type="object",
     *                               @OA\Property(
     *                                  property="FoquzQuestionCardSortingCategory[id]",
     *                                  description="ID категории",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  example=123,
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionCardSortingCategory[value]",
     *                                  description="Название категории",
     *                                  type="string",
     *                                  example="Фрукты",
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionCardSortingCategory[position]",
     *                                  description="Позиция в списке",
     *                                  type="integer",
     *                                  nullable=true,
     *                              ),
     *                              @OA\Property(
     *                                  property="FoquzQuestionCardSortingCategory[is_deleted]",
     *                                  description="Флаг удаления",
     *                                  type="integer",
     *                                  nullable=true,
     *                                  enum={0,1},
     *                              ),
     *                          ),
     *                      }),
     *                  ),
     *                  @OA\Property(
     *                        property="firstClick",
     *                        title="Параметры для опросов типа Тест первого клика",
     *                        type="array",
     *                        @OA\Items(oneOf={
     *                            @OA\Schema(
     *                                type="object",
     *                                @OA\Property(
     *                                   property="mobile_view",
     *                                   description="Отображение на сматрфоне 0-по ширине, 1-по высоте",
     *                                   type="integer",
     *                                   default=0,
     *                                   enum={0,1},
     *                               ),
     *                               @OA\Property(
     *                                   property="min_click",
     *                                   description="Min кол-во кликов",
     *                                   type="integer",
     *                                   maxLength=3,
     *                                   default=1
     *                               ),
     *                               @OA\Property(
     *                                   property="max_click",
     *                                   description="Max кол-во кликов",
     *                                   type="integer",
     *                                   nullable=true,
     *                                   maxLength=3,
     *                               ),
     *                               @OA\Property(
     *                                   property="show_time",
     *                                   description="Время показа изображения, секунд",
     *                                   type="integer",
     *                                   nullable=true,
     *                                   maxLength=5,
     *                               ),
     *                              @OA\Property(
     *                                   property="button_text",
     *                                   description="Текст кнопки",
     *                                   type="string",
     *                                   nullable=true,
     *                                   example="Показать изображение",
     *                                ),
     *                              @OA\Property(
     *                                   property="allow_cancel_click",
     *                                   description="Возможность отменить клик",
     *                                   type="integer",
     *                                   default=0,
     *                                   enum={0,1},
     *                               ),
     *                           ),
     *                       }),
     *                   ),
     *                   @OA\Property(
     *                       property="FirstClickArea",
     *                       title="Параметры области для опросов типа Тест первого клика",
     *                       type="array",
     *                       @OA\Items(oneOf={
     *                           @OA\Schema(
     *                               type="object",
     *                               @OA\Property(
     *                                  property="id",
     *                                  description="ID области (0-новая область)",
     *                                  type="integer",
     *                                  example=123,
     *                              ),
     *                              @OA\Property(
     *                                  property="height",
     *                                  description="Высота области, px",
     *                                  type="integer",
     *                                  example=50,
     *                              ),
     *                              @OA\Property(
     *                                  property="width",
     *                                  description="Ширина области, px",
     *                                  type="integer",
     *                                  example=50,
     *                              ),
     *                              @OA\Property(
     *                                  property="x_coord",
     *                                  description="X-координата области",
     *                                  type="integer",
     *                                  example=150,
     *                              ),
     *                              @OA\Property(
     *                                  property="y_coord",
     *                                  description="Y-координата области",
     *                                  type="integer",
     *                                  example=150,
     *                              ),
     *                              @OA\Property(
     *                                  property="name",
     *                                  description="Название области",
     *                                  type="string",
     *                                  example="Кнопка входа",
     *                              ),
     *                           ),
     *                        }),
     *                   ),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *           response=200,
     *           description="Success",
     *           @OA\JsonContent(
     *               example={"question":{"id":76870,"created_at":1694602219,"updated_at":1695024389,"created_by":44,"updated_by":44,"poll_id":27095,"name":"Имя вопроса","description":"Текст вопроса","description_html":"Текст вопроса","sub_description":"Дополнительное описание вопроса","text":null,"rating_type":1,"detail_question":null,"is_self_answer":0,"type":"text","position":1,"is_tmp":0,"service_name":"Служебное имя вопроса","is_system":0,"is_condition":0,"is_source":0,"show_category":0,"show_name":0,"show_portion":0,"value_all":0,"min_sum":0,"is_updated":1,"point_id":null,"is_deleted":0,"main_question_type":16,"is_required":1,"comment_minlength":0,"comment_maxlength":255,"self_variant_minlength":0,"self_variant_maxlength":255,"comment_label":"","variants_element_type":1,"mask":null,"date_type":null,"file_type":null,"files_length":4,"comment_enabled":0,"comment_required":0,"company_id":null,"for_all_rates":0,"link_with_client_field":0,"linked_client_field":null,"rewrite_linked_field":0,"placeholder_text":"","select_placeholder_text":null,"self_variant_placeholder_text":null,"choose_type":null,"mask_config":null,"dont_show_if_answered":0,"answers_from":"2023-09-11 11:06:29","smile_type":null,"smiles_count":null,"matrix_settings":null,"min_choose_variants":null,"max_choose_variants":null,"self_variant_text":null,"dropdown_variants":0,"only_date_month":0,"random_variants_order":0,"skip":0,"skip_text":null,"skip_variant":0,"donor":null,"donor_chosen":1,"show_labels":0,"show_numbers":0,"from_one":0,"dictionary_sort":"default","dictionary_list_type":"list","dictionary_id":null,"dictionary_element_id":123,"max_points_calc_method":0,"deleted_detail_question":null,"set_variants":0,"extra_required":1,"poll_is_auto":0,"detail_answers":{},"gallery":{},"enableGallery":false,"images":{},"smiles":{},"chooseMedia":{},"videos":{},"quizzes":{},"addressCodes":{"regions":{},"cities":{},"districts":{},"streets":{}},"countAnswers":"0","placeholderText":"","selectPlaceholderText":null,"maskConfig":null,"questionLogic":{},"questionViewLogic":{},"pointName":null,"foquzQuestionEndScreenLogos":{{"id":666,"foquz_question_id":76870,"logo":"/uploads/foquz/contact-points/1694602716-8EbNSFCjy1eTZLG4Wwqr.png","description":"","position":1,"width":null,"height":null,"link":"","external_logo":null}},"isHaveExtra":false,"langs":{{"id":1862,"foquz_question_id":76870,"foquz_poll_lang_id":2373,"name":null,"description":"","description_html":"","sub_description":null,"placeholder_text":null,"select_placeholder_text":null,"self_variant_placeholder_text":null,"comment_label":"","skip_text":null,"detail_question":null,"self_variant_text":null,"labels":null,"text":null}},"intermediateBlock":{"id":9842,"question_id":76870,"screen_type":3,"show_question_number":0,"text":"<p>пупу</p>","complaint_button":1,"unsubscribe_button":1,"complaint_button_text":"Пожаловаться","unsubscribe_button_text":"Отписаться","poll_button_text":"","code":null,"pool_id":null,"ready_button":1,"ready_button_text":"Готово","close_widget_button":1,"close_widget_button_text":"Закрыть","external_link":"http://ya.ru","scores_button":1,"scores_button_text":"","start_over_button":1,"start_over_button_text":"Начать","logos_backcolor":"rgba(255, 255, 255, 1)","agreement":0,"agreement_text":"","socNetworks":{"id":9707,"intermediate_block_id":9842,"social_networks_enabled":0,"social_networks":{"vk":"1","ok":"1"},"form":"round","style":"style1","substrate":0,"total_counter":0,"location_for_total_counter":"before","for_each_counter":0,"location_for_each_counter":null,"size":"24","socialNetworks":{"vk":"1","ok":"1"}},"langs":{{"id":23,"setting_id":9842,"lang_id":2373,"text":"<p>Intermediate block text</p>","complaint_button_text":"Complaint","unsubscribe_button_text":"Unsubscribe","poll_button_text":null,"ready_button_text":"Ready","external_link":null,"scores_button_text":"Test report","start_over_button_text":"Start over","agreement_text":null,"pool_id":null}}}}},
     *           )
     *     ),
     *     @OA\Response(
     *            response=400,
     *            description="Error",
     *            @OA\JsonContent(
     *               type="object",
     *               @OA\Property(
     *                   property="errors",
     *                   description="Ошибки",
     *                   type="array",
     *                   @OA\Items(
     *                       type="string",
     *                       description="Текст ошибки",
     *                   ),
     *               ),
     *           ),
     *      ),
     * ),
     */
    public function actionUpdate($id, $asContactPoint = false)
    {
        ini_set('memory_limit', '1000M');

        /** @var FoquzQuestion $model */
        $model = $this->findModel($id);

        $post_data = \Yii::$app->request->post();


        $service = new QuestionSaveService($model);
        $data = $service->saveQuestion($post_data, $id, $asContactPoint);
        TagDependency::invalidate(Yii::$app->cache, 'quiz-poll-' . $model->poll->id);
        TagDependency::invalidate(Yii::$app->cache, 'foquz-question-details-' . $model->id);


        if (!empty($data['error'])) {
            return  $data;
        }
        if (!empty($data['errors'])) {
            return  $data;
        }
        if (!isset($data['response'])) {
            return $data;
        }

        $response = $data['response'];
        $model = $data['model'];

        return !empty($response) ? $response : ['question' => $model];
    }


    public function actionImageUpload($id = null, $fileId = null, $langId = null, $logoId = null, $scenario = null)
    {
        $file = null;
        if ($fileId) {
            $file = FoquzQuestionFile::findOne($fileId);
            if (!$file) {
                throw new NotFoundHttpException('Original file not found');
            }
            $basePath = "uploads/foquz/$file->question_id";
        } elseif ($logoId) {
            $file = FoquzQuestionEndScreenLogo::findOne($logoId);
            if (!$file) {
                throw new NotFoundHttpException('Original file not found');
            }
            $basePath = "uploads/foquz/$file->foquz_question_id";
        } elseif ($id) {
            $questionModel = FoquzQuestion::findOne($id);
            $basePath = "uploads/foquz/{$id}";
        } else {
            $basePath = "uploads/foquz/contact-points";
        }
        $model = new UploadForm();
        $model->scenario = UploadForm::SCENARIO_PICTURE;
        if ($scenario === 'first_click') {
            $model->scenario = UploadForm::SCENARIO_PICTURE_FIRST_CLICK;
        }


        $fullPath = \Yii::getAlias("@app/web/{$basePath}");

        if (false === file_exists($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        $model->file = UploadedFile::getInstance($model, 'file');
        if($model->file === null) {
            $model->file = UploadedFile::getInstanceByName('file');
        }
        if ($model->file && $model->validate()) {
            $fileName = uniqid(). '.' . $model->file->extension;
            if ($model->file->saveAs($fullPath . '/' . $fileName)) {
                if ($langId && $file) {
                    $pollId = is_a($file, FoquzQuestionFile::class) ? $file->question->poll->id : $file->foquzQuestion->poll->id;
                    $foquzPollLang = FoquzPollLang::findOne(['foquz_poll_id' => $pollId, 'poll_lang_id' => $langId]);
                    if (!$foquzPollLang) {
                        throw new NotFoundHttpException('Язык для опроса не найден');
                    }
                    $fileLang = FoquzQuestionFileLang::findOne([
                        'foquz_poll_lang_id' => $foquzPollLang->id,
                        'foquz_question_file_id' => $fileId,
                        'end_screen_logo_id' => $logoId,
                    ]);
                    if (!$fileLang) {
                        $fileLang = new FoquzQuestionFileLang();
                        $fileLang->foquz_question_file_id = $fileId;
                        $fileLang->end_screen_logo_id = $logoId;
                        $fileLang->foquz_poll_lang_id = $foquzPollLang->id;
                    }
                    $fileLang->file_full_path = $fullPath . '/' . $fileName;
                    $fileLang->file_path = $basePath . '/' . $fileName;
                    if ($fileLang->save()) {
                        return $fileLang;
                    }
                } else {
                    $modelFoquz = new FoquzQuestionFile();
                    $modelFoquz->file_full_path = $fullPath . '/' . $fileName;
                    $modelFoquz->file_path = $basePath . '/' . $fileName;
                    $modelFoquz->question_id = $id;
                    if ($id) {
                        $modelFoquz->file_text = ArrayHelper::getValue(FoquzQuestionImage::$abc, FoquzQuestion::findOne($id)->getQuestionImages()->count() + 1, 'Z');
                        $questionModel->type = FoquzQuestion::TYPE_IMAGE;
                        $questionModel->save();
                    } else {
                        $modelFoquz->file_text = \Yii::$app->request->post('label');
                    }
                    $modelFoquz->save();
                    return $this->response(200, [
                        'success' => true,
                        'data' => [
                            'id' => $modelFoquz->id,
                            'image' => $basePath . '/' . $fileName,
                            'file_text' => $modelFoquz->file_text,
                        ]
                    ]);
                }
            } else {
                $errors = array_map(static function($item) {
                    return str_replace('МиБ', 'МБ', $item);
                }, $model->getErrors());
                return $this->response(400, ['errors' => $errors]);
            }
        }
        $errors = array_map(static function($item) {
            return str_replace('МиБ', 'МБ', $item);
        }, $model->getErrors());
        return $this->response(400, ['errors' => $errors]);
    }

    public function actionImageDelete($id)
    {
        $model = FoquzQuestionFile::findOne($id);
        $model->delete();

        return [
            'questionId' => $model->question_id,
        ];
    }

    /**
     * @OA\Post (
     *     path="/foquz/api/questions/upload-end-screen-logo",
     *     tags={"Вопросы"},
     *     summary="Загрузка изображения для стартового и конечного экранов",
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="logo",
     *                     description="Файл изображения",
     *                     type="file",
     *                     nullable=false,
     *                 ),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Success",
     *         @OA\JsonContent(
     *              type="object",
     *              @OA\Property(
     *                  property="foquz_question_id",
     *                  description="ID вопроса",
     *                  type="integer",
     *                  nullable=true,
     *                  example=456,
     *              ),
     *              @OA\Property(
     *                  property="id",
     *                  description="ID загруженного файла",
     *                  type="integer",
     *                  nullable=false,
     *                  example=123,
     *              ),
     *              @OA\Property(
     *                  property="logo",
     *                  description="Относительный URL загруженного файла",
     *                  type="string",
     *                  nullable=false,
     *                  example="/uploads/foquz/contact-points/1694598800-BQmYJOXv9nD7RrpHyg1T.png",
     *              ),
     *         ),
     *
     *     ),
     * ),
     */
    public function actionUploadEndScreenLogo($id = null)
    {
        if($id) {
            $questionModel = FoquzQuestion::findOne($id);
            if(!$questionModel or $questionModel->poll->company_id != \Yii::$app->user->identity->company->id) {
                throw new NotFoundHttpException();
            }
            $basePath = "uploads/foquz/end-screen-logos/{$id}";
        } else {
            $basePath = "uploads/foquz/contact-points";
        }

        $webDir = \Yii::getAlias('@app/web');
        $relativePath = $basePath;
        if(!file_exists($webDir.'/'.$relativePath)) {
            mkdir($webDir.'/'.$relativePath, 0777, true);
        }
        $image = UploadedFile::getInstanceByName('logo');
        if($image) {
            if($image->size > 5*1024*1024) {
                return ['error' => 'Размер файла не должен превышать 5 МБ'];
            }
            if(!in_array($image->getExtension(), ['jpeg', 'jpg', 'png', 'gif', 'svg'])) {
                return ['error' => 'Файл может быть только в форматах: jpg, jpeg, png, gif, svg'];
            }
            $fileName = time().'-'.substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'),1, 20).'.'.$image->extension;
            if($image->saveAs($webDir.'/'.$relativePath.'/'.$fileName)) {
                $endScreenLogo = new FoquzQuestionEndScreenLogo();
                $endScreenLogo->logo = '/'.$relativePath.'/'.$fileName;
                $endScreenLogo->foquz_question_id = $id;
                $endScreenLogo->save();
                return $endScreenLogo;
            } else {
                return ['error' => 'Не удалось загрузить изображение'];
            }
        } else {
            return [];
        }
    }

    public function actionDeleteEndScreenLogo($id)
    {
        if(($logoModel = FoquzQuestionEndScreenLogo::findOne($id))) {
            $webDir = \Yii::getAlias('@app/web');
            @unlink($webDir.'/'.$logoModel->logo);
            if($logoModel->delete()) {
                return [];
            }
        }
        throw new NotFoundHttpException();
    }

    public function actionUploadEndScreenLogoByLink($id = null)
    {
        $link = \Yii::$app->request->post('externalLogo');

        if(!$link) {
            return $this->response(400, ['errors' => [
                'externalLogo' => 'Необходимо передавать параметр'
            ]
            ]);
        }
        if($id) {
            $questionModel = FoquzQuestion::findOne($id);
            if(!$questionModel) {
                throw new NotFoundHttpException();
            }
        }

        $attachment_content = $link;

        $model = new FoquzQuestionEndScreenLogo([
            'foquz_question_id' => $id,
            'external_logo' => $attachment_content,
        ]);
        if($model->save()) {
            return $this->response(201, [
                'media' => [
                    'id' => $model->id,
                    'external_logo' => $model->external_logo
                ]
            ]);
        } else {
            return $this->response(400, ['errors' => $model->errors]);
        }
    }

    public function actionVideoUpload($id = null, $fileId = null, $langId = null)
    {
        $model = new VideoUploadForm();

        if($id) {
            $basePath = "uploads/foquz/{$id}";
        } else {
            $basePath = "uploads/foquz/contact-points";
        }
        $fullPath = \Yii::getAlias("@app/web/{$basePath}");

        if (false === file_exists($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        $model->file = UploadedFile::getInstance($model, 'file');
        if($model->file === null) {
            $model->file = UploadedFile::getInstanceByName('file');
        }
        if ($model->file && $model->validate()) {
            $fileName = uniqid() . '.' . $model->file->extension;
            if ($model->file->saveAs($fullPath . '/' . $fileName)) {
                if ($langId) {
                    $fileLang = new FoquzQuestionFileLang();
                    $fileLang->file_full_path = $fullPath . '/' . $fileName;
                    $fileLang->file_path = $basePath . '/' . $fileName;
                    $fileLang->foquz_question_file_id = $fileId;
                    $fileLang->foquz_poll_lang_id = $langId;
                    if ($fileLang->save()) {
                        return $fileLang;
                    }
                } else {
                    $modelFoquz = new FoquzQuestionFile();
                    $modelFoquz->file_full_path = $fullPath . '/' . $fileName;
                    $modelFoquz->file_path = $basePath . '/' . $fileName;
                    $modelFoquz->question_id = $id;
                    $modelFoquz->type = 'video';
                    if ($id) {
                        $modelFoquz->file_text = ArrayHelper::getValue(FoquzQuestionFile::$abc, FoquzQuestion::findOne($id)->getQuestionVideos()->count() + 1, 'Z');
                    } else {
                        $modelFoquz->file_text = \Yii::$app->request->post('label');
                    }
                    $ffmpeg = FFMpeg::create([
                        'ffmpeg.binaries' => \Yii::$app->params['ffmpeg_binaries'],
                        'ffprobe.binaries' => \Yii::$app->params['ffprobe_binaries']
                    ]);
                    $video = $ffmpeg->open($fullPath . '/' . $fileName);
                    $video
                        ->frame(TimeCode::fromSeconds(5))
                        ->save($fullPath . '/' . $fileName . '.jpg');

                    $modelFoquz->save();
                    return $this->response(201, [
                        'success' => true,
                        'data' => [
                            'model' => [
                                'id' => $modelFoquz->id,
                                'file_text' => $modelFoquz->file_text,
                                'image' => '/' . $basePath . '/' . $fileName . '.jpg',
                                'link' => '/' . $modelFoquz->file_path
                            ]
                        ]
                    ]);
                }
            }
        } else {
            return $this->response(400, ['errors' => $model->getErrors()]);
        }

        return $this->response(400, ['errors' => ['message' => 'wrong params']]);
    }

    public function actionUploadYoutube($id = null)
    {
        $model = new YouTubeVideoForm(['question' => FoquzQuestion::findOne($id), 'label' => \Yii::$app->request->post('label')]);
        $questionModel = FoquzQuestion::findOne($id);

        if ($model->load(\Yii::$app->getRequest()->post()) && $model->validate()) {
            if($questionModel) {
                $questionModel->type = FoquzQuestion::TYPE_VIDEO;
                $questionModel->save();
            }
            return $this->response(200, ['data' => ['model' => $model->handle()]]);
        } else {
            var_dump($model->load(\Yii::$app->getRequest()->post()));
            die();
        }

        //return ['errors' => ['message' => $model->errors]];
    }

    public function actionUploadByLink($id = null, $fileId = null, $langId = null)
    {
        $link = \Yii::$app->request->post('link');

        if(!$link) {
            return $this->response(400, ['errors' => [
                    'link' => 'Необходимо передавать параметр'
                ]
            ]);
        }
        $questionModel = FoquzQuestion::findOne($id);

        if (preg_match('/(https:\/\/www\.youtube\.com\/live\/|https:\/\/youtu\.be\/)(.*?)\?si=/', $link)) {
            $type = 'video';
            $attachment_content = $link;
        } elseif (stristr($link, 'https://www.youtube.com/watch?v=') || stristr($link, 'https://youtu.be/')) {
            $type = 'video';
            $attachment_content = str_replace(['https://www.youtube.com/watch?v=', 'https://youtu.be/'], '', $link);
        } else {
            $type = 'image';
            $attachment_content = $link;
        }

        if ($langId) {
            $model = new FoquzQuestionFileLang([
                'foquz_question_file_id' => $fileId,
                'foquz_poll_lang_id' => $langId,
                'link' => $link,
            ]);
            if ($model->save()) {
                return $model;
            }
        } else {
            $model = new FoquzQuestionFile([
                'question_id' => $id,
                'file_path' => '/',
                'file_full_path' => '/',
                'attachment_type' => 'link',
                'attachment_content' => $attachment_content,
                'type' => $type,
                'file_text' => $questionModel ? ArrayHelper::getValue(FoquzQuestionFile::$abc, $questionModel->getQuestionVideos()->count() + 1, 'Z') : \Yii::$app->request->post('label')
            ]);
        }
        if($model->save()) {
            return $this->response(201, [
                'media' => [
                    'id' => $model->id,
                    'file_text' => $model->file_text,
                    'type' => $model->type,
                    'attachment_type' => $model->attachment_type,
                    'image' => $model->getImage(),
                    'link' => $model->link
                ]
            ]);
        } else {
            return $this->response(400, ['errors' => $model->errors]);
        }
    }

    public function actionChangeLabel($id)
    {
        $model = FoquzQuestionFile::findOne($id);
        $model->file_text = \Yii::$app->request->post('label');
        $model->save();
    }

    public function actionCopy($id, $pageId = null)
    {
        $sourceQuestion = $this->findModel($id);
        if(!$sourceQuestion || $sourceQuestion->poll->is_auto) {
            return $this->response(400, ['errors' => ['id' => 'Не удалось получить данные вопроса']]);
        }

        $newQuestion = $sourceQuestion->copy([
            'created_at' => time(),
            'updated_at' => time(),
            'created_by' => \Yii::$app->user->id,
            'updated_by' => \Yii::$app->user->id,
            'poll_id' => $sourceQuestion->poll_id,
            'position' => FoquzQuestion::find()->where(['poll_id' => $sourceQuestion->poll_id])->max('position') + 1
        ]);
        $newQuestion = FoquzQuestion::findOne($newQuestion->id);

        if($sourceQuestion->poll->point_system) {
            $sourceQuestion->poll->max_points = $sourceQuestion->poll->calculatePoints();
            $sourceQuestion->poll->save();
        }

        if($pageId) {
            \Yii::$app->db->createCommand()->insert('foquz_poll_display_page_questions', [
                'display_page_id' => $pageId,
                'question_id' => $newQuestion->id
            ])->execute();
        }

        return $this->response(200, [
            'items' => [
                'source' => $sourceQuestion,
                'new' => $newQuestion
            ]
        ]);
    }

    public function actionView($id)
    {
        return $this->findModel($id);
    }

    /**
     * @param int $id
     * @param int $langId
     * @return FoquzQuestionLang|array|null
     * @throws Exception
     * @throws NotFoundHttpException
     */
    public function actionTranslate(int $id, int $langId)
    {
        $translateService = new TranslateService(Yii::$app->user->identity->company->id);
        return $translateService->translateQuestion(
            Yii::$app->request->post(),
            $id,
            $langId
        );
    }

    public function actionPrepareDetails(): Response
    {
        $oldDetails = ArrayHelper::index(Yii::$app->request->post('FoquzQuestionDetail'), 'value');
        $oldDetails = array_filter($oldDetails, static function ($detail) {
            return !empty($detail['value']);
        });
        $newDetails = preg_split("/[\n|\r]+/", Yii::$app->request->post('newDetails'));
        $replaceDetails = (bool) Yii::$app->request->post('replaceDetails');
        $filteredNewDetails = [];

        foreach ($newDetails as $detail) {
            $detail = trim($detail);
            if (!empty($detail)) {
                $filteredNewDetails[] = $detail;
            }
        }
        $filteredNewDetails = array_values(array_unique($filteredNewDetails));

        $details = [];
        if ($replaceDetails) {
            $oldDetails = array_filter($oldDetails, static function ($detail) use ($filteredNewDetails) {
                return in_array($detail['value'], $filteredNewDetails);
            });
        } else {
            $details = $oldDetails;
        }

        foreach ($filteredNewDetails as $newDetail) {
            if (isset($oldDetails[$newDetail])) {
                $details[$newDetail] = $oldDetails[$newDetail];
                continue;
            }

            $details[$newDetail] = [
                'id' => '0',
                'need_extra' => 1,
                'extra_question' => 0,
                'is_deleted' => 0,
                'points' => null,
                'position' => 0,
                'variant' => $newDetail,
                'dictionary_element_id' => null,
            ];
        }

        $i = 1;
        foreach ($details as $key => $detail) {
            if (isset($detail['value'])) {
                $details[$key]['variant'] = $detail['value'];
                unset($details[$key]['value']);
            }
            $details[$key]['is_deleted'] = 0;
            $details[$key]['position'] = $i++;
            $details[$key]['id'] = (string) $detail['id'];
            $details[$key]['need_extra'] = (int) $detail['need_extra'];
            $details[$key]['dictionary_element_id'] = (int) $detail['dictionary_element_id'];
            $details[$key]['points'] = $detail['points'] ? (int) $detail['points'] : null;
        }
        $details = array_values($details);
        return $this->asJson(['detail_answers' => $details]);
    }

    /**
     * @throws \yii\base\Exception
     * @throws NotFoundHttpException|\Random\RandomException
     */
    public function actionUploadDetailFile()
    {
        $file = UploadedFile::getInstanceByName('file');
        if (!$file) {
            return $this->response(400, ['errors' => ['file' => 'Необходимо передать файл']]);
        }

        $service = new FileService(FoquzFile::TYPE_DETAIL, 0, $file);

        if ($file = $service->upload()) {
            return $this->response(200, ['file' => $file->toArray()]);
        }

        return $this->response(400, ['errors' => $service->getErrors()]);
    }

    /**
     * @throws \yii\base\Exception
     * @throws NotFoundHttpException|\Random\RandomException
     */
    public function actionUploadSelfVariantFile()
    {
        $file = UploadedFile::getInstanceByName('file');
        if (!$file) {
            return $this->response(400, ['errors' => ['file' => 'Необходимо передать файл']]);
        }

        $service = new FileService(FoquzFile::TYPE_SELF_VARIANT, 0, $file);

        if ($file = $service->upload()) {
            return $this->response(200, ['file' => $file->toArray()]);
        }

        return $this->response(400, ['errors' => $service->getErrors()]);
    }

    public function actionUploadDetailFiles()
    {
        $files = UploadedFile::getInstancesByName('files');
        if (!$files) {
            return $this->response(400, ['errors' => ['files' => 'Необходимо передать файлы']]);
        }
        $fileModels = [];
        try {
            foreach ($files as $file) {
                $service = new FileService(FoquzFile::TYPE_DETAIL, 0, $file);
                $fileModel = $service->upload();
                if (!$fileModel) {
                    throw new BadRequestHttpException(json_encode($service->getErrors()));
                }
                $fileModels[] = $fileModel->toArray();
            }
            return $this->response(200, ['files' => $fileModels]);
        } catch (BadRequestHttpException $e) {
            return $this->response(400, ['errors' => json_decode($e->getMessage(), true)]);
        } catch (\Exception $e) {
            return $this->response(400, ['errors' => ['message' => 'Не удалось загрузить файлы']]);
        }
    }

    private function findModel(int $id, array $relations = [])
    {
        $alias = FoquzQuestion::tableName();
        $modelQuery = FoquzQuestion::find()
            ->alias($alias)
            ->where([$alias . '.id' => $id]);
        $model = !empty($relations)
            ? $modelQuery->joinWith($relations)->one()
            : $modelQuery->one();

        if(!$model){
            $poll_id = \Yii::$app->getRequest()->get('pollId');
            if($poll_id){
                $model1 = FoquzPoll::findOne($poll_id);
                //var_dump($model1->getViewUrl());exit;
                $this->redirect($model1->getViewUrl());
                \Yii::$app->end();
            }
        }

        if ($model !== null) {
            $companyId = \Yii::$app->user->identity->company->id;
            if ($companyId !== $model->poll->company_id || $model->poll->status === FoquzPoll::STATUS_ARCHIVE || ($model->poll->is_tmp && $model->poll->updated_at !== $model->poll->created_at)) {
                throw new NotFoundHttpException();
            }
            return $model;
        }

        throw new NotFoundHttpException('Вопрос не найден');
    }


}
