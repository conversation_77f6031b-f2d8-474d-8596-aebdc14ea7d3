<?php

namespace app\modules\foquz\controllers\api;

use Yii;
use yii\filters\Cors;
use yii\rest\Controller;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "widgetTriggers",
    properties: [
        new OA\Property(
            property: "cookies",
            title: "Таргетинг на cookie",
            type: "array",
            items: new OA\Items(
                oneOf: [new OA\Schema(ref: '#/components/schemas/widgetTriggersCookies')],
            ),
        ),
        new OA\Property(
            property: "devices",
            title: "Тип устройства",
            properties: [
                new OA\Property(
                    property: "desktop",
                    title: "Компьютеры",
                    description: "Компьютеры (0 - Нет, 1 - Да)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
                new OA\Property(
                    property: "tablet",
                    title: "Планшеты",
                    description: "Планшеты (0 - Нет, 1 - Да)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
                new OA\Property(
                    property: "smartphone",
                    title: "Смартфоны",
                    description: "Смартфоны (0 - Нет, 1 - Да)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
            ],
            type: "object",
        ),
        new OA\Property(
            property: "coverage",
            title: "Процент охвата (целое число)",
            type: "string",
            maxLength: 3,
            example: "50",
        ),
        new OA\Property(
            property: "url",
            title: "Условия для URL",
            properties: [
                new OA\Property(
                    property: "hashtag",
                    title: "Учитывать хэштег в URL",
                    description: "Учитывать хэштег в URL (0 - Нет, 1 - Да)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
                new OA\Property(
                    property: "conditions",
                    title: "Условия",
                    type: "array",
                    items: new OA\Items(
                        oneOf: [new OA\Schema(ref: '#/components/schemas/widgetTriggersUrlConditions')],
                    ),
                ),
                new OA\Property(
                    property: "visits_count",
                    title: "Количество переходов на страницу",
                    description: "Количество переходов на страницу",
                    properties: [
                        new OA\Property(
                            property: "active",
                            title: "Условие активно",
                            description: "Условие активно (0 - Нет, 1 - Да)",
                            type: "string",
                            enum: ["0", "1"],
                            example: "1",
                        ),
                        new OA\Property(
                            property: "value",
                            title: "Количество переходов",
                            description: "Количество переходов",
                            type: "string",
                            maxLength: 5,
                            example: "10",
                        ),
                        new OA\Property(
                            property: "flush_counter",
                            title: "Обнулять счетчик",
                            description: "Обнулять счетчик (0 - Нет, 1 - Да)",
                            type: "string",
                            enum: ["0", "1"],
                            example: "1",
                        ),
                    ],
                    type: "object",
                ),
                new OA\Property(
                    property: "time",
                    title: "Время после последнего перехода на страницу",
                    description: "Время после последнего перехода на страницу",
                    properties: [
                        new OA\Property(
                            property: "active",
                            title: "Условие активно",
                            description: "Условие активно (0 - Нет, 1 - Да)",
                            type: "string",
                            enum: ["0", "1"],
                            example: "1",
                        ),
                        new OA\Property(
                            property: "value",
                            title: "Время после последнего перехода на страницу",
                            description: "Время после последнего перехода на страницу",
                            type: "string",
                            maxLength: 5,
                            example: "10",
                        ),
                        new OA\Property(
                            property: "timer_type",
                            title: "Тип таймера",
                            description: "Тип таймера (1 - Таймер на страницу)",
                            type: "string",
                            enum: ["1"],
                            example: "1",
                        ),
                    ],
                    type: "object",
                ),
                new OA\Property(
                    property: "track_history_changes",
                    title: "Отслеживать изменение истории для одностроничного приложения",
                    description: "Отслеживать изменение истории для одностроничного приложения (0 - Нет, 1 - Да)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
            ],
            type: "object",
        ),
        new OA\Property(
            property: "client_tags",
            title: "По тегу контакта",
            type: "array",
            items: new OA\Items(
                oneOf: [new OA\Schema(ref: '#/components/schemas/widgetTriggersClientTags')],
            ),
        ),
        new OA\Property(
            property: "event",
            title: "По событию",
            type: "array",
            items: new OA\Items(
                oneOf: [new OA\Schema(ref: '#/components/schemas/widgetTriggersEvent')],
            ),
        ),
        new OA\Property(
            property: "variables",
            title: "Таргетинг на переменные",
            type: "array",
            items: new OA\Items(
                oneOf: [new OA\Schema(ref: '#/components/schemas/widgetTriggersVariables')],
            ),
        ),
        new OA\Property(
            property: "scroll_depth",
            title: "Таргетинг по глубине прокрутки страницы",
            type: "array",
            items: new OA\Items(
                oneOf: [new OA\Schema(ref: '#/components/schemas/widgetScrollDepth')],
            ),
        ),
    ],
    type: "object",
)]
#[OA\Schema(
    schema: "widgetTriggersCookies",
    properties: [
        new OA\Property(
            property: "name",
            title: "Наименование cookie",
            type: "string",
            example: "name",
        ),
        new OA\Property(
            property: "name_condition",
            title: "Условие фильтрации по имени cookie",
            description: "Условие фильтрации по имени cookie (1 - Равен, 2 - Включает, 3 - Исключает, 4 - Начинается с, 5 - Заканчивается на, 6 - Регулярное выражение)",
            type: "string",
            enum: ["1", "2", "3", "4", "5", "6"],
            example: "1",
        ),
        new OA\Property(
            property: "with_value",
            title: "Значение (с или без значения)",
            description: "Значение (0 - Без значения, 1 - Со значением)",
            type: "string",
            enum: ["0", "1"],
            example: "1",
        ),
        new OA\Property(
            property: "value_condition",
            title: "Условие фильтрации по значению cookie",
            description: "Условие фильтрации по значению cookie (1 - Равно, 2 - Включает, 3 - Исключает, 4 - Начинается с, 5 - Заканчивается на, 6 - Регулярное выражение)",
            type: "string",
            enum: ["1", "2", "3", "4", "5", "6"],
            example: "1",
        ),
        new OA\Property(
            property: "value",
            title: "Значение cookie",
            type: "string",
            example: "value",
        ),
    ],
    type: "object",
)]
#[OA\Schema(
    schema: "widgetTriggersUrlConditions",
    properties: [
        new OA\Property(
            property: "condition",
            title: "Условие",
            description: "Условие (1 - Равно, 2 - Включает, 3 - Исключает, 4 - Начинается с, 5 - Заканчивается на, 6 - Регулярное выражение)",
            type: "string",
            enum: ["1", "2", "3", "4", "5", "6"],
            example: "1",
        ),
        new OA\Property(
            property: "value",
            title: "Значение",
            type: "string",
            example: "https://example.com",
        ),
    ],
    type: "object",
)]
#[OA\Schema(
    schema: "widgetTriggersClientTags",
    properties: [
        new OA\Property(
            property: "condition",
            title: "Условие для тега",
            description: "Условие для тега (1 - Включить контакты с тегами, 2 - Исключить контакты с тегами)",
            type: "string",
            enum: ["1", "2"],
            example: "1",
        ),
        new OA\Property(
            property: "value",
            title: "ID тегов",
            type: "array",
            items: new OA\Items(
                description: "ID тега",
                type: "string",
                example: "55572",
            ),
        ),
    ],
    type: "object",
)]
#[OA\Schema(
    schema: "widgetTriggersEvent",
    properties: [
        new OA\Property(
            property: "conditions",
            title: "Условия",
            type: "array",
            items: new OA\Items(
                oneOf: [new OA\Schema(ref: '#/components/schemas/widgetTriggersEventConditions')],
            ),
        ),
        new OA\Property(
            property: "count",
            title: "Количество срабатываний события, после которого показать виджет",
            description: "Количество срабатываний события, после которого показать виджет",
            properties: [
                new OA\Property(
                    property: "active",
                    title: "Условие активно",
                    description: "Условие активно (0 - Нет, 1 - Да)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
                new OA\Property(
                    property: "value",
                    title: "Количество срабатываний",
                    description: "Количество срабатываний",
                    type: "string",
                    maxLength: 5,
                    example: "10",
                ),
                new OA\Property(
                    property: "flush_counter",
                    title: "Обнулять счетчик",
                    description: "Обнулять счетчик (0 - Нет, 1 - Да)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
            ],
            type: "object",
        ),
        new OA\Property(
            property: "time",
            title: "Через N секунд после срабатывания события",
            description: "Через N секунд после срабатывания события",
            properties: [
                new OA\Property(
                    property: "active",
                    title: "Условие активно",
                    description: "Условие активно (0 - Нет, 1 - Да)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
                new OA\Property(
                    property: "value",
                    title: "Количество секунд после срабатывания события",
                    description: "Количество секунд после срабатывания события",
                    type: "string",
                    maxLength: 5,
                    example: "10",
                ),
                new OA\Property(
                    property: "timer_type",
                    title: "Тип таймера",
                    description: "Тип таймера (1 - Таймер на страницу)",
                    type: "string",
                    enum: ["1"],
                    example: "1",
                ),
            ],
            type: "object",
        ),
    ],
    type: "object",
)]
#[OA\Schema(
    schema: "widgetTriggersEventConditions",
    properties: [
        new OA\Property(
            property: "condition",
            title: "Условие для наименования события",
            description: "Условие для наименования события (1 - Равно, 2 - Включает, 3 - Исключает, 4 - Начинается с, 5 - Заканчивается на, 6 - Регулярное выражение)",
            type: "string",
            enum: ["1", "2", "3", "4", "5", "6"],
            example: "1",
        ),
        new OA\Property(
            property: "value",
            title: "Значение",
            type: "string",
            example: "makeOrder",
        ),
    ],
    type: "object",
)]
#[OA\Schema(
    schema: "widgetTriggersVariables",
    properties: [
        new OA\Property(
            property: "name",
            title: "Наименование переменной",
            type: "string",
            example: "name",
        ),
        new OA\Property(
            property: "name_condition",
            title: "Условие для наименования переменной",
            description: "Условие для наименования переменной (1 - Равен, 2 - Включает, 3 - Исключает, 4 - Начинается с, 5 - Заканчивается на, 6 - Регулярное выражение)",
            type: "string",
            enum: ["1", "2", "3", "4", "5", "6"],
            example: "1",
        ),
        new OA\Property(
            property: "with_value",
            title: "Значение (с или без значения)",
            description: "Значение (0 - Без значения, 1 - Со значением)",
            type: "string",
            enum: ["0", "1"],
            example: "1",
        ),
        new OA\Property(
            property: "value_condition",
            title: "Условие фильтрации по значению переменной",
            description: "Условие фильтрации по значению переменной (1 - Равно, 2 - Включает, 3 - Исключает, 4 - Начинается с, 5 - Заканчивается на, 6 - Регулярное выражение)",
            type: "string",
            enum: ["1", "2", "3", "4", "5", "6"],
            example: "1",
        ),
        new OA\Property(
            property: "value",
            title: "Значение переменной",
            type: "string",
            example: "value",
        ),
    ],
    type: "object",
)]
#[OA\Schema(
    schema: "widgetTriggersStatus",
    properties: [
        new OA\Property(
            property: "cookies",
            title: "Таргетинг на cookie включен",
            description: "Таргетинг на cookie включен (0 - Нет, 1 - Да)",
            type: "string",
            enum: ["0", "1"],
            example: "1",
        ),
        new OA\Property(
            property: "devices",
            title: "Тип устройства включен",
            description: "Тип устройства включен (0 - Нет, 1 - Да)",
            type: "string",
            enum: ["0", "1"],
            example: "1",
        ),
        new OA\Property(
            property: "coverage",
            title: "Доля пользователей включена",
            description: "Доля пользователей включена (0 - Нет, 1 - Да)",
            type: "string",
            enum: ["0", "1"],
            example: "1",
        ),
        new OA\Property(
            property: "url",
            title: "Пользователи на конкретных страницах включен",
            description: "Пользователи на конкретных страницах включен (0 - Нет, 1 - Да)",
            type: "string",
            enum: ["0", "1"],
            example: "1",
        ),
        new OA\Property(
            property: "client_tags",
            title: "Таргетинг по тегу контакта включен",
            description: "Таргетинг по тегу контакта включен (0 - Нет, 1 - Да)",
            type: "string",
            enum: ["0", "1"],
            example: "1",
        ),
        new OA\Property(
            property: "event",
            title: "Таргетинг по событию включен",
            description: "Таргетинг по событию включен (0 - Нет, 1 - Да)",
            type: "string",
            enum: ["0", "1"],
            example: "1",
        ),
        new OA\Property(
            property: "variables",
            title: "Таргетинг по переменным включен",
            description: "Таргетинг по переменным включен (0 - Нет, 1 - Да)",
            type: "string",
            enum: ["0", "1"],
            example: "1",
        ),
    ],
    type: "object",
)]
#[OA\Schema(
    schema: "widgetWindowSettings",
    properties: [
        new OA\Property(
            property: "position",
            title: "Расположение окна",
            description: "left-bottom (слева внизу), right-bottom (справа внизу), left-top (слева вверху), right-top (справа вверху), left-center (слева по центру), right-center (справа по центру), center-center (посредине)",
            type: "string",
            enum: ["left-bottom", "right-bottom", "left-top", "right-top", "left-center", "right-center", "center-center"],
            example: "left-bottom",
        ),
        new OA\Property(
            property: "height",
            title: "Высота окна в %",
            type: "string",
            example: "40",
        ),
        new OA\Property(
            property: "width",
            title: "Ширина окна",
            description: "Ширина окна",
            properties: [
                new OA\Property(
                    property: "type",
                    title: "Единица измерения",
                    description: "(0 - проценты, 1 - пиксели)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
                new OA\Property(
                    property: "value",
                    title: "Значение ширины окна",
                    description: "Значение ширины окна",
                    type: "string",
                    example: "640",
                ),
            ],
            type: "object",
        ),
        new OA\Property(
            property: "round",
            title: "Радиус скругления углов",
            description: "Радиус скругления углов",
            properties: [
                new OA\Property(
                    property: "type",
                    title: "Единица измерения",
                    description: "(0 - проценты, 1 - пиксели)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
                new OA\Property(
                    property: "value",
                    title: "Значение радиуса",
                    description: "Целые положительные числа и ноль",
                    type: "string",
                    example: "40",
                ),
            ],
            type: "object",
        ),
    ],
    type: "object",
)]
#[OA\Schema(
    schema: "widgetScrollDepth",
    properties: [
        new OA\Property(
            property: "vertical",
            title: "Глубина вертикальной прокрутки",
            description: "Глубина вертикальной прокрутки",
            properties: [
                new OA\Property(
                    property: "type",
                    title: "Единица измерения",
                    description: "(0 - проценты, 1 - пиксели)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
                new OA\Property(
                    property: "value",
                    title: "Значение глубины вертикальной прокрутки",
                    description: "Значение глубины вертикальной прокрутки (целое число или целые числа через запятую)",
                    type: "string",
                    example: "15,20,30",
                ),
            ],
            type: "object",
        ),
        new OA\Property(
            property: "horizontal",
            title: "Глубина горизонтальной прокрутки",
            description: "Глубина горизонтальной прокрутки",
            properties: [
                new OA\Property(
                    property: "type",
                    title: "Единица измерения",
                    description: "(0 - проценты, 1 - пиксели)",
                    type: "string",
                    enum: ["0", "1"],
                    example: "1",
                ),
                new OA\Property(
                    property: "value",
                    title: "Значение глубины горизонтальной прокрутки",
                    description: "Значение глубины горизонтальной прокрутки (целое число или целые числа через запятую)",
                    type: "string",
                    example: "15",
                ),
            ],
            type: "object",
        ),
    ]
)]
#[OA\Schema(
    schema: "pollWidgetItem",
    properties: [
        new OA\Property(
            property: "is_active",
            title: "Виджет активен",
            description: "Виджет активен (0 - Нет, 1 - Да)",
            type: "integer",
            enum: [0, 1],
            example: 1,
        ),
        new OA\Property(
            property: "poll_id",
            title: "ID опроса",
            description: "ID опроса",
            type: "integer",
            example: 42432,
        ),
        new OA\Property(
            property: "name",
            title: "Название виджета",
            description: "Название виджета",
            type: "string",
            example: "Новый виджет",
        ),
        new OA\Property(
            property: "filial_id",
            title: "ID филиала",
            description: "ID филиала",
            type: "integer",
            example: 2,
            nullable: true,
        ),
        new OA\Property(
            property: "appearance",
            title: "Появление виджета",
            description: "Появление виджета 0-без клика, 1-по клику",
            type: "integer",
            enum: [0, 1],
            example: 1,
        ),
        new OA\Property(
            property: "form",
            title: "Форма виджета",
            description: "Форма виджета 0-кнопка, 1-Шкала оценок, 2-Всплывающее окно, 3-Page-stop, 4-Упрощенный Page-stop, 5-Hello-board",
            type: "integer",
            enum: [0, 1, 2, 3, 4, 5],
            example: 1,
        ),
        new OA\Property(
            property: "button_type",
            title: "Тип кнопки",
            description: "Тип кнопки 0-Текст, 1-Иконка, 2-Шкала",
            type: "integer",
            enum: [0, 1, 2],
            example: 1,
        ),
        new OA\Property(
            property: "position",
            title: "Позиция виджета",
            description: "Позиция виджета 0-Фиксированное положение, 1-Плавающая слева, 2-Плавающая справа, 3-Внизу слева, 4-Внизу справа",
            type: "integer",
            enum: [0, 1, 2, 3, 4],
            example: 1,
        ),
        new OA\Property(
            property: "button_text",
            title: "Текст кнопки",
            description: "Текст кнопки",
            type: "string",
            example: "Кнопка",
        ),
        new OA\Property(
            property: "font",
            title: "Шрифт",
            description: "Шрифт",
            type: "string",
            example: "Arial, Helvetica, sans-serif",
        ),
        new OA\Property(
            property: "font_size",
            title: "Размер шрифта",
            description: "Размер шрифта в пикселях",
            type: "integer",
            example: 16,
        ),
        new OA\Property(
            property: "italic",
            title: "Курсивный шрифт",
            description: "Курсивный шрифт 0-нет, 1-да",
            type: "integer",
            enum: [0, 1],
            example: 1,
        ),
        new OA\Property(
            property: "text_color",
            title: "Цвет текста",
            description: "Цвет текста",
            type: "string",
            example: "rgb(0, 0, 0)",
        ),
        new OA\Property(
            property: "background_color",
            title: "Цвет фона кнопки",
            description: "Цвет фона кнопки",
            type: "string",
            example: "rgb(255, 255, 255)",
        ),
        new OA\Property(
            property: "stroke",
            title: "Обводка",
            description: "Обводка 0-нет, 1-да",
            type: "integer",
            enum: [0, 1],
            example: 0,
        ),
        new OA\Property(
            property: "simple",
            title: "Упрощенный вид опроса",
            description: "Упрощенный вид опроса 0-нет, 1-да",
            type: "integer",
            enum: [0, 1],
            example: 0,
        ),
        new OA\Property(
            property: "show_until",
            title: "Логика отображения виджета",
            description: "Логика отображения виджета (0 - не показывать если кнопка / опрос показан, 1 - не показывать если опрос показан, 2 - не показывать если респондент ответил на хотя бы один вопрос, 3 - не показывать если респондент заполнил опрос до конца, 4 - показывать всегда)",
            type: "integer",
            enum: [0,1,2,3,4],
            example: 0,
        ),
        new OA\Property(
            property: "close_by_finish_button",
            title: "Закрывать виджет по кнопке «Завершить»",
            description: "Закрывать виджет по кнопке «Завершить»",
            type: "integer",
            enum: [0, 1],
            example: 0,
        ),
        new OA\Property(
            property: "priority",
            title: "Приоритет показа виджета",
            description: "Приоритет показа виджета",
            type: "integer",
            nullable: true,
        ),
        new OA\Property(
            property: "autoclose_delay",
            title: "Автоматически закрыть виджет после завершения опроса через N секунд",
            type: "integer",
            maximum: 999,
            minimum: 0,
            nullable: true,
        ),
        new OA\Property(
            property: "targeting",
            title: "Настроить таргетинг",
            description: "Настроить таргетинг 0-нет, 1-да",
            type: "integer",
            enum: [0, 1],
            example: 0,
        ),
        new OA\Property(
            property: "triggers",
            title: "Триггеры",
            description: "Триггеры",
            type: "array",
            items: new OA\Items(
                oneOf: [new OA\Schema(ref: '#/components/schemas/widgetTriggers')],
            ),
        ),
        new OA\Property(
            property: "triggers_status",
            title: "Статус таргетингов",
            description: "Статус таргетингов",
            type: "array",
            items: new OA\Items(
                oneOf: [new OA\Schema(ref: '#/components/schemas/widgetTriggersStatus')],
            ),
        ),
        new OA\Property(
            property: "filial",
            title: "Филиал",
            description: "Филиал",
            properties: [
                new OA\Property(
                    property: "id",
                    title: "ID филиала",
                    description: "ID филиала",
                    type: "integer",
                ),
                new OA\Property(
                    property: "name",
                    title: "Название филиала",
                    description: "Название филиала",
                    type: "string",
                ),
            ],
            type: "object",
        ),
        new OA\Property(
            property: "code",
            title: "Код виджета",
            description: "Код виджета",
            type: "string",
            example: "746d1df7e69b7567707c0849c9e11c40",
        ),
        new OA\Property(
            property: "poll_name",
            title: "Название опроса",
            description: "Название опроса",
            type: "string",
            example: "Анкета, NPS",
        ),
        new OA\Property(
            property: "window_settings",
            title: "Настройки окна",
            description: "Настройки окна",
            type: "array",
            items: new OA\Items(
                oneOf: [new OA\Schema(ref: '#/components/schemas/widgetWindowSettings')],
            ),
        ),
        new OA\Property(
            property: "scroll_depth",
            title: "Глубина прокрутки для таргетинга",
            description: "Глубина прокрутки для таргетинга",
            type: "array",
            items: new OA\Items(
                oneOf: [new OA\Schema(ref: '#/components/schemas/widgetScrollDepth')],
            ),
        ),
    ],
    type: "object",

)]
/*#[OA\Schema(
    schema: "pollLinkResponse",
    properties: [
        new OA\Property(
            property: "quote_id",
            description: "Id квоты на ссылку",
            type: "integer",
            example: 455,
            nullable: true,
        ),
        new OA\Property(
            property: "filial_id",
            description: "Id филиала",
            type: "integer",
            example: 455,
            nullable: true,
        ),
        new OA\Property(
            property: "filial_name",
            description: "Название филиала",
            type: "string",
            example: "Рязань",
            nullable: false,
        ),
        new OA\Property(
            property: "filial_category_id",
            description: "Id категории филиала",
            type: "integer",
            example: 105,
            nullable: true,
        ),
        new OA\Property(
            property: "key",
            description: "Ключ для ссылки на опрос",
            type: "string",
            example: "F42236476674d81f157ccf",
            nullable: false,
        ),
        new OA\Property(
            property: "link",
            description: "Ссылка на опрос",
            type: "string",
            example: "https://localhost:8081/p/F674d81f13c067",
            nullable: false,
        ),
        new OA\Property(
            property:"qr",
            description: "Ссылка на формирование qr-кода",
            type: "string",
            nullable: false,
        ),
        new OA\Property(
            property: "limit",
            description: "Лимит кол-ва ответов",
            type: "integer",
            example: 10000,
            nullable: true,
        ),
        new OA\Property(
            property: "datetime_end",
            description: "Дата-время когда закончить приём ответов в формате ISO 8601",
            type: "string",
            example: "2024-12-04T17:00:00+03:00",
            nullable: true,
        ),
        new OA\Property(
            property: "link_name",
            description: "Название ссылки",
            type: "string",
            example: "Ссылка 1",
            nullable: false,
        ),
        new OA\Property(
            property: "active",
            description: "Активность ссылки",
            type: "integer",
            enum: [0,1],
            example: 1,
            nullable: false,
        ),
        new OA\Property(
            property: "short_link",
            description: "Короткая ссылка",
            type: "string",
            example: "https://doxswf.ru/q73wvuo",
            nullable: false,
        ),
        new OA\Property(
            property: "answer_quotes",
            title: "Квоты на ответы",
            description: "Квоты на ответы",
            properties: [
                new OA\Property(
                    property: "id",
                    title: "Id квоты на ответ",
                    type: "integer",
                    nullable: false,
                ),
                new OA\Property(
                    property: "link_quote_id",
                    title: "Id квоты на ссылку",
                    type: "integer",
                    nullable: false,
                ),
                new OA\Property(
                    property: "answer_limit",
                    title: "Лимит кол-ва ответов",
                    type: "integer",
                    nullable: true,
                ),
                new OA\Property(
                    property: "logic_operation",
                    title: "Логика работы условий/групп",
                    description: "Логика работы условий/групп 0-через И, 1-через ИЛИ",
                    type: "integer",
                    enum: [0,1],
                ),
                new OA\Property(
                    property: "groups",
                    title: "Группа условий",
                    description: "Группа условий",
                    properties: [
                        new OA\Property(
                            property: "id",
                            title: "Id группы",
                            type: "integer",
                            nullable: false,
                        ),
                        new OA\Property(
                            property: "quote_id",
                            title: "Id квоты на ответ",
                            type: "integer",
                            nullable: false,
                        ),
                        new OA\Property(
                            property: "logic_operation",
                            title: "Логика работы условий в группе",
                            description: "Логика работы условий в группе 0-через И, 1-через ИЛИ",
                            type: "integer",
                            enum: [0,1],
                        ),
                        new OA\Property(
                            property: "criteria",
                            title: "Условие в группе",
                            properties: [
                                new OA\Property(
                                    property: "id",
                                    title: "Id условия",
                                    type: "integer",
                                    nullable: false,
                                ),
                                new OA\Property(
                                    property: "quote_id",
                                    title: "Id квоты на ответ",
                                    type: "integer",
                                    nullable: false,
                                ),
                                new OA\Property(
                                    property: "group_id",
                                    title: "Id группы",
                                    type: "integer",
                                    nullable: true,
                                ),
                                new OA\Property(
                                    property: "question_id",
                                    title: "Id вопроса",
                                    type: "integer",
                                    nullable: false,
                                ),
                                new OA\Property(
                                    property: "behavior",
                                    title: "Действие респондента",
                                    description: "Действие респондента 1-Выбрал вариант, 2-Не выбрал вариант, 3-Пропустил вопрос, 4-Затруднился ответить",
                                    type: "integer",
                                    enum: [1,2,3,4],
                                    nullable: false,
                                ),
                                new OA\Property(
                                    property: "variants",
                                    title: "Варианты ответов",
                                    type: "array",
                                    items: new OA\Items(
                                        description: "ID варианта или -1 если выбран свой вариант",
                                        type: "integer",
                                        example: 96480,
                                    ),
                                )
                            ]
                        )
                    ]
                ),
                new OA\Property(
                    property: "criteria",
                    title: "Условие",
                    properties: [
                        new OA\Property(
                            property: "id",
                            title: "Id условия",
                            type: "integer",
                            nullable: false,
                        ),
                        new OA\Property(
                            property: "quote_id",
                            title: "Id квоты на ответ",
                            type: "integer",
                            nullable: false,
                        ),
                        new OA\Property(
                            property: "group_id",
                            title: "Id группы",
                            type: "integer",
                            nullable: true,
                        ),
                        new OA\Property(
                            property: "question_id",
                            title: "Id вопроса",
                            type: "integer",
                            nullable: false,
                        ),
                        new OA\Property(
                            property: "behavior",
                            title: "Действие респондента",
                            description: "Действие респондента 1-Выбрал вариант, 2-Не выбрал вариант, 3-Пропустил вопрос, 4-Затруднился ответить",
                            type: "integer",
                            enum: [1,2,3,4],
                            nullable: false,
                        ),
                        new OA\Property(
                            property: "variants",
                            title: "Варианты ответов",
                            type: "array",
                            items: new OA\Items(
                                description: "ID варианта или -1 если выбран свой вариант",
                                type: "integer",
                                example: 96480,
                            ),
                        )
                    ]
                )
            ],
            type: "object",
        )
    ],
    type: "object",
)]*/
class TempController extends Controller
{

}