<?php

namespace app\modules\foquz\controllers;

use app\modules\foquz\models\ContactAdditionalField;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPointItem;
use app\modules\foquz\models\FoquzPointSelected;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollQuestionViewLogic;
use app\modules\foquz\models\FoquzPollStatsLink;
use app\modules\foquz\models\FoquzQuestionAddressCodes;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionFile;
use app\modules\foquz\models\FoquzQuestionFormField;
use app\modules\foquz\models\UploadForm;
use app\modules\foquz\models\VideoUploadForm;
use Yii;
use app\modules\foquz\models\FoquzQuestion;
use yii\caching\TagDependency;
use yii\data\ActiveDataProvider;
use yii\filters\AccessControl;
use yii\filters\auth\QueryParamAuth;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\BadRequestHttpException;
use yii\web\Controller;
use yii\web\ForbiddenHttpException;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;

/**
 * FoquzQuestionController implements the CRUD actions for FoquzQuestion model.
 */
class FoquzQuestionController extends Controller
{
    const SESSION_KEY = 'inputsVal';

    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => false,
                        'roles' => ['foquz_respondent'],
                    ],
                    [
                        'actions' => [
                            'create', 'change-type', 'save-file-input', 'delete', 'update-position'
                        ],
                        'allow' => true,
                        'roles' => ['foquz_admin', 'editor'],
                    ],
                    [
                        'actions' => [
                            'first', 'update', 'frame', 'dummy-content', 'question-images', 'question-videos'
                        ],
                        'allow' => true,
                        'roles' => ['foquz_admin', 'editor', 'filial_employee', 'foquz_watcher'],
                    ],
                    [
                        'actions' => ['contact-fields'],
                        'allow' => true,
                    ]
                ],
                'denyCallback' => function ($rule, $action) {
                    if(isset(\Yii::$app->user->identity) && \Yii::$app->user->identity->isExecutor()) {
                        return $this->redirect('/foquz/answers');
                    }  elseif(isset(\Yii::$app->user->identity)) {
                        throw new ForbiddenHttpException();
                    } else {
                        return $this->redirect('/user-management/auth/login');
                    }
                }
            ],
        ];
    }

    /**
     * Creates a new FoquzQuestion model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($pollId, $pageId = null, $pointId = null)
    {
        Yii::$app->getResponse()->format = Response::FORMAT_JSON;

        $poll = FoquzPoll::findOne($pollId);
        /** @var FoquzQuestion $question */
        $question = $poll->getFoquzQuestions()->orderBy(['id' => SORT_DESC])->one();
        if($question){
            switch ($question->type) {
                case $question::TYPE_TEXT:
                    if (
                        empty(ArrayHelper::getValue($question, 'name')) &&
                        empty(ArrayHelper::getValue($question, 'description')) &&
                        empty(ArrayHelper::getValue($question, 'text')) &&
                        $question->main_question_type !== FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
                        return ['success' => false, 'message' => 'Нельзя создавать пустые вопросы.'];
                    }
                    break;
                default:
                    if (
                        empty(ArrayHelper::getValue($question, 'name')) &&
                        empty(ArrayHelper::getValue($question, 'description')) &&
                        $question->main_question_type !== FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
                        return ['success' => false, 'message' => 'Нельзя создавать пустые вопросы.'];
                    }
                    break;
            }
        }

        $model = new FoquzQuestion(['poll_id' => $pollId, 'main_question_type' => FoquzQuestion::TYPE_STAR_RATING, 'rating_type' => FoquzQuestion::RATING_TYPE_START, 'is_tmp' => true]);

        if ($model->save()) {
            if($pageId) {
                Yii::$app->db->createCommand()->insert('foquz_poll_display_page_questions', [
                    'display_page_id' => $pageId,
                    'question_id' => $model->id
                ])->execute();
            }
            if (!Yii::$app->getRequest()->getIsAjax()) {
                return $this->redirect($model->poll->getViewUrl());
            }

            return [
                'question' => FoquzQuestion::findOne($model->id),
            ];
        }
    }

    public function actionFirst($id)
    {
        $model = FoquzPoll::findOne($id);
        if ($model) {
            return $this->redirect($model->getViewUrl());
        }
        throw new NotFoundHttpException();
    }

    /**
     * Updates an existing FoquzQuestion model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate()
    {
        $id = (int)Yii::$app->request->get('id');
        $pollId = (int)Yii::$app->request->get('pollId');

        if (!$id && !$pollId) {
            throw new BadRequestHttpException('Не переданы обязательные параметры');
        }

        if (!$id && $pollId) {
            $questionID = FoquzQuestion::find()
                ->select('foquz_question.id')
                ->joinWith('poll')
                ->where([
                    'foquz_poll.company_id' => Yii::$app->user->identity->company->id,
                    'foquz_question.poll_id' => $pollId,
                    'foquz_question.is_deleted' => 0,
                ])
                ->orderBy(['foquz_question.position' => SORT_ASC])
                ->scalar();

            if (!$questionID) {
                throw new NotFoundHttpException();
            }

            return $this->redirect(['update', 'id' => $questionID, 'pollId' => $pollId]);
        }

        if ($id && !$pollId) {
            $model = $this->findModel($id);
            return $this->redirect(['update', 'id' => $id, 'pollId' => $model->poll_id]);
        }

        $poll = FoquzPoll::find()
            ->with(['displayPages.displayPageQuestions.question'])
            ->where(['id' => $pollId, 'company_id' => Yii::$app->user->identity->company->id, 'deleted' => 0])
            ->one();

        if (!$poll) {
            throw new NotFoundHttpException('Poll not found');
        }

        if (Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher()) {
            $editorFoldersIds = FoquzPoll::getEditorFolderIdsAll(Yii::$app->user->identity->id);
            if (!$poll->folder_id || !in_array($poll->folder_id, $editorFoldersIds['folders'])) {
                throw new ForbiddenHttpException();
            }
        }

        $questions = FoquzQuestion::find()
            ->with(FoquzQuestion::getNecessaryRelations($poll->id))
            ->where(['poll_id' => $poll->id, 'is_deleted' => 0])
            ->orderBy(['position' => SORT_ASC])
            ->all();


        $points_count = 0;

        return $this->render('update', [
            'questions' => $questions,
            'poll' => $poll,
            'question_id' => $id,
            'points_count'=>$points_count
        ]);
    }

    public function actionDummyContent($id)
    {
        $answerForm = new FoquzPollAnswerItem();

        $model = $this->findModel($id);
        $model->setScenario($model::SCENARIO_UPDATE);
        if ($model->type === $model::TYPE_IMAGE) {
            $model->setScenario($model::SCENARIO_UPDATE_IMAGE);
        } elseif ($model->type === $model::TYPE_IMAGE) {
            $model->setScenario($model::SCENARIO_UPDATE_VIDEO);
        }

        $detailQuestions = $model->questionDetails ?: [new FoquzQuestionDetail()];

        return $this->renderPartial('_dummy_content', [
            'poll' => $model->poll,
            'model' => $model,
            'answerForm' => $answerForm,
            'detailQuestions' => $detailQuestions,
        ]);
    }

    /**
     * Updates an existing FoquzQuestion model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @param string  $type
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionChangeType($id, $type)
    {
        Yii::$app->getSession()->remove(self::SESSION_KEY);
        $model = $this->findModel($id);
        if(!$model){
            $poll_id = Yii::$app->getRequest()->get('pollId');
            if($poll_id){
                $model = FoquzQuestion::find()->where(['poll_id'=>$poll_id])->one();
                $this->redirect(Url::to(['change-type', 'id' => $model->id, 'pollId'=>$poll_id, 'type' => $model::TYPE_TEXT]));
            }
        }
        $model->type = $type;
        $model->save();
        $model->setScenario($model::SCENARIO_UPDATE);

        $uploadForm = new UploadForm();
        if ($model->type === $model::TYPE_VIDEO) {
            $uploadForm = new VideoUploadForm();
        }

        $detailQuestions = $model->questionDetails ?: [new FoquzQuestionDetail()];
        if ($model->type === $model::TYPE_IMAGE) {
            $model->setScenario($model::SCENARIO_UPDATE_IMAGE);
        } elseif (in_array($model->type, [$model::TYPE_IMAGE, $model::TYPE_VIDEO])) {
            $model->setScenario($model::SCENARIO_UPDATE_VIDEO);
        }
        $points_count = FoquzQuestion::find()->where(['service_name'=>$model->service_name])->count();

        $params = [
            'model' => $model,
            'detailQuestions' => $detailQuestions,
            'uploadForm' => $uploadForm,
            'points_count' =>$points_count
        ];

        if (Yii::$app->getRequest()->getIsAjax()) {
            return $this->renderPartial('update', $params);
        }

        return $this->render('update', $params);
    }

    public function actionQuestionImages($id)
    {
        return $this->renderPartial('question-images', [
            'id' => $id,
        ]);
    }

    public function actionQuestionVideos($id)
    {
        return $this->renderPartial('question-videos', [
            'id' => $id,
        ]);
    }

    public function actionSaveFileInput($fileId, $value)
    {
        $session = Yii::$app->getSession();
        $inputs = $session->get(self::SESSION_KEY, []);
        $inputs[$fileId] = $value;
        $session->set(self::SESSION_KEY, $inputs);
    }

    /**
     * Deletes an existing FoquzQuestion model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);

        $redirectUrl = '';

        $position = $model->position;
        $poll_id = $model->poll_id;

        $point_item = FoquzPointItem::find()->where(['name'=>$model->service_name])->one();
        if($point_item){
            $point_item_selected = FoquzPointSelected::find()->where(['foquz_point_item_id'=>$point_item->id,'foquz_poll_id'=>$model->poll_id])->one();
            if($point_item_selected)
                $point_item_selected->delete();
        }

        $model->is_deleted = 1;
        $model->save();

        FoquzPollQuestionViewLogic::deleteAll(['condition_question_id' => $model->id]);
        TagDependency::invalidate(Yii::$app->cache, 'quiz-poll-' . $model->poll->id);

        if($model->poll->point_system) {
            $model->poll->max_points = $model->poll->calculatePoints();
            $model->poll->save();
        }

        if ($model->poll->getFoquzQuestions()->count() == 0) {
            // fix for task 1187
            //$model->poll->delete();
            //return $this->redirect(['/foquz']);
            if($model->poll->is_auto) {
                return $this->redirect(['/foquz/foquz-poll/auto-settings?id='.$model->poll->id]);
            } else {
                return $this->redirect($model->poll->getViewUrl(true));
            }

        }

        $redirectUrl = $model->poll->getViewUrl(true);

        return $this->redirect($redirectUrl);

    }

    public function actionUpdatePosition()
    {
        Yii::$app->getResponse()->format = Response::FORMAT_JSON;
        $questionsId = Yii::$app->getRequest()->post('navs', []);

        foreach ($questionsId as $item) {
            FoquzQuestion::updateAll(['position' => $item['position']], [
                'id' => $item['id']
            ]);
        }

        return $questionsId;
    }

    /**
     * Finds the FoquzQuestion model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id
     * @param array $relations
     * @return FoquzQuestion
     * @throws NotFoundHttpException
     * @throws \yii\base\ExitException
     */
    private function findModel(int $id, array $relations = [])
    {
        $alias = FoquzQuestion::tableName();
        $modelQuery = FoquzQuestion::find()
            ->alias($alias)
            ->where([$alias . '.id' => $id]);
        $model = !empty($relations)
            ? $modelQuery->joinWith($relations)->one()
            : $modelQuery->one();

        if(!$model){
            $poll_id = Yii::$app->getRequest()->get('pollId');
            if($poll_id){
                $model1 = FoquzPoll::findOne($poll_id);
                if (!$model1) {
                    throw new NotFoundHttpException();
                }
                //var_dump($model1->getViewUrl());exit;
                $this->redirect($model1->getViewUrl());
                Yii::$app->end();
            }
        }
        if ($model !== null) {
            $companyId = Yii::$app->user->identity->company->id;
            if ($companyId !== $model->poll->company_id || $model->poll->deleted || $model->poll->status === FoquzPoll::STATUS_ARCHIVE || ($model->poll->is_tmp && $model->poll->updated_at !== $model->poll->created_at)) {
                throw new NotFoundHttpException();
            }

            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * Пересчет последующих позиций относительно конкретной
     * @param int $pollId
     * @param int $oldPosition
     */
    protected function recountPositions(int $pollId, int $oldPosition)
    {
        $questions = FoquzQuestion::find()
            ->andWhere(['poll_id' => $pollId])
            ->andWhere(['>', 'position', $oldPosition])
            ->orderBy(['position' => SORT_ASC])
            ->all();

        foreach ($questions as $key => $question) {
            $question->updateAttributes(['position' => $question->position - 1]);
        }
    }

    public function actionContactFields()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        return ContactAdditionalField::arrayFields($this->auth());
    }

    /**
     * Авторизует пользователя по токену или хешу ссылки
     * @throws ForbiddenHttpException
     * @return int
     */
    public function auth(): int
    {
        $accessToken = Yii::$app->request->get('access-token');
        $linkKey = Yii::$app->request->get('link') ?: Yii::$app->request->get('link-key');
        if (!Yii::$app->user->isGuest && !empty(Yii::$app->user->identity->company->id)) {
            return Yii::$app->user->identity->company->id;
        }
        if (is_string($linkKey) && strlen($linkKey) === 32) {
            $linkPollID = FoquzPollStatsLink::find()
                ->select('foquz_poll_id')
                ->where(['right_level' => FoquzPollStatsLink::RIGHT_READ])
                ->andWhere(['like', 'link', '/stats/' . $linkKey])
                ->scalar();
            if ($linkPollID && $poll = FoquzPoll::find()->where(['id' => $linkPollID])->one()) {
                return $poll->company_id;
            }
        }
        throw new ForbiddenHttpException('Доступ запрещен');
    }
}
