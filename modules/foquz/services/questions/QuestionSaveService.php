<?php

namespace app\modules\foquz\services\questions;

use app\models\DictionaryElement;
use app\modules\foquz\models\FoquzFile;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollDisplayPageQuestion;
use app\modules\foquz\models\FoquzPollQuestionViewLogic;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionAddressCodes;
use app\modules\foquz\models\FoquzQuestionCardSortingCategory;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionDifferentialRow;
use app\modules\foquz\models\FoquzQuestionFile;
use app\modules\foquz\models\FoquzQuestionFirstClick;
use app\modules\foquz\models\FoquzQuestionFirstClickArea;
use app\modules\foquz\models\FoquzQuestionFormField;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSetting;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSettingSocNetworks;
use app\modules\foquz\models\FoquzQuestionMatrixElement;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariant;
use app\modules\foquz\models\FoquzQuestionNpsRatingSetting;
use app\modules\foquz\models\FoquzQuestionPrioritySettings;
use app\modules\foquz\models\FoquzQuestionRightAnswer;
use app\modules\foquz\models\FoquzQuestionScaleRatingSetting;
use app\modules\foquz\models\FoquzQuestionSemDifSetting;
use app\modules\foquz\models\FoquzQuestionSmile;
use app\modules\foquz\models\FoquzQuestionStarRatingOptions;
use app\modules\foquz\models\RecipientQuestionDetail;
use app\modules\foquz\services\api\ExtraQuestionService;
use app\modules\foquz\services\QuestionEndScreenLogoService;
use app\modules\foquz\services\QuestionFileService;
use RuntimeException;
use Throwable;
use Yii;
use yii\db\Exception;
use yii\db\StaleObjectException;
use yii\helpers\ArrayHelper;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;
use yii\web\ServerErrorHttpException;
use yii\web\UploadedFile;

/**
 * @todo Перенес целиком из контроллера, чтобы тут со временем распилить и порефакторить.
 */
class QuestionSaveService
{
    private FoquzQuestion $model;

    public function __construct(FoquzQuestion $question)
    {
        $this->model = $question;
    }


    /**
     *
     * @param array $post_data
     * @param $id
     * @param bool $asContactPoint
     * @return array
     * @throws BadRequestHttpException
     * @throws Exception
     * @throws NotFoundHttpException
     * @throws ServerErrorHttpException
     * @throws StaleObjectException
     * @throws Throwable
     * @todo
     * Пока просто перенес из контроллера, чтобы постепенно тут распиливать
     */
    public function saveQuestion(array $post_data, $id, bool $asContactPoint = false): array
    {
        $model = $this->model;

        $statisticIsCollecting = FoquzPollAnswerItem::find()->where(['foquz_question_id' => $model->id])->exists();
        if ($statisticIsCollecting && $error = $this->checkChangeSettings($post_data)) {
            return $this->returnError($error);
        }

        $response = null;
        $model->show_labels = $post_data['FoquzQuestion']['show_labels'] ?? 0;
        $model->show_numbers = $post_data['FoquzQuestion']['show_numbers'] ?? 0;
        $model->sub_description = $post_data['FoquzQuestion']['sub_description'] ?? null;
        $model->description_html = $post_data['FoquzQuestion']['description'] ?? null;
        $model->from_one = $post_data['FoquzQuestion']['from_one'] ?? 0;
        $model->comment_label = $post_data['FoquzQuestion']['comment_label'] ?? null;
        $model->set_variants = $post_data['FoquzQuestion']['set_variants'] ?? 0;
        $model->extra_required = $post_data['FoquzQuestion']['extra_required'] ?? 1;
        $model->dictionary_element_id = $post_data['FoquzQuestion']['dictionary_element_id'] ?? null;
        $model->max_points_calc_method = $post_data['FoquzQuestion']['max_points_calc_method'] ?? FoquzQuestion::MAX_POINTS_CALC_METHOD_WITHOUT_HIDDEN;
        $model->file_types = $post_data['FoquzQuestion']['file_types'] ?? [];
        $model->is_tmp = 0;

        $model->setScenario($model::SCENARIO_UPDATE);

        if (isset($post_data['FoquzQuestion'])) {
            if (
                isset($post_data['FoquzQuestion']['comment_enabled']) &&
                $post_data['FoquzQuestion']['comment_enabled'] &&
                $post_data['FoquzQuestion']['main_question_type'] != FoquzQuestion::TYPE_VARIANTS
            ) {
                $model->detail_question = '';

                unset($post_data['FoquzQuestion']['detail_question']);
            }

            if (isset($post_data['FoquzQuestion']['mask_config'])) {
                $post_data['FoquzQuestion']['mask_config'] = json_encode($post_data['FoquzQuestion']['mask_config']);
            }
            if (isset($post_data['FoquzQuestion']['answers_from'])) {
                $post_data['FoquzQuestion']['answers_from'] = date('Y-m-d H:i:s',
                    strtotime($post_data['FoquzQuestion']['answers_from'] . ' ' . date('H:i:s')));
            }
            $model->is_updated = 1;
            foreach (['show_category', 'show_name', 'show_portion'] as $name) {
                if (!isset($post_data['FoquzQuestion'][$name])) {
                    $post_data['FoquzQuestion'][$name] = 0;
                }
            }
        }

        if ($model->id && (int)$model->variants_element_type === FoquzQuestion::TYPE_TEXT_ANSWER) {
            FoquzQuestionDetail::deleteAll(['foquz_question_id' => $model->id, 'extra_question' => 1]);
        }

        if (
            !empty($post_data['FoquzQuestion']['self_variant_comment_required']) &&
            !empty($post_data['FoquzQuestion']['is_self_answer']) &&
            (int)$post_data['FoquzQuestion']['main_question_type'] === FoquzQuestion::TYPE_VARIANTS
        ) {
            $model->self_variant_comment_required = 1;
        } else {
            $model->self_variant_comment_required = 0;
        }

        unset($post_data['FoquzQuestion']['self_variant_comment_required']);

        if (
            $model->main_question_type != $post_data['FoquzQuestion']['main_question_type'] &&
            $statisticIsCollecting
        ) {
            return $this->returnError('Изменить параметр «Тип вопроса» нельзя, так как по вопросу собирается статистика');
        }

        // Если у вопроса-реципиента меняется тип вопроса, связь с вопросом-донором сбрасывается
        if ($model->main_question_type != $post_data['FoquzQuestion']['main_question_type'] && $model->donor) {
            RecipientQuestionDetail::deleteAll(['recipient_id' => $model->id]);
            $model->donor = null;
        }

        if ($model->main_question_type != $post_data['FoquzQuestion']['main_question_type'] && ($model->donor_rows || $model->donor_columns)) {
            FoquzQuestionMatrixElement::updateAll(
                ['donor_variant_id' => null, 'donor_dictionary_element_id' => null],
                ['foquz_question_id' => $model->id]
            );
            $model->donor_rows = null;
            $model->donor_columns = null;
        }

        $poll = $model->poll ?? null;
        if ($poll && $model->main_question_type !== (int)$post_data['FoquzQuestion']['main_question_type']) {
            $problematicQuestionsIDs = FoquzPollQuestionViewLogic::find()
                ->select('question_id')
                ->where(['condition_question_id' => $model->id])
                ->distinct()
                ->column();
            if (!empty($problematicQuestionsIDs)) {
                $questionIndexes = $this->getQuestionNumbers($poll);
                $problematicQuestions = array_values(array_filter($questionIndexes,
                    static function ($questionID) use ($problematicQuestionsIDs) {
                        return in_array($questionID, $problematicQuestionsIDs);
                    }, ARRAY_FILTER_USE_KEY));
                if (!empty($problematicQuestions)) {
                    $problematicQuestions = array_unique($problematicQuestions);
                    if (count($problematicQuestions) > 10) {
                        $problematicQuestions = array_slice($problematicQuestions, 0, 10);
                        $errorText = 'Сменить тип вопроса нельзя, так как вопрос используется в условиях отображения вопросов №' . implode(', ',
                                $problematicQuestions) . ' и др.';
                    } elseif (count($problematicQuestions) > 1) {
                        $errorText = 'Сменить тип вопроса нельзя, так как вопрос используется в условиях отображения вопросов №' . implode(', ',
                                $problematicQuestions);
                    } else {
                        $errorText = 'Сменить тип вопроса нельзя, так как вопрос используется в условиях отображения вопроса №' . implode(', ',
                                $problematicQuestions);
                    }
                    return $this->returnError($errorText);
                }
            }
        }

        if ($poll && $model->skip && empty($post_data['FoquzQuestion']['skip'])) {
            /** @var FoquzPollQuestionViewLogic[] $logicQuestions */
            $logicQuestions = FoquzPollQuestionViewLogic::find()
                ->where(['condition_question_id' => $model->id])
                ->all();
            if (!empty($logicQuestions)) {
                $questionIndexes = $this->getQuestionNumbers($poll);
                $problematicQuestions = [];
                foreach ($logicQuestions as $logicQuestion) {
                    if (!empty($logicQuestion->skipped) && array_key_exists($logicQuestion->question_id,
                            $questionIndexes)) {
                        $problematicQuestions[] = $questionIndexes[$logicQuestion->question_id];
                    }
                }
                if (!empty($problematicQuestions)) {
                    $problematicQuestions = array_unique($problematicQuestions);
                    if (count($problematicQuestions) > 10) {
                        $problematicQuestions = array_slice($problematicQuestions, 0, 10);
                        $errorText = 'Отключить опцию пропуск оценки нельзя, так как она используется в условиях отображения вопросов №' . implode(', ',
                                $problematicQuestions) . ' и др.';
                    } elseif (count($problematicQuestions) > 1) {
                        $errorText = 'Отключить опцию пропуск оценки нельзя, так как она используется в условиях отображения вопросов №' . implode(', ',
                                $problematicQuestions);
                    } else {
                        $errorText = 'Отключить опцию пропуск оценки нельзя, так как она используется в условиях отображения вопроса №' . implode(', ',
                                $problematicQuestions);
                    }
                    return $this->returnError($errorText);
                }
            }
        }
        if ($poll && $model->is_self_answer && empty($post_data['FoquzQuestion']['is_self_answer'])) {
            $recipients = [];
            $questionIndexes = [];
            foreach ($poll->foquzQuestions as $key => $question) {
                if (
                    (int)$question->main_question_type === FoquzQuestion::TYPE_INTERMEDIATE_BLOCK &&
                    $question->intermediateBlock &&
                    in_array($question->intermediateBlock->screen_type, [
                        FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_START,
                        FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_END
                    ])
                ) {
                    continue;
                }
                $questionIndexes[$question->id] = $key + 1;
                if (!in_array($question->main_question_type, FoquzQuestion::TYPES_RECIPIENTS)) {
                    continue;
                }
                if ($question->donor === $model->id) {
                    $recipients[] = $question;
                    continue;
                }
                $mainDonor = $question->getMainDonor();
                if ($mainDonor && $mainDonor->id === $model->id) {
                    $recipients[] = $question;
                }
            }
            /** @var FoquzQuestion[] $recipients */
            $recipients = ArrayHelper::index($recipients, 'id');
            $recipientIDs = array_keys($recipients);
            $recipientDetails = RecipientQuestionDetail::find()
                ->where([
                    'recipient_id'          => $recipientIDs,
                    'dictionary_element_id' => null,
                    'question_detail_id'    => null
                ])
                ->all();
            /** @var FoquzPollQuestionViewLogic[] $viewLogicConditions */
            $viewLogicConditions = FoquzPollQuestionViewLogic::find()
                ->where(['condition_question_id' => ArrayHelper::merge([$model->id], $recipientIDs)])
                ->all();
            $conditionQuestions = ArrayHelper::merge([$model->id => $model], $recipients);

            $problematicQuestions = [];
            foreach ($viewLogicConditions as $viewLogicCondition) {
                /** @var FoquzQuestion $conditionQuestion */
                $conditionQuestion = $conditionQuestions[$viewLogicCondition->condition_question_id] ?? null;
                if ($conditionQuestion && (int)$conditionQuestion->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
                    $logicVariantExist = in_array($model->self_variant_text,
                            ArrayHelper::getColumn($viewLogicCondition->variants,
                                'row')) || in_array($model->self_variant_text, $viewLogicCondition->skipped);
                    if ($logicVariantExist) {
                        $problematicQuestions[] = $questionIndexes[$viewLogicCondition->question_id];
                    }
                } elseif ($conditionQuestion && (int)$conditionQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                    $logicVariantExist = in_array(0, $viewLogicCondition->variants) || in_array(0,
                            $viewLogicCondition->skipped);
                    if ($logicVariantExist) {
                        $problematicQuestions[] = $questionIndexes[$viewLogicCondition->question_id];
                    } else {
                        /** @var RecipientQuestionDetail $questionRecipientDetail */
                        foreach ($recipientDetails as $questionRecipientDetail) {
                            $logicVariantExist = in_array($questionRecipientDetail->id,
                                    $viewLogicCondition->variants) || in_array($questionRecipientDetail->id,
                                    $viewLogicCondition->skipped);
                            if ($logicVariantExist) {
                                $problematicQuestions[] = $questionIndexes[$viewLogicCondition->question_id];
                            }
                        }
                    }
                } else {
                    $logicVariantExist = in_array(0,
                            ArrayHelper::getColumn($viewLogicCondition->variants, 'row')) || in_array(0,
                            $viewLogicCondition->skipped);
                    if ($logicVariantExist) {
                        $problematicQuestions[] = $questionIndexes[$viewLogicCondition->question_id];
                    } else {
                        /** @var RecipientQuestionDetail $questionRecipientDetail */
                        foreach ($recipientDetails as $questionRecipientDetail) {
                            $logicVariantExist = in_array($questionRecipientDetail->id,
                                    ArrayHelper::getColumn($viewLogicCondition->variants,
                                        'row')) || in_array($questionRecipientDetail->id, $viewLogicCondition->skipped);
                            if ($logicVariantExist) {
                                $problematicQuestions[] = $questionIndexes[$viewLogicCondition->question_id];
                            }
                        }
                    }
                }
            }
            if (!empty($problematicQuestions)) {
                $problematicQuestions = array_unique($problematicQuestions);
                if (count($problematicQuestions) > 10) {
                    $problematicQuestions = array_slice($problematicQuestions, 0, 10);
                    $errorText = 'Отключить «Свой вариант» невозможно, так как он используется в условиях отображения вопросов №' . implode(', ',
                            $problematicQuestions) . ' и др.';
                } elseif (count($problematicQuestions) > 1) {
                    $errorText = 'Отключить «Свой вариант» невозможно, так как он используется в условиях отображения вопросов №' . implode(', ',
                            $problematicQuestions);
                } else {
                    $errorText = 'Отключить «Свой вариант» невозможно, так как он используется в условиях отображения вопроса №' . implode(', ',
                            $problematicQuestions);
                }
                return $this->returnError($errorText);
            }
        }

        if ($poll && (int)$model->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
            $matrixSettingsOld = json_decode($model->matrix_settings, true) ?? [];
            $matrixSettingsNew = $post_data['FoquzQuestion']['matrixSettings'] ?? null;
            if (
                is_array($matrixSettingsOld) &&
                is_array($matrixSettingsNew) &&
                isset($matrixSettingsOld['rows'], $matrixSettingsNew['rows'])
            ) {
                $oldRows = $matrixSettingsOld['rows'];
                $newRows = $matrixSettingsNew['rows'];
                if ($rowsForDelete = array_diff($oldRows, $newRows)) {
                    /** @var FoquzPollQuestionViewLogic[] $logicQuestions */
                    $logicQuestions = FoquzPollQuestionViewLogic::find()
                        ->where(['condition_question_id' => $model->id])
                        ->all();
                    if (!empty($logicQuestions)) {
                        $questionIndexes = $this->getQuestionNumbers($poll);
                        $problematicQuestions = [];
                        foreach ($logicQuestions as $logicQuestion) {
                            $logicRows = ArrayHelper::getColumn($logicQuestion->variants, 'row');
                            if (empty($logicRows) && empty($logicQuestion->skipped)) {
                                continue;
                            }
                            if (
                                !empty(array_intersect($rowsForDelete, $logicRows)) ||
                                !empty(array_intersect($rowsForDelete, $logicQuestion->skipped))
                            ) {
                                $problematicQuestions[] = $questionIndexes[$logicQuestion->question_id];
                            }
                        }
                        if (!empty($problematicQuestions)) {
                            $problematicQuestions = array_unique($problematicQuestions);
                            if (count($problematicQuestions) > 10) {
                                $problematicQuestions = array_slice($problematicQuestions, 0, 10);
                                $errorText = 'Удалить и переименовывать строки матрицы нельзя, так как они используются в условиях отображения вопросов №' . implode(', ',
                                        $problematicQuestions) . ' и др.';
                            } elseif (count($problematicQuestions) > 1) {
                                $errorText = 'Удалить и переименовывать строки матрицы нельзя, так как они используются в условиях отображения вопросов №' . implode(', ',
                                        $problematicQuestions);
                            } else {
                                $errorText = 'Удалить и переименовывать строки матрицы нельзя, так как они используются в условиях отображения вопроса №' . implode(', ',
                                        $problematicQuestions);
                            }
                            return $this->returnError($errorText);
                        }
                    }
                }
            }
        }

        // Установить настройки вопроса-донора
        $donorModel = $donor = null;
        if (in_array($post_data['FoquzQuestion']['main_question_type'], FoquzQuestion::TYPES_RECIPIENTS)) {
            $donor = $post_data['FoquzQuestion']['donor'] ?? '';
            if ($donor && $donor != $id) {
                $donorModel = FoquzQuestion::findOne(['id' => $donor, 'is_deleted' => 0]);
                if (!$donorModel) {
                    throw new NotFoundHttpException('Вопрос-донор с id ' . $donor . ' не найден');
                }
                if ($donorModel->poll_id != $model->poll_id) {
                    throw new \Exception('Id опроса вопроса-донора не совпадает с id опроса текущего вопроса');
                }
//                if ($donorModel->donor && $post_data['FoquzQuestion']['main_question_type'] != FoquzQuestion::TYPE_VARIANTS) {
//                    throw new RuntimeException(
//                        'Выбранный вопрос не может быть выбран в качестве донора, так как является реципиентом'
//                    );
//                }
                $model->donor = $donor;
                $model->is_self_answer = $donorModel->is_self_answer;
                $model->self_variant_text = $donorModel->self_variant_text;
            } else {
                $model->donor = null;
            }
            if (isset($post_data['FoquzQuestion']['donor_chosen']) && !$post_data['FoquzQuestion']['donor_chosen']) {
                $model->donor_chosen = 0;
            }
            if (isset($post_data['FoquzQuestion']['donor_cols_chosen']) && !$post_data['FoquzQuestion']['donor_cols_chosen']) {
                $model->donor_cols_chosen = 0;
            }
        }

        /** SET FILIALS */
        if (isset($post_data['FoquzQuestion']['filials'])) {
            if (!$post_data['FoquzQuestion']['filials']) {
                throw new RuntimeException('Нужно выбрать хотя бы один филиал');
            }

            $filials = $post_data['FoquzQuestion']['filials'];
            //print_r($_POST);

            if (!empty($post_data['FoquzQuestion']['dictionary_sort'])) {
                $model->dictionary_sort = $post_data['FoquzQuestion']['dictionary_sort'];
            }


            $deleted = [];
            $current = json_decode($model->detail_question);
            $deletedDetails = json_decode($model->deleted_detail_question) ?? [];

            if ($current) {
                foreach ($current as $current_id) {
                    if (!array_key_exists($current_id, array_flip($filials))) {
                        $deleted[] = $current_id;
                    }
                }
            }

            $model->detail_question = json_encode($filials);
            $updateDeleted = array_unique(array_merge((array)$deletedDetails, $deleted));

            /** Если удаленный вариант вернули в вопрос */
            $deletedDetailsFlipped = array_flip($updateDeleted);
            foreach ($filials as $filial) {
                if (array_key_exists($filial, $deletedDetailsFlipped)) {
                    unset($deletedDetailsFlipped[$filial]);
                }
            }

            $model->deleted_detail_question = json_encode(array_flip($deletedDetailsFlipped));
        }

        if (!empty($post_data['FoquzQuestion']['dictionary_id'])) {
            $model->dictionary_id = $post_data['FoquzQuestion']['dictionary_id'];

            if (isset($post_data['FoquzQuestion']['dictionaries'])) {
                if (!$post_data['FoquzQuestion']['dictionaries']) {
                    throw new RuntimeException('Нужно выбрать хотя бы один филиал');
                }
                $model->detail_question = json_encode($post_data['FoquzQuestion']['dictionaries']);

                if (!empty($post_data['FoquzQuestion']['dictionary_sort'])) {
                    $model->dictionary_sort = $post_data['FoquzQuestion']['dictionary_sort'];
                }
            }
        }
        if (!empty($post_data['FoquzQuestion']['dictionary_list_type'])) {
            $model->dictionary_list_type = $post_data['FoquzQuestion']['dictionary_list_type'];
        }

        if ($model->main_question_type === FoquzQuestion::TYPE_DISTRIBUTION_SCALE) {
            if (!empty($post_data['FoquzQuestion']['variants_element_type']) && $post_data['FoquzQuestion']['variants_element_type'] > FoquzQuestion::VARIANT_ELEMENT_TYPE_CHECKBOX) {
                $post_data['FoquzQuestion']['variants_element_type'] = FoquzQuestion::VARIANT_ELEMENT_TYPE_CHECKBOX;
            }
        }
                       
        if ($model->main_question_type === FoquzQuestion::TYPE_SMILE_RATING) {
            if (!empty($post_data['FoquzQuestion']['extra_question_type']) && $post_data['FoquzQuestion']['extra_question_type'] > FoquzQuestion::EXTRA_QUESTION_COMMON) {
                $post_data['FoquzQuestion']['extra_question_type'] = FoquzQuestion::EXTRA_QUESTION_COMMON;
            }
        }

        $this->model = $model;
        if ($model->load($post_data) && $model->save()) {
            $poll = $model->poll;
            $poll->is_tmp = 0;
            $poll->is_template = 0;
            $poll->save();
            if (isset($_POST['save_all'])) {
                $source_question = FoquzQuestion::find()->where([
                    'service_name' => $model->service_name,
                    'is_source'    => 1
                ])->one();
                if (!$source_question) {
                    $model->is_source = 1;
                    $model->save();
                } elseif ($source_question->id != $model->id) {
                    $source_question->is_source = 0;
                    $source_question->save();
                    $model->is_source = 1;
                    $model->save();
                }
            }

            if (
                $model->poll->point_system &&
                in_array($model->main_question_type, [FoquzQuestion::TYPE_DATE, FoquzQuestion::TYPE_PRIORITY]) &&
                isset($post_data['FoquzQuestion']['rightAnswer'])
            ) {
                $rightAnswer = $model->rightAnswer;
                if (!$rightAnswer) {
                    $rightAnswer = new FoquzQuestionRightAnswer([
                        'foquz_question_id' => $model->id,
                    ]);
                }
                if (!empty($post_data['FoquzQuestion']['rightAnswer']['answer']['time'])) {
                    $post_data['FoquzQuestion']['rightAnswer']['answer']['time'] = str_replace(' : ', ':',
                        $post_data['FoquzQuestion']['rightAnswer']['answer']['time']);
                }
                $rightAnswer->answer = json_encode($post_data['FoquzQuestion']['rightAnswer']['answer'],
                    JSON_UNESCAPED_UNICODE);
                $rightAnswer->points = $post_data['FoquzQuestion']['rightAnswer']['points'] ?? 0;
                $rightAnswer->save();
            }

            if ($model->main_question_type == FoquzQuestion::TYPE_PRIORITY) {
                $settings = $model->foquzQuestionPrioritySettings;
                if (!$settings) {
                    $settings = new FoquzQuestionPrioritySettings();
                }
                $settings->reorder_required = (isset($post_data['FoquzQuestion']['reorder_required']) && $post_data['FoquzQuestion']['reorder_required']) ? 1 : 0;
                $settings->foquz_question_id = $model->id;
                $settings->save();
            } else {
                if ($model->foquzQuestionPrioritySettings) {
                    $model->foquzQuestionPrioritySettings->delete();
                }
            }

            $response = [];
            if ($model->main_question_type == FoquzQuestion::TYPE_FORM && isset($post_data['FoquzQuestion']['quizzes'])) {
                $ids = [];
                $position = 1;
                foreach ($post_data['FoquzQuestion']['quizzes'] as $formField) {

                    if ($formField['id'] === '0') {
                        $formFieldModel = new FoquzQuestionFormField();
                    } else {
                        $formFieldModel = FoquzQuestionFormField::findOne($formField['id']);
                        if ($formFieldModel->question_id !== $model->id) {
                            $formFieldModel = new FoquzQuestionFormField();
                        }
                    }

                    $formFieldModel->question_id = $id;
                    $formFieldModel->name = $formField['name'];
                    $formFieldModel->is_required = $formField['is_required'];
                    $formFieldModel->mask_type = $formField['mask_type'];
                    $formFieldModel->variants_type = $formField['variants_type'];
                    $formFieldModel->comment_minlength = $formField['comment_minlength'];
                    $formFieldModel->comment_maxlength = $formField['comment_maxlength'];
                    $formFieldModel->link_with_client_field = $formField['link_with_client_field'];
                    $formFieldModel->linked_client_field = $formField['linked_client_field'];
                    $formFieldModel->rewrite_linked_field = $formField['rewrite_linked_field'];
                    $formFieldModel->placeholder_text = $formField['placeholder_text'];
                    if (isset($formField['mask_type']) && $formField['mask_type'] == 5) {
                        $formFieldModel->name = 'ФИО';
                        $formFieldModel->mask_config = json_encode($formField['mask_config']);
                    } else {
                        $formFieldModel->mask_config = null;
                    }
                    $formFieldModel->position = $position;
                    $formFieldModel->save();
                    $ids[] = $formFieldModel->id;
                    $position++;
                }
                FoquzQuestionFormField::deleteAll(['AND', ['NOT IN', 'id', $ids], ['question_id' => $id]]);
                $response = [
                    'question' => $model,
                    'ids'      => $ids
                ];
            }

            $fileService = new QuestionFileService($model);
            if (isset($post_data['FoquzQuestion']['enableGallery']) && !$post_data['FoquzQuestion']['enableGallery']) {
                $fileService->deleteOldFiles();
            } elseif (isset($post_data['FoquzQuestion']['gallery'])) {
                $fileService->updateFiles($post_data['FoquzQuestion']['gallery']);
                $fileService->deleteOldFiles();
            }
            if (isset($post_data['media'])) {
                $fileService->updateFiles($post_data['media']);
                $fileService->deleteOldFiles();
            }

            if ($model->main_question_type == FoquzQuestion::TYPE_ADDRESS) {
                $addressCodeModel = $model->addressCodes;
                if ($addressCodeModel === null) {
                    $addressCodeModel = new FoquzQuestionAddressCodes();
                    $addressCodeModel->question_id = $model->id;
                }
                $addressCodeModel->regions = json_encode($post_data['FoquzQuestion']['regionsValues'] ?? []);
                $addressCodeModel->districts = json_encode($post_data['FoquzQuestion']['districtValues'] ?? []);
                $addressCodeModel->cities = json_encode($post_data['FoquzQuestion']['cityValues'] ?? []);
                $addressCodeModel->streets = json_encode($post_data['FoquzQuestion']['streetValues'] ?? []);
                $addressCodeModel->save();
                return [
                    'question' => $model,
                    'codes'    => $addressCodeModel
                ];
            }
            if ($model->main_question_type == FoquzQuestion::TYPE_CHOOSE_MEDIA) {
                $fileService = new QuestionFileService($model);
                if (isset($post_data['FoquzQuestion']['variants']) && count($post_data['FoquzQuestion']['variants']) > 0) {
                    $fileService->updateFiles($post_data['FoquzQuestion']['variants']);
                }
                $fileService->deleteOldFiles();
            }
            if ($model->main_question_type == FoquzQuestion::TYPE_SMILE_RATING) {
                $model->smile_type = $post_data['FoquzQuestion']['smile_type'];
                $model->smiles_count = $post_data['FoquzQuestion']['smiles_count'] ?? null;
                if (isset($post_data['FoquzQuestion']['smiles']) && count($post_data['FoquzQuestion']['smiles']) > 0) {
                    $smileIds = [];
                    foreach ($post_data['FoquzQuestion']['smiles'] as $key => $smileData) {

                        $smileModel = null;
                        if (isset($smileData['id'])) {
                            $smileModel = FoquzQuestionSmile::findOne($smileData['id']);
                        }
                        if ($smileModel === null || $smileModel->foquz_question_id !== $model->id) {
                            $smileModel = new FoquzQuestionSmile();
                        }
                        $smileModel->foquz_question_id = $model->id;
                        $smileModel->label = $smileData['label'] ?? '';

                        $smileModel->file = UploadedFile::getInstanceByName('smile' . $key);
                        if ($post_data['FoquzQuestion']['smile_type'] === FoquzQuestion::SMILE_CUSTOM && $smileModel->file) {
                            $dir = "uploads/smiles/custom" . (int)$id;
                            if (!file_exists($dir) && !mkdir($dir, 0775, true) && !is_dir($dir)) {
                                throw new RuntimeException(sprintf('Directory "%s" was not created', $dir));
                            }
                            ++$key;
                            $filePath = $dir . "/$key.{$smileModel->file->extension}";
                            $smileModel->smile_url = '/' . $filePath;
                        } else {
                            $filePath = $smileModel->smile_url = $smileData['url'];
                        }
                        if ($smileModel->save()) {
                            $smileModel->file?->saveAs($filePath);
                            $smileIds[] = $smileModel->id;
                        } else {
                            print_r($smileModel->errors);
                            die();
                        }
                    }
                    FoquzQuestionSmile::deleteAll([
                        'AND',
                        ['NOT IN', 'id', $smileIds],
                        ['foquz_question_id' => $model->id]
                    ]);
                } else {
                    FoquzQuestionSmile::deleteAll(['foquz_question_id' => $model->id]);
                }
            }
            if ($model->main_question_type == FoquzQuestion::TYPE_NPS_RATING) {
                $npsRatingSetting = $model->npsRatingSetting ?? new FoquzQuestionNpsRatingSetting(['foquz_question_id' => $model->id]);
                $npsRatingSetting->design = $post_data['FoquzQuestion']['design'];
                $npsRatingSetting->start_point_color = $post_data['FoquzQuestion']['start_point_color'] ?? null;
                $npsRatingSetting->end_point_color = $post_data['FoquzQuestion']['end_point_color'] ?? null;
                $npsRatingSetting->start_label = $post_data['FoquzQuestion']['start_label'] ?? null;
                $npsRatingSetting->end_label = $post_data['FoquzQuestion']['end_label'] ?? null;
                $npsRatingSetting->save();
            } else {
                if ($model->npsRatingSetting) {
                    $model->npsRatingSetting->delete();
                }
            }
            if ($model->main_question_type == FoquzQuestion::TYPE_SCALE) {
                $scaleRatingSetting = $model->scaleRatingSetting ?? new FoquzQuestionScaleRatingSetting(['foquz_question_id' => $model->id]);
                $scaleRatingSetting->start = $post_data['FoquzQuestion']['scaleQuestion']['start'] ?? null;
                $scaleRatingSetting->end = $post_data['FoquzQuestion']['scaleQuestion']['end'] ?? null;
                $scaleRatingSetting->step = $post_data['FoquzQuestion']['scaleQuestion']['step'] ?? null;
                $scaleRatingSetting->save();
            }
            if ((int)$model->main_question_type === FoquzQuestion::TYPE_DISTRIBUTION_SCALE) {
                $scaleRatingSetting = $model->scaleRatingSetting ?? new FoquzQuestionScaleRatingSetting(['foquz_question_id' => $model->id]);
                $scaleRatingSetting->start = 1;
                $end = $post_data['FoquzQuestion']['scaleQuestion']['end'] ?? null;
                if (!is_null($end) && !is_numeric($end)) {
                    return $this->returnError(['end' => 'Значение должно быть числом']);
                }
                if (!is_null($end) && $end < 0) {
                    return $this->returnError(['end' => 'Значение должно быть положительным числом']);
                }
                $scaleRatingSetting->end = $end;
                $scaleRatingSetting->step = 10;
                $scaleRatingSetting->save();
            }
            if ((int)$model->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX || (int)$model->main_question_type === FoquzQuestion::TYPE_3D_MATRIX) {
                $model->matrix_settings = json_encode($post_data['FoquzQuestion']['matrixSettings'],
                    JSON_UNESCAPED_UNICODE);
                $model->save();
            }

            if ((int)$model->main_question_type === FoquzQuestion::TYPE_3D_MATRIX ||
                (int)$model->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $rows = !empty($post_data['FoquzQuestionMatrixElement']['rows']) ? $post_data['FoquzQuestionMatrixElement']['rows'] : [];
                    $columns = !empty($post_data['FoquzQuestionMatrixElement']['columns']) ? $post_data['FoquzQuestionMatrixElement']['columns'] : [];
                    $rows = array_map(static function ($row) {
                        $row['type_id'] = FoquzQuestionMatrixElement::TYPE_ROW;
                        return $row;
                    }, $rows);
                    $columns = array_map(static function ($column) {
                        $column['type_id'] = FoquzQuestionMatrixElement::TYPE_COLUMN;
                        return $column;
                    }, $columns);
                    $elements = array_merge($rows, $columns);
                    $elementsIDs = array_values(array_filter(ArrayHelper::getColumn($elements, 'id')));
                    $elementsIDsForDelete = FoquzQuestionMatrixElement::find()
                        ->select('id')
                        ->where(['AND', ['foquz_question_id' => $model->id], ['NOT', ['id' => $elementsIDs]]])
                        ->column();
                    FoquzQuestionMatrixElement::updateAll(
                        ['is_deleted' => 1],
                        ['id' => $elementsIDsForDelete]
                    );
                    FoquzQuestionMatrixElementVariant::updateAll(
                        ['is_deleted' => 1],
                        ['matrix_element_id' => $elementsIDsForDelete]
                    );
                    $elementModels = FoquzQuestionMatrixElement::find()
                        ->where(['id' => $elementsIDs, 'foquz_question_id' => $model->id, 'is_deleted' => 0])
                        ->all();
                    $elementModels = ArrayHelper::index($elementModels, 'id');
                    $donorColumnQuestion = null;
                    if ($model->donor_columns) {
                        $donorColumnQuestion = $model->getMainDonorColumns();
                    }
                    $donorRowQuestion = null;
                    if ($model->donor_rows) {
                        $donorRowQuestion = $model->getMainDonorRows();
                    }
                    foreach ($elements as $element) {
                        if (empty($element['id'])) {
                            $elementModel = new FoquzQuestionMatrixElement();
                        } else {
                            $elementModel = !empty($elementModels[$element['id']]) ? $elementModels[$element['id']] : null;
                            if (!$elementModel) {
                                $elementModel = new FoquzQuestionMatrixElement();
                            }
                        }
                        $elementModel->load($element, '');
                        $elementModel->foquz_question_id = $model->id;

                        if ($elementModel->type_id === FoquzQuestionMatrixElement::TYPE_COLUMN && !$donorColumnQuestion) {
                            $elementModel->donor_variant_id = null;
                            $elementModel->donor_dictionary_element_id = null;
                        } elseif ($elementModel->type_id === FoquzQuestionMatrixElement::TYPE_ROW && !$donorRowQuestion) {
                            $elementModel->donor_variant_id = null;
                            $elementModel->donor_dictionary_element_id = null;
                        } elseif (
                            $elementModel->type_id === FoquzQuestionMatrixElement::TYPE_COLUMN &&
                            $donorColumnQuestion && $donorColumnQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS
                        ) {
                            $elementModel->donor_dictionary_element_id = null;
                        } elseif (
                            $elementModel->type_id === FoquzQuestionMatrixElement::TYPE_COLUMN &&
                            $donorColumnQuestion && $donorColumnQuestion->main_question_type === FoquzQuestion::TYPE_DICTIONARY
                        ) {
                            $elementModel->donor_variant_id = null;
                        } elseif (
                            $elementModel->type_id === FoquzQuestionMatrixElement::TYPE_ROW &&
                            $donorRowQuestion && $donorRowQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS
                        ) {
                            $elementModel->donor_dictionary_element_id = null;
                        } elseif (
                            $elementModel->type_id === FoquzQuestionMatrixElement::TYPE_ROW &&
                            $donorRowQuestion && $donorRowQuestion->main_question_type === FoquzQuestion::TYPE_DICTIONARY
                        ) {
                            $elementModel->donor_variant_id = null;
                        }

                        if (!$elementModel->validate()) {
                            throw new BadRequestHttpException('Ошибка валидации элемента матрицы c ID ' .
                                $elementModel->id . ' - ' . implode(', ', $elementModel->getFirstErrors()));
                        }
                        if (!$elementModel->save()) {
                            throw new ServerErrorHttpException('Ошибка сохранения элемента матрицы c ID ' . $elementModel->id);
                        }
                        if ($elementModel->type_id === FoquzQuestionMatrixElement::TYPE_COLUMN) {
                            $variants = !empty($element['variants']) ? $element['variants'] : [];
                            if (empty($element['id'])) {
                                $variants = array_map(static function ($variant) {
                                    unset($variant['id']);
                                    return $variant;
                                }, $variants);
                            }
                            $variantsIDs = array_values(array_filter(ArrayHelper::getColumn($variants, 'id')));
                            FoquzQuestionMatrixElementVariant::updateAll(
                                ['is_deleted' => 1],
                                ['AND', ['matrix_element_id' => $elementModel->id], ['NOT', ['id' => $variantsIDs]]]
                            );
                            $variantModels = FoquzQuestionMatrixElementVariant::find()
                                ->where([
                                    'id'                => $variantsIDs,
                                    'matrix_element_id' => $elementModel->id,
                                    'is_deleted'        => 0
                                ])
                                ->all();
                            $variantModels = ArrayHelper::index($variantModels, 'id');
                            foreach ($variants as $variant) {
                                if (empty($variant['id'])) {
                                    $variantModel = new FoquzQuestionMatrixElementVariant();
                                } else {
                                    $variantModel = !empty($variantModels[$variant['id']]) ? $variantModels[$variant['id']] : null;
                                    if (!$variantModel) {
                                        $variantModel = new FoquzQuestionMatrixElementVariant();
                                    }
                                }
                                $variantModel->load($variant, '');
                                $variantModel->matrix_element_id = $elementModel->id;
                                if (!$variantModel->validate()) {
                                    throw new BadRequestHttpException('Ошибка валидации варианта элемента матрицы c ID ' .
                                        $variantModel->id . ' - ' . implode(', ', $variantModel->getFirstErrors()));
                                }
                                if (!$variantModel->save()) {
                                    throw new ServerErrorHttpException('Ошибка сохранения варианта элемента матрицы c ID ' .
                                        $variantModel->id);
                                }
                            }
                        }
                    }
                    $transaction->commit();
                } catch (BadRequestHttpException|NotFoundHttpException $e) {
                    $transaction->rollBack();
                    throw $e;
                } catch (Throwable $e) {
                    $transaction->rollBack();
                    throw new ServerErrorHttpException($e->getMessage());
                }
            }

            if ($model->main_question_type == FoquzQuestion::TYPE_SEM_DIFFERENTIAL) {
                $position = 1;
                $rowIds = [];
                $semDifSetting = $model->semDifSetting ?? new FoquzQuestionSemDifSetting(['foquz_question_id' => $model->id]);
                $semDifSetting->form = $post_data['FoquzQuestion']['form'] ?? null;
                $semDifSetting->start_point_color = $post_data['FoquzQuestion']['start_point_color'] ?? null;
                $semDifSetting->end_point_color = $post_data['FoquzQuestion']['end_point_color'] ?? null;
                $semDifSetting->save();
                foreach ($post_data['FoquzQuestion']['differentialRows'] as $differentialRow) {

                    if ($differentialRow['id'] == 0) {
                        $row = new FoquzQuestionDifferentialRow();
                    } else {
                        $row = FoquzQuestionDifferentialRow::findOne($differentialRow['id']);
                        if ($row->question_id !== $model->id) {
                            $row = new FoquzQuestionDifferentialRow();
                        }
                    }

                    $row->question_id = $model->id;
                    $row->start_label = $differentialRow['start_label'];
                    $row->end_label = $differentialRow['end_label'];
                    $row->position = $position;
                    $row->save();
                    $rowIds[] = $row->id;
                    $position++;
                }
                if (count($rowIds) > 0) {
                    FoquzQuestionDifferentialRow::deleteAll([
                        'AND',
                        ['NOT IN', 'id', $rowIds],
                        ['question_id' => $model->id]
                    ]);
                } else {
                    FoquzQuestionDifferentialRow::deleteAll(['question_id' => $model->id]);
                }
            }

            if (in_array($model->main_question_type,
                [FoquzQuestion::TYPE_STAR_RATING, FoquzQuestion::TYPE_VARIANT_STAR, FoquzQuestion::TYPE_RATING])) {
                $starOptions = $model->starRatingOptions ?? new FoquzQuestionStarRatingOptions(['foquz_question_id' => $model->id]);
                $starOptions->color = $post_data['FoquzQuestion']['starOptions']['color'] ?? '#f8cd1c';
                $starOptions->count = $post_data['FoquzQuestion']['starOptions']['count'] ?? 5;
                $starOptions->size = $post_data['FoquzQuestion']['starOptions']['size'] ?? 'md';
                $starOptions->extra_question_rate_from = $post_data['FoquzQuestion']['starOptions']['extra_question_rate_from'] ?? null;
                $starOptions->extra_question_rate_to = $post_data['FoquzQuestion']['starOptions']['extra_question_rate_to'] ?? null;
                $starOptions->labels = json_encode($post_data['FoquzQuestion']['starOptions']['labels'],
                    JSON_UNESCAPED_UNICODE);
                $starOptions->save();
            }

            $this->model = $model;

            // настройки промежуточных экранов
            try {
                $this->intermediateBlock($post_data);
            } catch (Throwable $e) {
                return $this->returnError($e->getMessage());
            }

            //варианты/строки/варианты УВ
            if (!$this->details($donorModel, $post_data, empty($donor) ? null : (int)$donor)) {
                return $this->returnError();
            }

            //сохранить картинки и видео вопроса
            $this->saveMedia();

            if ($this->model->poll->point_system) {
                $this->model->poll->max_points = $this->model->poll->calculatePoints();
                $this->model->poll->save();
            }

        }

        $this->model->refresh();

        /** Сохранение точки контакта */
        // убрано в рамках задачи #4484
        /*if ($asContactPoint && is_null($this->model->point_id) && !$this->model->errors) {
            $foquzPointItem = new FoquzPointItem();
            $foquzPointItem->foquz_poll_id = $this->model->poll_id;
            $foquzPointItem->name = FoquzPointItem::setNameIfExist($this->model->service_name,
                $this->model->poll->company_id);
            $foquzPointItem->is_active = 1;

            if (!$foquzPointItem->save()) {
                return $this->returnError(!empty($foquzPointItem->errors[0]) ? $foquzPointItem->errors[0] : 'Неизвестная ошибка');
            }

            $this->model->point_id = $foquzPointItem->id;
            $this->model->is_source = 1;
            $this->model->save();

            $newPoint = FoquzQuestion::createQuestionFromPoint($foquzPointItem->foquz_poll_id, $foquzPointItem, null,
                false, $this->model->position);
            $this->model->is_source = 0;
            $this->model->save();

            $newPoint->is_source = 1;
            $newPoint->poll_id = null;
            $newPoint->save();
        }*/

        if ((int)$post_data['FoquzQuestion']['main_question_type'] === FoquzQuestion::TYPE_CARD_SORTING_CLOSED) {

            $this->model->card_column_text = $post_data['FoquzQuestion']['card_column_text'] ??= 'Карточки';
            $this->model->category_column_text = $post_data['FoquzQuestion']['category_column_text'] ??= 'Категории';

            if (isset($post_data['FoquzQuestionCardSortingCategory']) && count(
                    $post_data['FoquzQuestionCardSortingCategory']
                )) {
                $cardSortingCategoryIds = [];
                foreach ($post_data['FoquzQuestionCardSortingCategory'] as $category) {
                    $cartSortingCategory = FoquzQuestionCardSortingCategory::createOrUpdate($category, $id);
                    if (!$cartSortingCategory->save()) {
                        Yii::$app->response->statusCode = 400;
                        return ['errors' => $cartSortingCategory->errors];
                    }
                    $cartSortingCategory->ensureHasTranslations();
                    $cardSortingCategoryIds[] = $cartSortingCategory->id;
                }
                if (count($cardSortingCategoryIds)) {
                    FoquzQuestionCardSortingCategory::updateAll(['is_deleted' => true], [
                        'AND',
                        ['foquz_question_id' => $this->model->id],
                        ['NOT IN', 'id', $cardSortingCategoryIds]
                    ]);
                }
            }
        }
        if ((int)$post_data['FoquzQuestion']['main_question_type'] === FoquzQuestion::TYPE_FIRST_CLICK) {

            if (empty($post_data['FoquzQuestion']['gallery'][0]['id'])) {
                Yii::$app->response->statusCode = 400;
                return ['errors' => ['gallery' => 'Необходимо добавить изображение']];
            }

            if (isset($post_data['FoquzQuestion']['firstClick'])) {
                $firstClick = FoquzQuestionFirstClick::createOrUpdate(
                    $post_data['FoquzQuestion']['firstClick'],
                    $id,
                    $post_data['FoquzQuestion']['gallery'][0]['id'],
                );
                if (!$firstClick->save()) {
                    Yii::$app->response->statusCode = 400;
                    return ['errors' => $firstClick->errors];
                }
                if (!$statisticIsCollecting) {
                    $data = $this->saveFirstClickArea($post_data['FoquzQuestion'], $id);
                    if (!empty($data['errors'])) {
                        return $data;
                    }
                }
            }
        }

        $this->model->save();
        $this->model->comment_label = $this->model->comment_label ?: '';

        return ['response' => $response, 'model' => $this->model];

    }


    private function checkChangeSettings(array $post_data): string
    {
        try {
            if ($this->model->main_question_type == FoquzQuestion::TYPE_NPS_RATING) {
                if (isset($post_data['FoquzQuestion']['extra_question_type']) && (int)$post_data['FoquzQuestion']['extra_question_type'] !== $this->model->extra_question_type) {
                    throw new \InvalidArgumentException('Изменить параметр «Тип уточняющего вопроса» нельзя, так как по вопросу собирается статистика');
                }

                if (isset($post_data['FoquzQuestion']['comment_enabled']) && (int)$post_data['FoquzQuestion']['comment_enabled'] !== (int)$this->model->comment_enabled) {
                    throw new \InvalidArgumentException('Изменить параметр «Комментарий» нельзя, так как по вопросу собирается статистика');
                }
                if (isset($post_data['FoquzQuestion']['variants_element_type']) && (int)$post_data['FoquzQuestion']['variants_element_type'] !== $this->model->variants_element_type) {
                    throw new \InvalidArgumentException('Изменить параметр «Тип уточняющего вопроса» нельзя, так как по вопросу собирается статистика');
                }
                if (isset($post_data['FoquzQuestion']['is_self_answer']) && (int)$post_data['FoquzQuestion']['is_self_answer'] !== (int)$this->model->is_self_answer) {
                    throw new \InvalidArgumentException('Изменить параметр «Опция свой вариант» нельзя, так как по вопросу собирается статистика');
                }
                ;
                if (isset($post_data['FoquzQuestion']['set_variants']) && (int)$post_data['FoquzQuestion']['set_variants'] !== (int)$this->model->set_variants) {
                    throw new \InvalidArgumentException('Изменить параметр «Тип рейтинга» нельзя, так как по вопросу собирается статистика');
                }
                if (isset($post_data['FoquzQuestion']['donor']) && (int)$post_data['FoquzQuestion']['donor'] !== $this->model->donor) {
                    throw new \InvalidArgumentException('Изменить параметр «ID донора» нельзя, так как по вопросу собирается статистика');
                }
            }
            return '';
        } catch (\InvalidArgumentException $e) {
            return $e->getMessage();
        }
    }

    /**
     * @return void
     * @throws Exception
     */
    function saveMedia(): void
    {
        $images = Yii::$app->getRequest()->post('images', []);
        foreach ($images as $key => $image) {
            $imageModel = FoquzQuestionFile::findOne($key);
            $imageModel->file_text = $image;
            $imageModel->save();
        }

        $images = Yii::$app->getRequest()->post('Videos', []);
        foreach ($images as $key => $image) {
            $imageModel = FoquzQuestionFile::findOne($key);
            $imageModel->file_text = $image;
            $imageModel->save();
        }
    }

    /**
     * Все, что касается строк / вариантов / уточняющих вопросов
     * @param FoquzQuestion|null $donorModel
     * @param array $post_data
     * @param  ?int $donor
     * @return bool
     * @throws Exception
     * @throws NotFoundHttpException
     * @throws StaleObjectException
     * @throws Throwable
     */
    private function details(
        ?FoquzQuestion $donorModel,
        array $post_data,
        ?int $donor
    ): bool {
        $extraQuestionService = new ExtraQuestionService();

        if (!empty($post_data['FoquzQuestionDetail'])) {
            //удаление логических правил в реципиентах
            if (!$this->deleteLogicConditionsRecipients($post_data['FoquzQuestionDetail'])) {
                return false;
            }

            $detailsIds = [];
            $detailsPosition = $extraDetailPosition = 1;

            $recipients = FoquzQuestion::find()
                ->where(['donor' => $this->model->id, 'is_deleted' => 0])
                ->andWhere(['!=', 'id', $donor])
                ->all();
            $recipientDetailIds = [];
            foreach ($post_data['FoquzQuestionDetail'] as $detail) {
                $detailQuestion = null;
                if (isset($detail['extra_question']) && $detail['extra_question']) {

                    $detailId = $extraQuestionService->setExtraQuestion(
                        $detail,
                        $this->model->id,
                        $extraDetailPosition++,
                    );
                    $detailsIds[] = $detailId;
                    /** Связь варианта в уточняющем вопросе с изображением */
                    if (isset($detail['file_id'])) {
                        $extraQuestionService->linkImage(
                            $detailId,
                            $detail['file_id'],
                            FoquzFile::TYPE_DETAIL
                        );
                    }

                } else {
                    // обновление связей реципиента с донором
                    if (!empty($donorModel)) {
                        while ($donorModel->donor && $donorModel->id != $donorModel->donor) {
                            $donorModel = FoquzQuestion::findOne(['id' => $donorModel->donor, 'is_deleted' => 0]);
                            if (!$donorModel) {
                                throw new NotFoundHttpException('Вопрос-донор с id ' . $post_data['FoquzQuestion']['donor'] . ' не найден');
                            }
                        }

                        if ((int)$donorModel->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                            if (isset($detail['id']) && !FoquzQuestionDetail::find()->where([
                                    'id'                => $detail['id'],
                                    'foquz_question_id' => $donorModel->id
                                ])->exists()) {
                                throw new NotFoundHttpException("Вариант-донор не найден");
                            }
                        } elseif ((int)$donorModel->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                            if (!DictionaryElement::find()->where(['id' => $detail['id']])->exists()) {
                                throw new NotFoundHttpException("Вариант-донор не найден");
                            }
                        }

                        $recipientDetail = null;
                        if ($donorModel->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                            $recipientDetail = RecipientQuestionDetail::findOne([
                                'recipient_id'       => $this->model->id,
                                'question_detail_id' => $detail['id'] ?? null,
                            ]);
                        } elseif ($donorModel->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                            $recipientDetail = RecipientQuestionDetail::findOne([
                                'recipient_id'          => $this->model->id,
                                'dictionary_element_id' => $detail['id'] ?? null,
                            ]);
                        }
                        if (!$recipientDetail) {
                            $recipientDetail = new RecipientQuestionDetail();
                        }
                        $recipientDetail->recipient_id = $this->model->id;
                        $recipientDetail->description = $detail['description'] ?? null;
                        $recipientDetail->type = $detail['type'] ?? FoquzQuestionDetail::TYPE_GENERAL;
                        $recipientDetail->position = $detail['position'] ?? $detailsPosition++;
                        $recipientDetail->need_extra = $detail['need_extra'] ?? 0;
                        $recipientDetail->random_exclusion = $detail['random_exclusion'] ?? 0;

                        if (
                            !empty($detail['comment_required']) &&
                            (int)$this->model->main_question_type === FoquzQuestion::TYPE_VARIANTS
                        ) {
                            $recipientDetail->comment_required = true;
                        } else {
                            $recipientDetail->comment_required = false;
                        }

                        if ($donorModel->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                            $recipientDetail->question_detail_id = $detail['id'] ?? null;
                            $recipientDetail->dictionary_element_id = null;
                        } elseif ($donorModel->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                            $recipientDetail->question_detail_id = null;
                            $recipientDetail->dictionary_element_id = $detail['id'] ?? null;
                        } else {
                            $recipientDetail->question_detail_id = null;
                            $recipientDetail->dictionary_element_id = null;
                        }

                        if (empty($detail['without_points'])) {
                            $recipientDetail->points = $detail['points'] ?? null;
                            $recipientDetail->without_points = false;
                        } else {
                            $recipientDetail->points = null;
                            $recipientDetail->without_points = true;
                        }

                        $recipientDetail->detail_question = $detail['detail_question'] ?? null;
                        $recipientDetail->self_variant_text = $detail['self_variant_text'] ?? null;
                        $recipientDetail->variants_element_type = $detail['variants_element_type'] ?? 0;
                        $recipientDetail->extra_question_rate_from = $detail['extra_question_rate_from'] ?? null;
                        $recipientDetail->extra_question_rate_to = $detail['extra_question_rate_to'] ?? null;
                        $recipientDetail->min_choose_extra_variants = $detail['min_choose_extra_variants'] ?? null;
                        $recipientDetail->max_choose_extra_variants = $detail['max_choose_extra_variants'] ?? null;
                        $recipientDetail->extra_required = $detail['extra_required'] ?? 0;
                        $recipientDetail->self_variant_minlength = $detail['self_variant_minlength'] ?? null;
                        $recipientDetail->self_variant_maxlength = $detail['self_variant_maxlength'] ?? null;
                        $recipientDetail->text_variant_minlength = $detail['text_variant_minlength'] ?? null;
                        $recipientDetail->text_variant_maxlength = $detail['text_variant_maxlength'] ?? null;
                        $recipientDetail->self_variant_placeholder_text = $detail['self_variant_placeholder_text'] ?? null;
                        $recipientDetail->for_all_rates = $detail['for_all_rates'] ?? 1;
                        $recipientDetail->placeholder_text = $detail['placeholder_text'] ?? null;
                        $recipientDetail->variants_with_files = $detail['variants_with_files'] ?? 0;
                        $recipientDetail->is_self_answer = $detail['is_self_answer'] ?? 0;

                        if (!$recipientDetail->save()) {
                            throw new Exception('Не удалось сохранить вариант для вопроса-реципиента',
                                $recipientDetail->errors);
                        }

                        // Разные УВ для каждого варианта
                        if (!empty($detail['detail_question'])) {
                            if (isset($detail['detail_question_options']) && is_array($detail['detail_question_options'])) {
                                $dqOptionPosition = 1;
                                foreach ($detail['detail_question_options'] as $detail_question_option) {
                                    if (!$detail['variants_with_files']) {
                                        $detail_question_option["file"] = null;
                                        $detail_question_option["file_id"] = null;
                                    }
                                    $detailQuestionOptionId = $extraQuestionService->setExtraQuestion(
                                        $detail_question_option,
                                        $recipientDetail->recipient_id,
                                        $dqOptionPosition++,

                                        $recipientDetail->question_detail_id,
                                        $recipientDetail->id,
                                    );

                                    $detailsIds[] = $detailQuestionOptionId;

                                    /** Связь варианта ответа в уточняющем вопросе с изображением */
                                    if (isset($detail_question_option['file_id'])) {
                                        $extraQuestionService->linkImage(
                                            $detailQuestionOptionId,
                                            $detail_question_option['file_id'],
                                            FoquzFile::TYPE_DETAIL
                                        );
                                    }
                                }
                            }
                        }

                        /** Связь своего варианта в уточняющем вопросе с изображением */
                        if (isset($detail['self_variant_file_id'])) {
                            $extraQuestionService->linkImage($recipientDetail->id, $detail['self_variant_file_id']);
                        }
                    } else {
                        if (
                            (
                                !isset($detail['value']) ||
                                $detail['value'] === ''
                            ) && (
                                empty($detail['file_id']) ||
                                !$this->model->variants_with_files
                            )
                        ) {
                            continue;
                        }
                        if (isset($detail['id']) && $detail['id']) {
                            $detailQuestion = FoquzQuestionDetail::findOne($detail['id']);
                        } else {
                            $detailQuestion = new FoquzQuestionDetail();
                        }
                        if ($detailQuestion === null || $detailQuestion->foquz_question_id !== $this->model->id) {
                            $detailQuestion = new FoquzQuestionDetail();
                        }
                        $detailQuestion->foquz_question_id = $this->model->id;
                        $detailQuestion->type = $detail['type'] ?? FoquzQuestionDetail::TYPE_GENERAL;
                        $detailQuestion->question = $detail['value'] ?? '';
                        $detailQuestion->description = $detail['description'] ?? null;
                        $detailQuestion->is_empty = false;
                        $detailQuestion->position = $detail['position'] ?? $detailsPosition++;
                        $detailQuestion->need_extra = $detail['need_extra'] ?? 0;
                        $detailQuestion->dictionary_element_id = $detail['dictionary_element_id'] ?? null;
                        $detailQuestion->is_deleted = false;

                        if (
                            !empty($detail['comment_required']) &&
                            (int)$this->model->main_question_type === FoquzQuestion::TYPE_VARIANTS
                        ) {
                            $detailQuestion->comment_required = true;
                        } else {
                            $detailQuestion->comment_required = false;
                        }

                        if (isset($detail['points']) && $detail['points'] !== '-') {
                            $detailQuestion->points = $detail['points'];
                            $detailQuestion->without_points = false;
                        } else {
                            $detailQuestion->points = null;
                            $detailQuestion->without_points = true;
                        }

                        $detailQuestion->variants_with_files = $detail['variants_with_files'] ?? 0;
                        $detailQuestion->extra_question_rate_from = $detail['extra_question_rate_from'] ?? null;
                        $detailQuestion->extra_question_rate_to = $detail['extra_question_rate_to'] ?? null;
                        $detailQuestion->extra_required = $detail['extra_required'] ?? 0;
                        $detailQuestion->min_choose_extra_variants = $detail['min_choose_extra_variants'] ?? null;
                        $detailQuestion->max_choose_extra_variants = $detail['max_choose_extra_variants'] ?? null;
                        $detailQuestion->self_variant_text = $detail['self_variant_text'] ?? null;
                        $detailQuestion->self_variant_placeholder_text = $detail['self_variant_placeholder_text'] ?? null;
                        $detailQuestion->variants_element_type = $detail['variants_element_type'] ?? 0;
                        $detailQuestion->for_all_rates = $detail['for_all_rates'] ?? 1;
                        $detailQuestion->placeholder_text = $detail['placeholder_text'] ?? null;
                        $detailQuestion->self_variant_minlength = $detail['self_variant_minlength'] ?? null;
                        $detailQuestion->self_variant_maxlength = $detail['self_variant_maxlength'] ?? null;
                        $detailQuestion->text_variant_minlength = $detail['text_variant_minlength'] ?? null;
                        $detailQuestion->text_variant_maxlength = $detail['text_variant_maxlength'] ?? null;
                        $detailQuestion->is_self_answer = $detail['is_self_answer'] ?? 0;
                        $detailQuestion->detail_question = $detail['detail_question'] ?? null;
                        $detailQuestion->random_exclusion = isset($detail['random_exclusion']) && (bool)$detail['random_exclusion'];
                        $detailQuestion->save();

                        // Разные УВ для каждого варианта
                        if (!empty($detail['detail_question'])) {
                            if (isset($detail['detail_question_options']) && is_array($detail['detail_question_options'])) {
                                $dqOptionPosition = 1;
                                foreach ($detail['detail_question_options'] as $detail_question_option) {
                                    if (!$detailQuestion->variants_with_files) {
                                        $detail_question_option["file"] = null;
                                        $detail_question_option["file_id"] = null;
                                    }

                                    $detailQuestionOptionId = $extraQuestionService->setExtraQuestion(
                                        $detail_question_option,
                                        $detailQuestion->foquz_question_id,
                                        $dqOptionPosition++,
                                        $detailQuestion->id
                                    );

                                    $detailsIds[] = $detailQuestionOptionId;

                                    /** Связь варианта ответа в уточняющем вопросе с изображением */
                                    if (isset($detail_question_option['file_id'])) {
                                        $extraQuestionService->linkImage(
                                            $detailQuestionOptionId,
                                            $detail_question_option['file_id'],
                                            FoquzFile::TYPE_DETAIL
                                        );
                                    }
                                }
                            }
                        }

                        /** Связь своего варианта в уточняющем вопросе с изображением */
                        if (isset($detail['self_variant_file_id'])) {
                            $extraQuestionService->linkImage($detailQuestion->id, $detail['self_variant_file_id']);
                        }

                        $detailQuestion->refresh();

                        // Связывание медиа с вариантом ответа
                        if ((int)$this->model->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                            $extraQuestionService->setFile($detail, $detailQuestion, $detailsPosition++);
                            FoquzFile::deleteAll([
                                'AND',
                                ['entity_id' => $detailQuestion->id],
                                ['entity_type' => FoquzFile::TYPE_DETAIL],
                                ['NOT IN', 'id', $detail['file_id'] ?? []]
                            ]);
                        }

                        $detailsIds[] = $detailQuestion->id;
                        // добавление связей реципиентам
                        if ($recipients) {
                            /** @var FoquzQuestion $recipient */
                            foreach ($recipients as $recipient) {
                                $recipientDetail = RecipientQuestionDetail::findOne([
                                    'question_detail_id' => $detailQuestion->id,
                                    'recipient_id'       => $recipient->id,
                                ]);
                                if (!$recipientDetail) {
                                    $recipientDetail = new RecipientQuestionDetail();
                                    $recipientDetail->question_detail_id = $detailQuestion->id;
                                    $recipientDetail->recipient_id = $recipient->id;
                                    $recipientDetail->description = $detail['description'] ?? null;
                                    $recipientDetail->type = $detail['type'] ?? FoquzQuestionDetail::TYPE_GENERAL;
                                    $recipientDetail->points = $detail['points'] ?? null;
                                    $recipientDetail->position = RecipientQuestionDetail::find()
                                            ->where(['recipient_id' => $recipient->id])
                                            ->orderBy(['position' => SORT_DESC])
                                            ->select('position')
                                            ->scalar() + 1;

                                    $recipientDetail->save();
                                }
                                $recipientDetailIds[$recipient->id][] = $detailQuestion->id;
                            }
                        }
                    }
                }
            }
            if (count($detailsIds) > 0) {
                if ($this->model->questionAnswerItems) {
                    FoquzQuestionDetail::updateAll(['is_deleted' => true], [
                        'AND',
                        ['foquz_question_id' => $this->model->id],
                        ['NOT IN', 'id', $detailsIds]
                    ]);
                } else {
                    FoquzQuestionDetail::deleteAll([
                        'AND',
                        ['foquz_question_id' => $this->model->id],
                        ['NOT IN', 'id', $detailsIds]
                    ]);
                }
            }
            foreach ($recipientDetailIds as $recipientId => $recipientDetailId) {
                if (!$this->model->questionAnswerItems) {
                    RecipientQuestionDetail::deleteAll([
                        'AND',
                        ['recipient_id' => $recipientId],
                        ['NOT IN', 'question_detail_id', $recipientDetailId]
                    ]);
                }
            }
        }

        /** Связь своего варианта в уточняющем вопросе с изображением */
        if (isset($post_data['FoquzQuestion']['self_variant_file_id'])) {
            $extraQuestionService->linkImage($this->model->id, (int)$post_data['FoquzQuestion']['self_variant_file_id']);
        }

        /** Обновление вариантов у реципиентов для донора классификатора */
        if ((int)$this->model->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
            $selectedElements = json_decode($this->model->detail_question, false) ?? [];
            /** @var FoquzQuestion[] $recipients */
            $recipients = FoquzQuestion::find()
                ->where(['donor' => $this->model->id, 'is_deleted' => 0])
                ->andWhere(['main_question_type' => FoquzQuestion::TYPES_RECIPIENTS])
                ->all();
            foreach ($recipients as $recipient) {
                RecipientQuestionDetail::deleteAll([
                    'dictionary_element_id' => $selectedElements,
                    'recipient_id'          => $recipient->id
                ]);
                $recipientDetails = RecipientQuestionDetail::find()
                    ->where(['recipient_id' => $recipient->id])
                    ->all();
                $recipientDetails = ArrayHelper::index($recipientDetails, 'dictionary_element_id');
                foreach ($selectedElements as $key => $element) {
                    $recipientDetail = $recipientDetails[$element] ?? new RecipientQuestionDetail();
                    $recipientDetail->recipient_id = $recipient->id;
                    $recipientDetail->dictionary_element_id = $element;
                    $recipientDetail->question_detail_id = null;
                    $recipientDetail->position = $key + 1;
                    $recipientDetail->save();
                }
            }
        }

        /** Если меняем названия вариантов в доноре, обновляем название строк матриц */
        if ((int)$this->model->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
            $recipients = FoquzQuestion::find()
                ->where(['donor' => $this->model->id, 'is_deleted' => 0])
                ->andWhere(['main_question_type' => FoquzQuestion::TYPE_SIMPLE_MATRIX])
                ->all();

            if (!empty($recipients)) {
                $variants = ArrayHelper::getColumn($this->model->questionDetailsActive, 'question');
                /** @var FoquzQuestion $recipient */
                foreach ($recipients as $recipient) {
                    $matrixSettings = json_decode($recipient->matrix_settings, false) ?? [];
                    if (is_object($matrixSettings)) {
                        $matrixSettings->rows = $variants;
                        $recipient->matrix_settings = json_encode($matrixSettings,
                            JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                        $recipient->save();
                    }
                }
            }
        } elseif ((int)$this->model->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
            $recipients = FoquzQuestion::find()
                ->where(['donor' => $this->model->id, 'is_deleted' => 0])
                ->andWhere(['main_question_type' => FoquzQuestion::TYPE_SIMPLE_MATRIX])
                ->all();

            if (!empty($recipients)) {
                $selectedElements = json_decode($this->model->detail_question, false) ?? [];
                $elements = DictionaryElement::findAll(['id' => $selectedElements, 'deleted' => 0]);
                $variants = [];
                foreach ($elements as $element) {
                    $variants[] = $element->fullPath;
                }
                foreach ($recipients as $recipient) {
                    $matrixSettings = json_decode($recipient->matrix_settings, false) ?? [];
                    if (is_object($matrixSettings)) {
                        $matrixSettings->rows = $variants;
                        $matrixSettings->donorRows = array_map('strval', ArrayHelper::getColumn($elements, 'id'));
                        $recipient->matrix_settings = json_encode($matrixSettings,
                            JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                        $recipient->save();
                    }
                }
            }

            /** @var FoquzQuestion[] $recipients */
            $recipients = FoquzQuestion::find()
                ->where(['is_deleted' => 0])
                ->andWhere(['OR', ['donor_columns' => $this->model->id], ['donor_rows' => $this->model->id]])
                ->andWhere(['main_question_type' => FoquzQuestion::TYPE_3D_MATRIX])
                ->all();

            if (!empty($recipients)) {
                $selectedElements = json_decode($this->model->detail_question, false) ?? [];
                $elements = DictionaryElement::findAll(['id' => $selectedElements]);
                /** @var DictionaryElement[] $elements */
                $elements = ArrayHelper::index($elements, 'id');
                foreach ($recipients as $recipient) {
                    if ($recipient->donor_rows === $this->model->id) {
                        $newElements = $selectedElements;
                        FoquzQuestionMatrixElement::updateAll(
                            ['is_deleted' => 1],
                            [
                                'AND',
                                ['foquz_question_id' => $recipient->id],
                                ['type_id' => FoquzQuestionMatrixElement::TYPE_ROW],
                                ['NOT IN', 'donor_dictionary_element_id', $selectedElements]
                            ]
                        );
                        $currentElements = FoquzQuestionMatrixElement::find()
                            ->where([
                                'foquz_question_id' => $recipient->id,
                                'type_id'           => FoquzQuestionMatrixElement::TYPE_ROW,
                                'is_deleted'        => 0
                            ])
                            ->andWhere(['NOT', ['donor_dictionary_element_id' => null]])
                            ->all();
                        /** @var FoquzQuestionMatrixElement $currentElement */
                        foreach ($currentElements as $currentElement) {
                            if (!isset($elements[$currentElement->donor_dictionary_element_id])) {
                                $currentElement->is_deleted = 1;
                                $currentElement->save();
                                continue;
                            }
                            if ($currentElement->name !== $elements[$currentElement->donor_dictionary_element_id]->title) {
                                $currentElement->name = $elements[$currentElement->donor_dictionary_element_id]->title;
                                $currentElement->save();
                            }
                            $index = array_search($currentElement->donor_dictionary_element_id, $newElements);
                            if ($index !== false) {
                                unset($newElements[$index]);
                            }
                        }
                        foreach ($newElements as $element) {
                            $matrixElement = new FoquzQuestionMatrixElement();
                            $matrixElement->foquz_question_id = $recipient->id;
                            $matrixElement->type_id = FoquzQuestionMatrixElement::TYPE_ROW;
                            $matrixElement->name = $elements[$element]->title ?? '';
                            $matrixElement->description = $elements[$element]->description ?? '';
                            $matrixElement->donor_dictionary_element_id = $element;
                            $matrixElement->position = FoquzQuestionMatrixElement::find()
                                ->select('MAX(position) + 1')
                                ->where([
                                    'foquz_question_id' => $recipient->id,
                                    'type_id'           => FoquzQuestionMatrixElement::TYPE_ROW
                                ])->scalar();
                            $matrixElement->save();
                        }
                    }

                    if ($recipient->donor_columns === $this->model->id) {
                        $newElements = $selectedElements;
                        FoquzQuestionMatrixElement::updateAll(
                            ['is_deleted' => 1],
                            [
                                'AND',
                                'foquz_question_id' => $recipient->id,
                                'type_id'           => FoquzQuestionMatrixElement::TYPE_COLUMN,
                                ['NOT IN', 'donor_dictionary_element_id', $selectedElements]
                            ]
                        );
                        $currentElements = FoquzQuestionMatrixElement::find()
                            ->where([
                                'foquz_question_id' => $recipient->id,
                                'type_id'           => FoquzQuestionMatrixElement::TYPE_COLUMN,
                                'is_deleted'        => 0
                            ])
                            ->andWhere(['NOT', ['donor_dictionary_element_id' => null]])
                            ->all();
                        /** @var FoquzQuestionMatrixElement $currentElement */
                        foreach ($currentElements as $currentElement) {
                            if (!isset($elements[$currentElement->donor_dictionary_element_id])) {
                                $currentElement->is_deleted = 1;
                                $currentElement->save();
                                continue;
                            }
                            if ($currentElement->name !== $elements[$currentElement->donor_dictionary_element_id]->title) {
                                $currentElement->name = $elements[$currentElement->donor_dictionary_element_id]->title;
                                $currentElement->save();
                            }
                            $index = array_search($currentElement->donor_dictionary_element_id, $newElements);
                            if ($index !== false) {
                                unset($newElements[$index]);
                            }
                        }
                        foreach ($newElements as $element) {
                            $matrixElement = new FoquzQuestionMatrixElement();
                            $matrixElement->foquz_question_id = $recipient->id;
                            $matrixElement->type_id = FoquzQuestionMatrixElement::TYPE_COLUMN;
                            $matrixElement->name = $elements[$element]->title ?? '';
                            $matrixElement->description = $elements[$element]->description ?? '';
                            $matrixElement->donor_dictionary_element_id = $element;
                            $matrixElement->position = FoquzQuestionMatrixElement::find()
                                ->select('MAX(position) + 1')
                                ->where([
                                    'foquz_question_id' => $recipient->id,
                                    'type_id'           => FoquzQuestionMatrixElement::TYPE_COLUMN
                                ])->scalar();
                            $matrixElement->save();
                        }
                    }
                }
            }
        }

        return true;
    }

    /**
     * Добавление/редактирование области к изображению для вопроса типа Тест первого клика
     * @param array $post_data
     * @param int $id
     * @return array
     * @throws Exception
     */
    private function saveFirstClickArea(array $post_data, int $id): array
    {
        $firstClickAreaIds = [];
        if (isset($post_data['firstClickArea'])) {
            foreach ($post_data['firstClickArea'] as $firstClickArea) {
                $firstClickArea = FoquzQuestionFirstClickArea::createOrUpdate($firstClickArea, $id);
                if ($firstClickArea && !$firstClickArea->save()) {
                    Yii::$app->response->statusCode = 400;
                    return ['errors' => $firstClickArea->errors];
                }
                $firstClickAreaIds[] = $firstClickArea->id;
            }
        }
        if (count($firstClickAreaIds)) {
            FoquzQuestionFirstClickArea::deleteAll([
                'AND',
                ['question_id' => $id],
                ['NOT IN', 'id', $firstClickAreaIds]
            ]);
        } else {
            FoquzQuestionFirstClickArea::deleteAll(['question_id' => $id]);
        }

        return ['model' => $this->model];
    }

    /**
     * Удаление логических правил реципиентов,
     * связанных с удаляемыми вариантами ответа
     * @param array $details
     * @return bool
     */
    private function deleteLogicConditionsRecipients(array $details): bool
    {
        $poll = $this->model->poll;

        if ((int)$this->model->main_question_type === FoquzQuestion::TYPE_CARD_SORTING_CLOSED) {
            foreach ($details as $detail) {
                if (empty($detail['value'])) {
                    Yii::$app->response->statusCode = 400;
                    return ['errors' => ['question_detail' => ['field' => 'question', 'message' => 'Обязательное поле']]];
                }
            }
        }


        /** @var FoquzQuestionDetail[] $detailsForDelete */
        $detailsForDelete = FoquzQuestionDetail::find()
            ->where([
                'AND',
                ['foquz_question_id' => $this->model->id],
                ['NOT IN', 'id', array_filter(ArrayHelper::getColumn($details, 'id'))]
            ])
            ->all();
        if (empty($detailsForDelete)) {
            return true;
        }

        //ищем всех реципиентов
        $recipients = [];
        $questionIndexes = [];
        foreach ($poll->foquzQuestions as $key => $question) {
            if (
                (int)$question->main_question_type === FoquzQuestion::TYPE_INTERMEDIATE_BLOCK &&
                $question->intermediateBlock &&
                in_array($question->intermediateBlock->screen_type, [
                    FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_START,
                    FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_END
                ])
            ) {
                continue;
            }

            $questionIndexes[$question->id] = $key + 1;

            if (!in_array($question->main_question_type, FoquzQuestion::TYPES_RECIPIENTS)) {
                continue;
            }

            if ($question->donor === $this->model->id) {
                $recipients[] = $question;
                continue;
            }

            $mainDonor = $question->getMainDonor();
            if ($mainDonor && $mainDonor->id === $this->model->id) {
                $recipients[] = $question;
            }
        }

        /** @var FoquzQuestion[] $recipients */
        $recipients = ArrayHelper::index($recipients, 'id');
        $recipientIDs = array_keys($recipients);
        $recipientDetails = RecipientQuestionDetail::find()
            ->where(['recipient_id' => $recipientIDs])
            ->all();
        $recipientDetails = ArrayHelper::index($recipientDetails, null, 'question_detail_id');
        /** @var FoquzPollQuestionViewLogic[] $viewLogicConditions */
        $viewLogicConditions = FoquzPollQuestionViewLogic::find()
            ->where(['condition_question_id' => ArrayHelper::merge([$this->model->id], $recipientIDs)])
            ->all();
        $conditionQuestions = ArrayHelper::merge([$this->model->id => $this->model], $recipients);

        $problematicQuestions = [];
        foreach ($viewLogicConditions as $viewLogicCondition) {
            foreach ($detailsForDelete as $detailForDelete) {
                $conditionQuestion = $conditionQuestions[$viewLogicCondition->condition_question_id] ?? null;
                if ($conditionQuestion && (int)$conditionQuestion->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
                    $logicVariantExist = in_array($detailForDelete->question,
                            ArrayHelper::getColumn($viewLogicCondition->variants,
                                'row')) || in_array($detailForDelete->question,
                            $viewLogicCondition->skipped);
                    if ($logicVariantExist) {
                        $problematicQuestions[] = $questionIndexes[$viewLogicCondition->question_id];
                    }
                } elseif ($conditionQuestion && (int)$conditionQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                    $logicVariantExist = in_array($detailForDelete->id,
                            $viewLogicCondition->variants) || in_array($detailForDelete->id,
                            $viewLogicCondition->skipped);
                    if ($logicVariantExist) {
                        $problematicQuestions[] = $questionIndexes[$viewLogicCondition->question_id];
                    } else {
                        $questionRecipientDetails = $recipientDetails[$detailForDelete->id] ?? [];
                        /** @var RecipientQuestionDetail $questionRecipientDetail */
                        foreach ($questionRecipientDetails as $questionRecipientDetail) {
                            $logicVariantExist = in_array($questionRecipientDetail->id,
                                    $viewLogicCondition->variants) || in_array($questionRecipientDetail->id,
                                    $viewLogicCondition->skipped);
                            if ($logicVariantExist) {
                                $problematicQuestions[] = $questionIndexes[$viewLogicCondition->question_id];
                            }
                        }
                    }
                } else {
                    $logicVariantExist = in_array($detailForDelete->id,
                            ArrayHelper::getColumn($viewLogicCondition->variants,
                                'row')) || in_array($detailForDelete->id, $viewLogicCondition->skipped);
                    if ($logicVariantExist) {
                        $problematicQuestions[] = $questionIndexes[$viewLogicCondition->question_id];
                    } else {
                        $questionRecipientDetails = $recipientDetails[$detailForDelete->id] ?? [];
                        /** @var RecipientQuestionDetail $questionRecipientDetail */
                        foreach ($questionRecipientDetails as $questionRecipientDetail) {
                            $logicVariantExist = in_array($questionRecipientDetail->id,
                                    ArrayHelper::getColumn($viewLogicCondition->variants,
                                        'row')) || in_array($questionRecipientDetail->id,
                                    $viewLogicCondition->skipped);
                            if ($logicVariantExist) {
                                $problematicQuestions[] = $questionIndexes[$viewLogicCondition->question_id];
                            }
                        }
                    }
                }
            }
        }
        if (!empty($problematicQuestions)) {
            $problematicQuestions = array_unique($problematicQuestions);
            if (count($problematicQuestions) > 10) {
                $problematicQuestions = array_slice($problematicQuestions, 0, 10);
                $errorText = 'Удалить варианты невозможно, так как они используются в условиях отображения вопросов №' . implode(', ',
                        $problematicQuestions) . ' и др.';
            } elseif (count($problematicQuestions) > 1) {
                $errorText = 'Удалить варианты невозможно, так как они используются в условиях отображения вопросов №' . implode(', ',
                        $problematicQuestions);
            } else {
                $errorText = 'Удалить варианты невозможно, так как они используются в условиях отображения вопроса №' . implode(', ',
                        $problematicQuestions);
            }
            return $this->error($errorText);
        }

        return true;
    }

    private string $errorText;

    private function error(string $errorText): bool
    {
        $this->errorText = $errorText;
        return false;
    }

    /**
     * Настройки вопроса - Промежуточный блок.
     * @param array $post_data
     * @return void
     * @throws Exception
     */
    private function intermediateBlock(array $post_data): void
    {
        //если не промежуточный блок, то удаляем настройки промежуточного блока
        if ((int) $this->model->main_question_type !== FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
            FoquzQuestionIntermediateBlockSetting::deleteAll(['question_id' => $this->model->id]);
            return;
        }

        //общие настройки
        $intermediateBlock = $this->model->intermediateBlock ?? new FoquzQuestionIntermediateBlockSetting([
            'question_id' => $this->model->id
        ]);
        $intermediateBlock->load($post_data['IntermediateBlockSetting'], '');
        $intermediateBlock->save();

        //социальные сети
        if (isset($post_data['IntermediateBlockNetworks'])) {
            $intermediateBlockNetworks = $intermediateBlock->socNetworks ?? new FoquzQuestionIntermediateBlockSettingSocNetworks([
                'intermediate_block_id' => $intermediateBlock->id
            ]);
            $intermediateBlockNetworks->load($post_data['IntermediateBlockNetworks'], '');
            if (isset($post_data['IntermediateBlockNetworks']['social_networks'])) {
                $intermediateBlockNetworks->social_networks = json_encode($post_data['IntermediateBlockNetworks']['social_networks']);
            }
            $intermediateBlockNetworks->save();
        }

        //текстовый экран
        if ($intermediateBlock->screen_type != FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_TEXT &&
            $intermediateBlock->screen_type != FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_TEST5S
        ) {
            FoquzPollDisplayPageQuestion::deleteAll(['question_id' => $this->model->id]);
        }

        //лого на финальном экране
        $endScreenLogoService = new QuestionEndScreenLogoService($this->model);
        if (isset($post_data['FoquzQuestion']['endScreenLogos']) && count($post_data['FoquzQuestion']['endScreenLogos']) > 0) {
            $endScreenLogoService->updateFiles($post_data['FoquzQuestion']['endScreenLogos']);
        }

        // для Тест 5сек изображение обязательно
        if ((int)$intermediateBlock->screen_type === FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_TEST5S) {
            if (empty($post_data['FoquzQuestion']['endScreenLogos'][0]['id'])) {
                throw new \Exception(Yii::t('main','Необходимо загрузить изображение'));
            }
        }

        $endScreenLogoService->deleteQuestionEndScreenLogos();

    }

    private function getQuestionNumbers(FoquzPoll $poll): array
    {
        $questionIndexes = [];
        foreach ($poll->foquzQuestions as $key => $question) {
            if (
                (int)$question->main_question_type === FoquzQuestion::TYPE_INTERMEDIATE_BLOCK &&
                $question->intermediateBlock &&
                in_array($question->intermediateBlock->screen_type, [
                    FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_START,
                    FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_END
                ])
            ) {
                continue;
            }
            $questionIndexes[$question->id] = $key + 1;
        }
        return $questionIndexes;
    }

    private function returnError(string|null|array $error = null): array
    {
        if (empty($error)) {
            $error = $this->errorText ?? '';
        }
        $this->model->refresh();
        Yii::$app->response->statusCode = 400;
        return ['question' => $this->model, 'error' => $error];
    }
}