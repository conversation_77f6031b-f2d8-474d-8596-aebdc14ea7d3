<?php

declare(strict_types=1);

namespace app\modules\foquz\services\api;

use app\modules\foquz\models\FoquzFile;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\services\FileService;
use Throwable;
use yii\db\Exception;
use yii\db\StaleObjectException;

class ExtraQuestionService
{
    /**
     * @param array $detail
     * @param int $foquzQuestionId
     * @param int $extraDetailPosition
     * @param int|null $questionDetailId - id варианта опроса
     * @param int|null $recipientDetailId
     * @return int
     * @throws Exception
     * @throws StaleObjectException
     * @throws Throwable
     */
    public function setExtraQuestion(
        array $detail,
        int $foquzQuestionId,
        int $extraDetailPosition,
        int|null $questionDetailId = null,
        int|null $recipientDetailId = null,
    ): int
    {
        $detailQuestion = null;
        if (isset($detail['id']) && $detail['id']) {
            $detailQuestion = FoquzQuestionDetail::findOne($detail['id']);
        }
        if (!$detailQuestion) {
            $detailQuestion = new FoquzQuestionDetail();
        }
        $detailQuestion->foquz_question_id = $foquzQuestionId;
        $detailQuestion->question = $detail['value'];
        $detailQuestion->is_empty = false;
        $detailQuestion->position = $detail['position'] ?? $extraDetailPosition;
        $detailQuestion->extra_question = 1;
        $detailQuestion->question_detail_id = $questionDetailId;
        $detailQuestion->recipient_question_detail_id = $recipientDetailId;
        $detailQuestion->save();

        $this->setFile($detail, $detailQuestion, $extraDetailPosition);

        return $detailQuestion->id;
    }

    /**
     * Связать медиа с вариантом ответа
     *
     * @param array $detail
     * @param FoquzQuestionDetail $detailQuestion
     * @param int $extraDetailPosition
     * @return void
     * @throws Exception
     * @throws StaleObjectException
     * @throws Throwable
     */
    public function setFile(array $detail, FoquzQuestionDetail $detailQuestion, int $extraDetailPosition): void
    {
        if (!empty($detail['file_id'])) {
            if ($detailQuestion->question === '') {
                $name = ($extraDetailPosition > 0) ? 'Вариант ' . $extraDetailPosition : 'Вариант ' . $detailQuestion->position;
                $detailQuestion->question = $name;
                $detailQuestion->is_empty = true;
                $detailQuestion->save();
            } elseif ($detailQuestion->is_empty) {
                $detailQuestion->is_empty = false;
                $detailQuestion->save();
            }
            /** @var FoquzFile|null $detailFile */
            $detailFile = FoquzFile::find()
                ->where(['id' => $detail['file_id']])
                ->andWhere(['entity_id' => 0, 'entity_type' => FoquzFile::TYPE_DETAIL])
                ->one();

            if ($detailFile) {
                $detailFile->entity_id = $detailQuestion->id;
                $detailFile->save();
            }
        } else {
            $detailFile = FoquzFile::find()
                ->andWhere(['entity_id' => $detailQuestion->id, 'entity_type' => FoquzFile::TYPE_DETAIL])
                ->one();
            $detailFile?->delete();
        }
    }

    /**
     * Обновить привязки к родителям для УВ
     *
     * @param array<FoquzQuestionDetail> $sourceDetailsTemp
     * @return void
     * @throws Exception
     */
    public function fixQuestionDetailId(array $sourceDetailsTemp): void
    {
        foreach ($sourceDetailsTemp as $question_detail) {
            if ($question_detail->question_detail_id) {
                $tkDetail = $sourceDetailsTemp[$question_detail->question_detail_id];
                $question_detail->question_detail_id = $tkDetail->id;
                $question_detail->save();
            }
        }
    }

    /**
     * Получить содержимое столбца Комментарий для выгрузки 1 в Excel
     * для типа опроса EXTRA_QUESTION_DIFFERENT_EACH
     * @param FoquzPollAnswerItem $foquzPollAnswerItem
     * @param bool $withEqAnswers
     * @return string
     */
    public static function getCommentColumnNps(
        FoquzPollAnswerItem $foquzPollAnswerItem,
        bool $withEqAnswers = false
    ): string
    {
        $answer = [];

        $detail_item = $foquzPollAnswerItem->detail_item;
        if ($detail_item && is_array($detail_item)) {
            foreach ($detail_item as $detail_id => $item) {
                if (is_array($item)) {
                    foreach ($item as $di_key => $di_value) {
                        if ($di_key === 'self_variant' || $di_key === 'answer') {
                            if ($withEqAnswers) {
                                $detail = FoquzQuestionDetail::findOne($detail_id);
                                if ($detail) {
                                    $part[] = $detail->question;
                                    $part[] = $detail->detail_question;
                                }
                                $part[] = $di_value;
                                $answer[] = implode(':', $part);
                                unset($part);

                            } else {
                                $answer[] = $di_value;
                            }
                        } else {
                            if ($withEqAnswers) {
                                $detail = FoquzQuestionDetail::findOne((int)$di_value);
                                if ($detail) {
                                    $parent = FoquzQuestionDetail::findOne($detail->question_detail_id);
                                    if ($parent) {
                                        $part[] = $parent->question;
                                        $part[] = $parent->detail_question;

                                    }
                                    $part[] = $detail->question;
                                    $answer[] = implode(':', $part);
                                    unset($part);

                                } else {
                                    $answer[] = '';
                                }
                            }
                        }
                    }
                }
            }
        }
        if ($foquzPollAnswerItem->self_variant) {
            $answer[] = $foquzPollAnswerItem->self_variant;
        }

        return implode("\n", $answer);
    }

    /**
     * Получить содержимое столбца Комментарий для выгрузки 1 в Excel
     * для типа опроса TYPE_VARIANT_STAR
     * @param FoquzPollAnswerItem $foquzPollAnswerItem
     * @param bool $withEqAnswers
     * @return string
     */
    public static function getCommentColumnVariantStar(
        FoquzPollAnswerItem $foquzPollAnswerItem,
        bool $withEqAnswers = false
    ): string
    {
        $answer = [];

        if($foquzPollAnswerItem->answer) {

            $decodedDetail = json_decode($foquzPollAnswerItem->answer ?? '', true) ?? [];
            if (isset($decodedDetail['extra']) && is_array($decodedDetail['extra'])) {
                foreach ($decodedDetail['extra'] as $extra_detail_id => $item) {
                    foreach ($item as $arr_key => $arr_val) {
                        if ($arr_key === 'self_variant' || $arr_key === 'answer') {

                            if ($withEqAnswers) {
                                $detail = FoquzQuestionDetail::findOne($extra_detail_id);
                                if ($detail) {
                                    $part[] = $detail->question;
                                    $part[] = $detail->foquzQuestion->detail_question;
                                }
                                $part[] = $arr_val;
                                $answer[] = implode(': ', $part);
                                unset($part);

                            } else {
                                $answer[] = $arr_val;
                            }
                        } else {
                            if ($withEqAnswers) {
                                $detail = FoquzQuestionDetail::findOne((int)$arr_val);
                                if ($detail) {
                                    $question = $detail->foquzQuestion;
                                    $answer[] = $detail->question . ': ' . $question->detail_question . ': ' . $detail->question;
                                }
                            }
                        }
                    }
                }
            }
            if ($foquzPollAnswerItem->self_variant) {
                $answer[] = $foquzPollAnswerItem->self_variant;
            }
        }

        return implode("\n", $answer);
    }

    /**
     * Получить содержимое столбца Комментарий для выгрузки 1 в Excel
     * для типа опроса TYPE_SMILE_RATING
     * @param FoquzPollAnswerItem $foquzPollAnswerItem
     * @param bool $common
     * @return string
     */
    public static function getCommentColumnSmileRating(
        FoquzPollAnswerItem $foquzPollAnswerItem,
        bool $common = false
    ): string
    {
        $answer = [];
        if (is_array($foquzPollAnswerItem->detail_item)) {
            foreach ($foquzPollAnswerItem->detail_item as $key => $detail_item) {
                if ($key === 'self_variant') { // один ответ на УВ
                    $answer[] = $detail_item;
                } else if ($key === 'text_answer') {
                    $answer[] = $detail_item;
                } else { // несколько ответов на УВ
                    if (is_array($detail_item) && isset($detail_item['self_variant'])) {
                        $answer[] = $detail_item['self_variant'];
                    } else {
                        if (!$common) {
                            $detailModel = FoquzQuestionDetail::findOne($detail_item);
                            if ($detailModel) {
                                $answer[] = $detailModel->question;
                            }
                        }
                    }
                }
            }
        }
        if (!empty($foquzPollAnswerItem->self_variant)) {
            $answer[] = $foquzPollAnswerItem->self_variant;
        }
        return implode("\n", $answer);
    }


    /**
     * Получить содержимое столбца Комментарий для выгрузки 1 в Excel
     * для типа опроса TYPE_SIMPLE_MATRIX
     * @param FoquzPollAnswerItem $foquzPollAnswerItem
     * @return string
     */
    public static function getCommentColumnSimpleMatrix(
        FoquzPollAnswerItem $foquzPollAnswerItem,
    ): string
    {
        $answer = [];
        if (is_array($foquzPollAnswerItem->detail_item)) {
            foreach ($foquzPollAnswerItem->detail_item as $key => $detail_item) {
                foreach ($detail_item as $arr_id => $arr_val) {
                    if ($arr_id === 'self_variant' || $arr_id === 'answer') {
                        $answer[] = $arr_val;
                    } else {
                        $detailModel = FoquzQuestionDetail::findOne($arr_val);
                        if ($detailModel) {
                            $answer[] = $detailModel->question;
                        }
                    }
                }
            }
        }
        if (!empty($foquzPollAnswerItem->self_variant)) {
            $answer[] = $foquzPollAnswerItem->self_variant;
        }
        return implode("\n", $answer);
    }

    /**
     * Получить варианты ответа для УВ
     * @param int $question_detail_id - ID элемента FoquzQuestionDetail в котором лежит УВ
     */
    public function getVariantsForExtraQuestion(int $question_detail_id): array
    {
        return FoquzQuestionDetail::find()
            ->where(['question_detail_id' => $question_detail_id])
            ->all();
    }

    /**
     * Связь своего варианта в уточняющем вопросе с изображением
     * @param int $entityId
     * @param int $selfVariantFileId
     * @param int $entityType
     * @return void
     * @throws Exception
     * @throws Throwable
     * @throws StaleObjectException
     */
    public function linkImage(int $entityId, int $selfVariantFileId, int $entityType = FoquzFile::TYPE_SELF_VARIANT): void
    {
        if ($selfVariantFileId) {
            /** @var FoquzFile|null $detailFile */
            $detailFile = FoquzFile::find()
                ->where(['id' => $selfVariantFileId])
                ->andWhere(['entity_id' => [0, $entityId], 'entity_type' => $entityType])
                ->one();

            if ($detailFile) {
                $detailFile->entity_id = $entityId;
                $detailFile->save();
            }
        }

        $oldFilesIDs = FoquzFile::find()
            ->select(['id'])
            ->where(['entity_id' => $entityId, 'entity_type' => $entityType]);

        if (!empty($detailFile->id)) {
            $oldFilesIDs->andWhere(['NOT', ['id' => $detailFile->id]]);
        }

        $oldFilesIDs = $oldFilesIDs->column();

        foreach ($oldFilesIDs as $oldFilesID) {
            FileService::deleteFile($oldFilesID);
        }
    }

}