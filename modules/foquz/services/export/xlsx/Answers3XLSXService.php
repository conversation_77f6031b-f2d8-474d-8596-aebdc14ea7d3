<?php

namespace app\modules\foquz\services\export\xlsx;

use app\models\DictionaryElement;
use app\models\Export;
use app\models\Filial;
use app\modules\foquz\models\ContactAdditionalField;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollDisplayPage;
use app\modules\foquz\models\FoquzPollDisplaySetting;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionMatrixElement;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariant;
use app\modules\foquz\models\SettingTables;
use app\modules\foquz\services\answers\search\AnswerSearchService;
use app\modules\foquz\services\export\ExportHelper;
use app\modules\foquz\services\export\usecase\CardSortingClosed;
use app\modules\foquz\services\export\usecase\FirstClick;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Yii;
use yii\helpers\ArrayHelper;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;

class Answers3XLSXService extends BaseXLSXService
{
    /** @var string $model */
    public $model = FoquzPollAnswer::class;

    /** @var FoquzPoll $poll */
    public $poll;

    /** @var Export $export */
    public $export;

    /** @var array $columns */
    public $columns;

    /** @var array $additionFields */
    public $additionalFields = [];

    /** @var FoquzQuestion[] $questions */
    public $questions;

    /** @var FoquzQuestion[] $formQuestions */
    public $formQuestions;

    /** @var Filial[] $companyFilials */
    public $companyFilials;

    /** @var array $questionDictionary  */
    public $questionDictionary = [];

    /** @var FoquzQuestionMatrixElementVariant[] $matrixVariants */
    public $matrixVariants;


    public function __construct(Export $export)
    {
        $this->export = $export;
    }

    /**
     * Формирует XLSX файл экспорта
     * @return void
     * @throws NotFoundHttpException
     * @throws BadRequestHttpException|\PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function process(): void
    {
        $time = time();

        ini_set('memory_limit', '2048M');
        $export = $this->export;
        print('Processing...' . PHP_EOL);
        if (empty($export->params['id'])) {
            throw new BadRequestHttpException('Не указан ID опроса');
        }

        $userCompanyID = ExportHelper::getUserCompanyId($export);

        $this->poll = FoquzPoll::find()
            ->with(['displayPages.displayPageQuestions'])
            ->where(['id' => $export->params['id'], 'company_id' => $userCompanyID])
            ->one();
        if (!$this->poll) {
            throw new NotFoundHttpException('Опрос не найден');
        }

        $this->entityType = Export::ENTITES[$export->entity_type];
        $this->fileType = Export::FILETYPES[$export->file_type];
        $path = Yii::getAlias('@app') . '/upload/export/' . $this->fileType . '/' . $this->entityType;
        if (!file_exists($path) && !mkdir($path, 0777, true) && !is_dir($path)) {
            throw new \RuntimeException(sprintf('Directory "%s" was not created', $path));
        }

        $export->status = Export::STATUS_PROCESSING;
        $export->total = 100;
        $export->save();

        foreach (ContactAdditionalField::arrayFields($this->poll->company_id)['additional'] as $field) {
            $this->additionalFields['client' . $field['id']] = $field['text'];
        }
        $allColumns = ArrayHelper::merge(SettingTables::COLUMNS, $this->additionalFields);
        $this->columns = array_filter(explode(',', $export->params['columns']), static function ($value) use ($allColumns) {
            return !in_array($value, ['points', 'comments', 'pointsCollect']) && array_key_exists($value, $allColumns);
        });
        $this->setQuestions();

        ExportHelper::updateProcess($export, 20);

        $file = $path . '/' . $export->id . '.xlsx';
        $array = ArrayHelper::merge([$this->getHeader()], $this->getRows($userCompanyID));

        ExportHelper::updateProcess($export, 40);

        foreach ($array as $rowIndex => $row) {
            foreach ($row as $columnIndex => $column) {
                if (is_string($column) && preg_match('/(^=+)/', $column)) {
                    $array[$rowIndex][$columnIndex] = preg_replace('/(^=+)/', '', $column);
                }
            }
        }

        ExportHelper::updateProcess($export, 60);

        //доходим сюда за 11 секунд на 985 ответах
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->fromArray($array); //тут +32 секунды

        $lastLetter = $sheet->getHighestColumn();
        $lastRow = $sheet->getHighestRow();

        //Выделяем заголовок таблицы жирным
        $sheet->getStyle('A1:' . $lastLetter . '1')->applyFromArray([
            'font' => [
                'size' => 12,
                'bold' => true,
            ]
        ]);

        //Выравниваем столбец с ID по левому краю
        if (get_class($this) === __CLASS__) {
            $sheet->getStyle('B2:B' . $lastRow)->applyFromArray([
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_LEFT,
                ]
            ]);
        }

        ExportHelper::updateProcess($export, 80);

        for ($i = 1; $i <= $lastRow; $i++) {  //этот цикл 48 секунд
            $sheet->getStyle('A' . $i . ':' . $lastLetter . $i)->getAlignment()->setWrapText(true);
        }

        //Выставляем ширину столбцов
        foreach ($sheet->getColumnIterator() as $column) {
            $sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
        }

        $sheet->calculateColumnWidths(); //тут +44 секунды

        foreach ($sheet->getColumnDimensions() as $colDim) {
            if ($colDim->getWidth() > 50) {
                $colDim->setAutoSize(false);
                $colDim->setWidth(50);
            }
        }

        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save($file); //вечность и дальше не идем - отваливается

        $export->processed = $export->total;
        $export->status = Export::STATUS_DONE;
        $export->save();
    }

    /**
     * Устанавливает вопросы для экспорта
     * @return void
     */
    public function setQuestions(): void
    {

        $questions = $this->poll->getFoquzQuestions()
            ->with([
                'questionDetails', 'recipientsQuestionDetails', 'questionFiles', 'differentialRows', 'matrixElements',
                'matrixElements'
            ])
            ->andWhere(['NOT IN', 'main_question_type', [
                FoquzQuestion::TYPE_FORM,
                FoquzQuestion::TYPE_INTERMEDIATE_BLOCK
            ]]);

        if ($this->poll->displaySetting?->type === FoquzPollDisplaySetting::MANUAL_SPLIT) {
            $questions->joinWith(['pollDisplayPageQuestion.displayPage'], false)
                ->orderBy(['foquz_poll_display_pages.order' => SORT_ASC, 'foquz_question.position' => SORT_ASC]);
        }

        $this->questions = $questions->all();

        /** @var FoquzQuestion[] $formQuestions */
        $this->formQuestions = $this->poll->getFoquzQuestions()
            ->with('formFields')
            ->andWhere(['main_question_type' => FoquzQuestion::TYPE_FORM])
            ->all();

        $this->companyFilials = ArrayHelper::index(Filial::find()->where(['company_id' => $this->poll->company_id])->all(), 'id');
    }

    /**
     * Возвращает заголовок выгрузки
     * @return array
     */
    public function getHeader(): array
    {
        $row = [];
        $row[] = 'Наименование анкеты';
        foreach ($this->columns as $value) {
            if (isset(SettingTables::COLUMNS[$value])) {
                $row[] = SettingTables::COLUMNS[$value];
            } elseif (array_key_exists($value, $this->additionalFields)) {
                $row[] = $this->additionalFields[$value];
            }
        }

        foreach ($this->formQuestions as $formQuestion) {
            foreach ($formQuestion->formFields as $formField) {
                $row[] = $formField->name;
            }
        }
        if (!empty($this->questions)) {
            $row[] = 'Служебное название';
            $row[] = 'Тег вопроса';
            $row[] = 'Текст вопроса';
            $row[] = 'Ответ на вопрос';
            $row[] = 'Ответ на комментарий';
        }

        if ($this->poll->point_system) {
            $row[] = 'Набранное кол-во баллов (номинально)';
            $row[] = 'Максимальное кол-во баллов';
            $row[] = 'Набранное кол-во баллов (%)';
        }
        return $row;
    }


    /**
     * Возвращает строки выгрузки
     * @return array
     */
    public function getRows($companyId = null): array
    {
        $export = $this->export;
        if ($this->export->user_id) {
            $serviceSearch = AnswerSearchService::getInstanceByUser($this->export->user_id);
        } else {
            $serviceSearch = AnswerSearchService::getInstanceByCompany($companyId);
        }
        $serviceSearch->applyParams($export->params);
        $data = ArrayHelper::getColumn($serviceSearch->all(), "id");

        /** @var FoquzPollAnswer[] $answers */
        $answers = FoquzPollAnswer::find()
            ->where(['id' => $data])
            ->with([
                'foquzAnswer', 'foquzPoll', 'answerChannel', 'pollLang', 'mailingListSend.mailingListContact.mailingList', 'tags',
                'answerFilial', 'order', 'contact', 'processing', 'tags',
            ])
            ->all();

        $answers = ArrayHelper::index($answers, 'id');
        $sortedAnswers = [];
        foreach ($data as $answer) {
            if (!isset($answers[$answer])) {
                continue;
            }
            $sortedAnswers[] = $answers[$answer];
        }
        $rows = [];
        foreach ($sortedAnswers as $key => $answer) {
            $answerItems = ArrayHelper::index($answer->foquzAnswer, 'foquz_question_id');
            $infoColumns = ArrayHelper::merge(
                $this->getInfoColumns($answer, $answerItems),
                $this->getFormAnswers($answerItems)
            );
            $questionColumns = $this->getQuestionsAnswers($answerItems);
            if (empty($questionColumns)) {
                return [$infoColumns];
            }
            foreach ($questionColumns as $questionColumn) {
                $rows[] = ArrayHelper::merge($infoColumns, $questionColumn);
            }
        }
        return $rows;
    }

    /**
     * Возвращает столбцы с информацией о респонденте
     * @param FoquzPollAnswer $answer
     * @param array $answerItems
     * @return array
     */
    public function getInfoColumns(FoquzPollAnswer $answer, array $answerItems): array
    {
        $columns = [];
        $columns[] = $answer->foquzPoll->name;
        foreach ($this->columns as $column) {
            $columns[] = $answer->getAnswerValue($column);
        }

        return $columns;
    }

    /**
     * Возвращает столбцы с ответами на Анкету
     * @param array $answerItems
     * @return array
     */
    public function getFormAnswers(array $answerItems): array
    {
        $columns = [];
        foreach ($this->formQuestions as $formQuestion) {
            $answer = json_decode($answerItems[$formQuestion->id]->answer ?? '', true) ?? [];
            foreach ($formQuestion->formFields as $formField) {
                $value = $answer[$formField->id] ?? null;
                if ($value && $formField->mask_type !== FoquzQuestion::MASK_NAME) {
                    $columns[] = $answer[$formField->id] ?? '';
                } elseif ($value && $formField->mask_type === FoquzQuestion::MASK_NAME) {
                    $columns[] = ($value['surname'] ?? '') . ' ' . ($value['name'] ?? '') . ' ' . ($value['patronymic'] ?? '');
                } else {
                    $columns[] = '';
                }
            }
        }

        return $columns;
    }

    /**
     * Возвращает столбцы с ответами на вопросы и комментариями
     * @param array $answerItems
     * @return array
     */
    public function getQuestionsAnswers(array $answerItems): array
    {
        $columns = [];
        $maxRowsCount = 0;
        foreach ($this->questions as $question) {
            /** @var FoquzPollAnswerItem $answerItem */
            $answerItem = $answerItems[$question->id] ?? null;
            $questionInfoColumns = [];
            $questionAnswerColumns = [];
            $emptyAnswer = [$question->description, ''];
            $questionInfoColumns[] = $question->service_name;
            if ($question->dictionary_element_id) {
                $questionInfoColumns[] = $question->dictionaryElement->title;
            } else {
                $questionInfoColumns[] = '';
            }

            if ($answerItem) {
                switch ($question->main_question_type) {
                    case FoquzQuestion::TYPE_VARIANTS:
                    case FoquzQuestion::TYPE_PRIORITY:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getVariantAnswer($question, $answerItem, $answerItems);
                        break;
                    case FoquzQuestion::TYPE_TEXT_ANSWER:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getTextAnswer($question, $answerItem);
                        break;
                    case FoquzQuestion::TYPE_DATE:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getDateAnswer($question, $answerItem);
                        break;
                    case FoquzQuestion::TYPE_ADDRESS:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getAddressAnswer($question, $answerItem);
                        break;
                    case FoquzQuestion::TYPE_FILE_UPLOAD:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getFileUploadAnswer($question, $answerItem);
                        break;
                    case FoquzQuestion::TYPE_VARIANT_STAR:
                        $rowsCount = 0;
                        $questionAnswerColumns = $this->getVariantStarAnswer($question, $answerItem, $answerItems, $rowsCount);
                        break;
                    case FoquzQuestion::TYPE_CHOOSE_MEDIA:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getChooseMediaAnswer($question, $answerItem);
                        break;
                    case FoquzQuestion::TYPE_GALLERY_RATING:
                        $rowsCount = 0;
                        $questionAnswerColumns = $this->getGalleryRatingAnswer($question, $answerItem, $rowsCount);
                        break;
                    case FoquzQuestion::TYPE_SMILE_RATING:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getSmileRatingAnswer($question, $answerItem);
                        break;
                    case FoquzQuestion::TYPE_STAR_RATING:
                    case FoquzQuestion::TYPE_RATING:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getRatingAnswer($question, $answerItem);
                        break;
                    case FoquzQuestion::TYPE_NPS_RATING:
                    case FoquzQuestion::TYPE_SCALE:
                        $rowsCount = 0;
                        $questionAnswerColumns = $this->getNpsAnswer($question, $answerItem, $answerItems, $rowsCount);
                        break;
                    case FoquzQuestion::TYPE_DISTRIBUTION_SCALE:
                        $rowsCount = 0;
                        $questionAnswerColumns = $this->getDistributionScaleAnswer($question, $answerItem, $answerItems, $rowsCount);
                        break;
                    case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                        $rowsCount = 0;
                        $matrixPoints = [];
                        $questionAnswerColumns = $this->getSimpleMatrixAnswer($question, $answerItem, $answerItems, $matrixPoints, $rowsCount);
                        break;
                    case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                        $rowsCount = 0;
                        $questionAnswerColumns = $this->getSemDiffAnswer($question, $answerItem, $rowsCount);
                        break;
                    case FoquzQuestion::TYPE_FILIAL:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getFilialAnswer($question, $answerItem);
                        break;
                    case FoquzQuestion::TYPE_DICTIONARY:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getDictionaryAnswer($question, $answerItem);
                        break;
                    case FoquzQuestion::TYPE_3D_MATRIX:
                        $rowsCount = 0;
                        $questionAnswerColumns = $this->get3DMatrixAnswer($question, $answerItem, $answerItems, $rowsCount);
                        break;
                    case FoquzQuestion::TYPE_CARD_SORTING_CLOSED:
                        $rowsCount = 1;
                        $questionAnswerColumns = $this->getCardSortingClosed($question, $answerItem);
                        break;
                    case FoquzQuestion::TYPE_FIRST_CLICK:
                        $rowsCount = 1;
                        $questionAnswerColumns = (new FirstClick())->getAnswerSheetRowsExport3($question, $answerItem);
                        break;
                    default:
                        $rowsCount = 1;
                        $questionAnswerColumns[] = $emptyAnswer;
                }
            } else {
                $rowsCount = 1;
                $questionAnswerColumns[] = $emptyAnswer;
            }

            if ($question->main_question_type !== FoquzQuestion::TYPE_SMILE_RATING) {
                if (($question->comment_enabled ||
                        ($question->is_self_answer && $question->main_question_type === FoquzQuestion::TYPE_FILE_UPLOAD)) && !$question->haveClarifyingQuestion) {
                    $commentField = $question->commentField;
                    $questionAnswerColumns[0][2] = $answerItem->$commentField ?? '';
                    for ($i = 1; $i < $rowsCount; $i++) {
                        $questionAnswerColumns[$i][2] = '';
                    }
                } else {
                    for ($i = 0; $i < $rowsCount; $i++) {
                        if (!isset($questionAnswerColumns[$i][2])) {
                            $questionAnswerColumns[$i][2] = '';
                        }
                    }
                }
            }


            if ($rowsCount > $maxRowsCount) {
                $maxRowsCount = $rowsCount;
            }

            $pointsColumns = [];
            if (
                $this->poll->point_system &&
                $answerItem &&
                in_array($question->main_question_type, FoquzQuestion::TYPES_POINTS)
            ) {
                $points = $answerItem->points;
                $max_points = (string)$answerItem->max_points;
                $percent = $answerItem->max_points ? round($answerItem->points / $answerItem->max_points * 100) : 0;
                $points = (string)$points;
                $percent = (string)$percent;
                $pointsColumns = [$points, $max_points, $percent];
            } elseif ($this->poll->point_system) {
                $pointsColumns = ['', '', ''];
            }

            foreach ($questionAnswerColumns as $key => $questionAnswerColumn) {
                if (!empty($pointsColumns)) {
                    if ($question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
                        $pointsColumns = $matrixPoints[$key] ?? ['', '', ''];
                    }
                    $columns[] = ArrayHelper::merge($questionInfoColumns, $questionAnswerColumn, $pointsColumns);
                } else {
                    $columns[] = ArrayHelper::merge($questionInfoColumns, $questionAnswerColumn);
                }
            }
        }

        return $columns;
    }

    /**
     * Возвращает ответ на вопрос типов Варианты ответа и приоритет
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param array $answerItems
     * @return array[]
     */
    public function getVariantAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, array $answerItems): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить']];
        }
        if (is_array($answerItem->detail_item)) {
            $answerArray = $answerItem->detail_item;
        } else {
            $answerArray = json_decode($answerItem->detail_item ?? '', true) ?? [];
        }
        if (!$question->donor) {
            $variants = ArrayHelper::map($question->questionDetails, 'id', 'question');
        } elseif ($question->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
            $variants = ArrayHelper::map(DictionaryElement::findAll(array_values($answerArray)), 'id', 'fullPath');
        } else {
            $variants = ArrayHelper::map($question->recipientsQuestionDetails, 'id', 'question');
        }
        $selectedVariants = [];
        foreach ($answerArray as $item) {
            $variantName = $variants[$item] ?? '';
            if ($item === '-1') {
                /** @var FoquzPollAnswerItem $donorAnswerItem */
                $donorAnswerItem = $answerItems[$question->donor] ?? null;
                $variantName = $donorAnswerItem->self_variant ?? '';
            }
            $selectedVariants[] = $variantName;
        }
        if ($answerItem->self_variant !== null && $answerItem->self_variant !== '') {
            $selectedVariants[] = $answerItem->self_variant;
        }
        return [[$question->description, implode(';', $selectedVariants)]];
    }

    /**
     * Возвращает ответ на вопрос типа Текстовый ответ
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @return array[]
     */
    private function getTextAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить']];
        }
        if ($question->mask !== FoquzQuestion::MASK_NAME) {
            $answer = $answerItem->answer;
        } else {
            $value = json_decode($answerItem->answer ?? '', true) ?? [];
            $answer = ($value['surname'] ?? '') . ' ' . ($value['name'] ?? '') . ' ' . ($value['patronymic'] ?? '');
        }
        return [[$question->description, $answer]];
    }

    /**
     * Возвращает ответ на вопрос типа Дата/время
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @return array[]
     */
    private function getDateAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem): array
    {
        $answer = ['date' => '', 'time' => ''];
        if (!empty($answerItem->answer)) {
            $answer = json_decode($answerItem->answer, true) ?? [];
        }
        return [[$question->description, (!empty($answer['date']) ? $answer['date'] . ' ' : '') . $answer['time'] ?: '']];
    }

    /**
     * Возвращает ответ на вопрос типа Адрес
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @return array[]
     */
    private function getAddressAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem): array
    {
        return [[$question->description, $answerItem->answer]];
    }

    /**
     * Возвращает ответ на вопрос типа Загрузка файла
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @return array[]
     */
    private function getFileUploadAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem): array
    {
        $links = [];
        foreach ($answerItem->answerItemFiles as $file) {
            $links[] = $file->url;
        }
        $links = implode("\n", $links);
        return [[$question->description, $links]];
    }

    /**
     * Возвращает ответ на вопрос типа Выбор изображения/видео
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @return array[]
     */
    private function getChooseMediaAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить']];
        }
        $elements = [];
        $answer = json_decode($answerItem->answer ?? '', true) ?? [];
        $files = $question->questionFiles;
        ArrayHelper::multisort($files, 'position');
        $files = ArrayHelper::index($files, 'id');
        foreach ($answer as $value) {
            $elements[] = 'Элемент ' . (array_search($value, array_keys($files))  + 1);
        }
        return [[$question->description, implode(';', $elements)]];
    }

    /**
     * Возвращает ответ на вопрос типа Рейтинг фото/видео галереи
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param $rowsCount
     * @return array|array[]
     */
    private function getGalleryRatingAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, &$rowsCount): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Не готов(а) оценить']];
        }
        $rowsCount = count($question->questionFiles);
        $result = [];
        $files = $question->questionFiles;
        ArrayHelper::multisort($files, 'position');
        $answer = json_decode($answerItem->answer ?? '', true) ?? [];
        foreach ($question->questionFiles as $key => $file) {
            $result[] = [$question->description . ': Элемент ' . ($key + 1), $answer[$file->id] ?? ''];
        }
        return $result;
    }

    /**
     * Возвращает ответ на вопрос типа Рейтинг, Звездный рейтинг, Смайл-рейтинг
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @return array[]
     */
    private function getRatingAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Не готов(а) оценить']];
        }
        if ($question->haveClarifyingQuestion) {
            $answers = [];
            $variants = ArrayHelper::map($question->questionDetails, 'id', 'question');
            $detailsItems = json_decode($answerItem->detail_item ?? '', true) ?? [];
            foreach ($detailsItems as $detailsItem) {
                $answers[] = $variants[$detailsItem] ?? '';
            }
            if ($answerItem->self_variant !== null && $answerItem->self_variant !== '') {
                $answers[] = $answerItem->self_variant;
            }
            if (!empty($answerItem->answer)) {
                $answers[] = $answerItem->answer;
            }
            return [[$question->description, $answerItem->rating ?: '', implode(';', $answers)]];
        }
        return [[$question->description, $answerItem->rating ?: '']];
    }

    private function getSmileRatingAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Не готов(а) оценить']];
        }
        if ($question->detail_question) {
            $answers = [];
            $variants = ArrayHelper::map($question->questionDetails, 'id', 'question');
            $positions = ArrayHelper::map($question->questionDetails, 'id', 'position');
            if ($answerItem && is_array($answerItem->detail_item)) {
                foreach ($answerItem->detail_item as $key => $detail_item) {
                    if (is_array($detail_item) && !empty($detail_item['self_variant'])) {
                        $answers[] = $detail_item['self_variant'];
                    } elseif ($key === 'self_variant') {
                        $answers[] = $detail_item;
                    } elseif ($key === 'text_answer') {
                        $answers[] = $detail_item;
                    } else {
                        if (!empty($variants[$detail_item])) {
                            $answers[] = $variants[$detail_item];
                        }
                    }
                }
            }
            return [[$question->description, $answerItem->rating ?: '', implode(';', $answers)]];
        }
        return [[$question->description, $answerItem->rating ?: '']];
    }

    /**
     * Возвращает ответ на вопрос типа NPS рейтинг и шкала
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param array $answerItems
     * @param $rowsCount
     * @return array|array[]
     */
    public function getNpsAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, array $answerItems, &$rowsCount): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Не готов(а) оценить']];
        }
        if (!$question->set_variants) {
            $rowsCount = 1;
            // Странная конструкция, но иначе будет пустая ячейка
            if ($answerItem->rating === 0) {
                $answerItem->rating = '0';
            }
            return [[$question->description, $answerItem->rating !== -1 ? $answerItem->rating : '']];
        }
        $answer = json_decode($answerItem->answer ?? '', true) ?? [];
        $result = [];
        $details = [];
        if (!$question->donor) {
            $details = $question->questionDetails;
            ArrayHelper::multisort($details, 'position');
        } else {
            if ($question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                $details = $question->recipientsQuestionDetails;
            } else {
                $dictionaryElements = DictionaryElement::findAll(array_keys($answer));
                foreach ($dictionaryElements as $dictionaryElement) {
                    $detail = new FoquzQuestionDetail();
                    $detail->id = $dictionaryElement->id;
                    $detail->question = $dictionaryElement->fullPath;
                    $details[] = $detail;
                }
            }
            ArrayHelper::multisort($details, 'position');
            $donorAnswerItem = $answerItems[$question->donor] ?? null;
            if (isset($answer['-1'])) {
                $detail = new FoquzQuestionDetail();
                $detail->id = -1;
                $detail->question = $donorAnswerItem->self_variant;
                $details[] = $detail;
            }
        }
        $i = 0;
        foreach ($details as $detail) {
            if ($detail->extra_question) {
                continue;
            }
            $value = '';
            if (isset($answer[$detail->id]) && $answer[$detail->id] !== '-1' && $answer[$detail->id] !== 'null') {
                $value = $answer[$detail->id];
            } elseif (isset($answer[$detail->id]) && $answer[$detail->id] === 'null') {
                $value = $question->skip_text ?: 'Не готов(а) оценить';
            }
            if (!empty($value)) {
                $result[] = [$question->description . ': '. $detail->question, $value];
                $i++;
            }
        }
        $rowsCount = $i;
        if ($question->haveClarifyingQuestion && array_key_exists(0, $result)) {
            $extraVariants = ArrayHelper::index($question->questionDetails, null, 'extra_question')[1] ?? [];
            $extraVariants = ArrayHelper::map($extraVariants, 'id', 'question');
            $detailsItems = $answerItem->detail_item ?? [];
            if (!is_array($detailsItems)) {
                $detailsItems = [];
            }
            switch ($question->extra_question_type) {
                case FoquzQuestion::EXTRA_QUESTION_COMMON:
                    $answers = [];
                    if ($question->variants_element_type !== FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT) {
                        foreach ($detailsItems as $detailsItem) {
                            $answers[] = $extraVariants[$detailsItem] ?? '';
                        }
                        if ($answerItem->self_variant !== null && $answerItem->self_variant !== '') {
                            $answers[] = $answerItem->self_variant;
                        }
                    } elseif ($answerItem->self_variant !== null && $answerItem->self_variant !== '') {
                        $answers[] = $answerItem->is_self_variant ? $answerItem->self_variant : '';
                    }
                    $answers = array_filter($answers, static function ($value) {
                        return $value !== '';
                    });
                    $result[0][2] = implode(';', $answers);
                    break;
                case FoquzQuestion::EXTRA_QUESTION_COMMON_FOR_EACH:
                    $i = 0;
                    foreach ($details as $detail) {
                        if ($detail->extra_question || !isset($detailsItems[$detail->id])) {
                            continue;
                        }
                        $answers = [];
                        if ($question->variants_element_type !== FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT) {
                            foreach ($detailsItems[$detail->id] as $detailKey => $detailsItem) {
                                if (in_array($detailKey, ['self_variant', 'answer'])) {
                                    continue;
                                }
                                $answers[] = $extraVariants[$detailsItem] ?? '';
                            }
                            if (isset($detailsItems[$detail->id]['self_variant'])) {
                                $answers[] = $detailsItems[$detail->id]['self_variant'];
                            }
                        } elseif (isset($detailsItems[$detail->id]['answer'])) {
                            $answers[] = $detailsItems[$detail->id]['answer'];
                        }
                        $answers = array_filter($answers, static function ($value) {
                            return $value !== '';
                        });
                        $result[$i][2] = implode(';', $answers);
                        $i++;
                    }
                    break;
            }
        }

        if ($question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_DIFFERENT_EACH) {
            $detailsItems = $answerItem->detail_item ?? [];
            if (!is_array($detailsItems)) {
                $detailsItems = [];
            }
            $detailsMap = ArrayHelper::map($details, 'id', 'question');
            $i = 0;
            foreach ($details as $detail) {
                if ($detail->extra_question) {
                    continue;
                }
                $answers = [];
                if (isset($detailsItems[$detail->id])) {
                    foreach ($detailsItems[$detail->id] as $diKey => $diValue) {
                        $answers[] = ($diKey === 'self_variant' || $diKey === 'answer') ?
                            $diValue :
                            $detailsMap[$diValue] ?? '';
                    }
                }
                $result[$i++][2] = implode(';', $answers);
            }
        }
        return $result;
    }

    /**
     * Возвращает ответ на вопрос типа Распределительная шкала
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param array $answerItems
     * @param $rowsCount
     * @return array|array[]
 */
    public function getDistributionScaleAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, array $answerItems, &$rowsCount): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить']];
        }
        $answer = json_decode($answerItem->answer ?? '', true) ?? [];
        $result = [];
        $details = $question->questionDetails;
        ArrayHelper::multisort($details, 'position');
        $i = 0;
        foreach ($details as $detail) {
            $value = '';
            if (isset($answer[$detail->id]) && $answer[$detail->id] !== '-1' && $answer[$detail->id] !== 'null') {
                $value = $answer[$detail->id];
            }
            $result[] = [$question->description . ': '. $detail->question, $value];
            $i++;
        }
        $rowsCount = $i;
        if ($answerItem->self_variant) {
            $result[0][2] = $answerItem->self_variant;
        }
        return $result;
    }

    /**
     * Возвращает ответ на вопрос типа Семантический дифференциал
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param $rowsCount
     * @return array|array[]
     */
    public function getSemDiffAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, &$rowsCount): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Не готов(а) оценить']];
        }
        $answer = json_decode($answerItem->answer ?? '', true) ?? [];
        if (count($question->differentialRows) === 1) {
            $rowsCount = 1;
            $value = $answer[$question->differentialRows[0]->id] ?? '';
            return [[$question->description, $value]];
        }
        $rowsCount = count($question->differentialRows);
        $result = [];
        foreach ($question->differentialRows as $key => $row) {
            $value = $answer[$row->id] ?? '';
            $result[] = [$question->description . ': Строка ' . ($key + 1), $value];
        }
        return $result;
    }

    /**
     * Возвращает ответ на вопрос типа Выбор филиала
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @return array[]
     */
    public function getFilialAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить']];
        }
        $filialID = !empty($answerItem->detail_item[0]) ? $answerItem->detail_item[0] : null;
        $filial = $this->companyFilials[$filialID] ?? null;
        return [[$question->description, $filial->name ?? '']];
    }

    /**
     * Возвращает ответ на вопрос типа Классификатор
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param string $separator
     * @return array[]
     */
    public function getDictionaryAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, string $separator = ';'): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить']];
        }
        $selectedIDs = $answerItem->detail_item ?? [];
        if (isset($this->questionDictionary[$question->dictionary_id])) {
            $dictionary = $this->questionDictionary[$question->dictionary_id];
        } else {
            /** @var DictionaryElement[] $dictionary */
            $dictionary = ArrayHelper::index(DictionaryElement::find()->where(['dictionary_id' => $question->dictionary_id])->all(), 'id');
            $this->questionDictionary[$question->dictionary_id] = $dictionary;
        }
        $result = [];
        foreach ($selectedIDs as $selectedID) {
            $result[] = $dictionary[$selectedID]->title ?? '';
        }
        return [[$question->description, implode($separator, $result)]];
    }

    /**
     * Возвращает ответ на вопрос типа 3D матрица
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param array $answerItems
     * @param $rowsCount
     * @return array|array[]
     */
    public function get3DMatrixAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, array $answerItems, &$rowsCount): array
    {
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить']];
        }
        $result = [];
        $answer = json_decode($answerItem->answer ?? '', true) ?? [];
        $matrixElements = ArrayHelper::index($question->matrixElements, null, 'type_id');
        /** @var FoquzQuestionMatrixElement[] $columns */
        $columns = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN]) ?
            ArrayHelper::index($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN], 'id') : [];
        /** @var FoquzQuestionMatrixElement[] $rows */
        $rows = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW]) ?
            ArrayHelper::index($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW], 'id') : [];
        if (!empty($this->matrixVariants[$question->id])) {
            $matrixVariants = $this->matrixVariants[$question->id];
        } else {
            $matrixVariants = FoquzQuestionMatrixElementVariant::find()
                ->where(['matrix_element_id' => ArrayHelper::getColumn($columns, 'id')])
                ->all();
            $this->matrixVariants[$question->id] = ArrayHelper::index($matrixVariants, 'id');
        }
        /** @var FoquzQuestionMatrixElementVariant[] $matrixVariants */
        $matrixVariants = ArrayHelper::index($matrixVariants, 'id');
        foreach ($rows as $row) {
            foreach ($columns as $column) {
                if (!isset($answer[$row->id][$column->id])) {
                    continue;
                }
                $rowsCount++;
                $value = [];
                if (is_array($answer[$row->id][$column->id])) {
                    foreach ($answer[$row->id][$column->id] as $variantID) {
                        if ($variantID === '-1') {
                            $value[] = $question->skip_text ?: 'Затрудняюсь ответить';
                        } else {
                            $value[] = $matrixVariants[$variantID]->name ?? '';
                        }
                    }
                } elseif ($answer[$row->id][$column->id] == null) {
                    $value[] = $question->skip_text ?: 'Затрудняюсь ответить';
                }
                $result[] = [$question->description . ': ' . $row->name . ' / ' . $column->name, implode(';', $value)];
            }
        }
        if ($rowsCount === 0) {
            $rowsCount = 1;
            return [[$question->description, '']];
        }
        return $result;
    }

    /**
     * Возвращает ответ на вопрос типа Простая матрица
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param array $answerItems
     * @param $points
     * @param $rowsCount
     * @return array|array[]
     */
    public function getSimpleMatrixAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, array $answerItems, &$points, &$rowsCount): array
    {
        if ($answerItem->skipped && !$question->detail_question) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить']];
        }
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Затрудняюсь ответить', '']];
        }
        $result = [];
        $answer = json_decode($answerItem->answer ?? '', true) ?? [];
        $detail = $answerItem->detail_item ?? [];
        $settings = json_decode($question->matrix_settings ?? '', true) ?? [];

        if ($question->donor) {
            /** @var FoquzPollAnswerItem $donorAnswerItem */
            $donorAnswerItem = $answerItems[$question->donor] ?? null;
            if (isset($answer['-1'])) {
                $settings['rows'][] = $donorAnswerItem->self_variant;
                $settings['donorRows'][] = -1;
                // непонятно
                /*if (in_array($this->questions[$question->donor]->self_variant_text ?: 'Свой вариант', $settings['extra_question']['rows'])) {
                    $settings['extra_question']['rows'][] = $donorAnswerItem->self_variant;
                }*/
                if (isset($detail[-1])) {
                    $detail[$donorAnswerItem->self_variant] = $detail[-1];
                    unset($detail[-1]);
                }
            }
        }
        foreach ($settings['rows'] as $key => $row) {
            $rowPoints = 0;
            if ($question->donor) {
                $donorRowId = $settings['donorRows'][$key] ?? null;
                $answerValue = $answer[$donorRowId] ?? null;
            } else {
                $answerValue = $answer[$row] ?? null;
            }
            if ($answerValue === null) {
                continue;
            }
            if ($answerValue !== 'null') {
                $columns = [];
                foreach ($settings['cols'] as $colKey => $column) {
                    if (in_array($column, $answerValue, true)) {
                        $columns[] = $column;
                        if ($this->poll->point_system) {
                            $rowPoints += ($settings['points'][array_search($row, $settings['rows'])][array_search($column, $settings['cols'])] ?? 0);
                        }
                    }
                }
                $rowsCount++;
                if (!$question->detail_question) {
                    $result[] = [$question->description . ': ' . $row, implode(';', $columns)];
                } else {
                    if ($question->variants_element_type  === 2) {
                        $detailAnswer = $detail[$row]['answer'] ?? '';
                    } else {
                        $detailAnswer = [];
                        $variants = ArrayHelper::map($question->questionDetails, 'id', 'question');
                        foreach ($detail[$row] ?? [] as $detailKey => $item) {
                            if ($detailKey === 'answer' || $detailKey === 'self_variant') {
                                continue;
                            }
                            $detailAnswer[] = $variants[$item] ?? '';
                        }
                        if (!empty($detail[$row]['self_variant'])) {
                            $detailAnswer[] = $detail[$row]['self_variant'];
                        }
                        $detailAnswer = implode(';', $detailAnswer);
                    }
                    if (empty($detailAnswer) && isset($detail['Свой вариант'])) {
                        $detailAnswer = $variants[$detail['Свой вариант'][0]] ?? '';
                    }
                    $result[] = [$question->description . ': ' . $row, implode(';', $columns), $detailAnswer];
                }
            } elseif ($question->skip_variant) {
                $rowsCount++;
                if (!$question->detail_question) {
                    $result[] = [$question->description . ': ' . $row, $question->skip_text ?: 'Затрудняюсь ответить'];
                } else {
                    $result[] = [$question->description . ': ' . $row, $question->skip_text ?: 'Затрудняюсь ответить', ''];
                }
            }
            if ($this->poll->point_system) {
                if (isset($settings['points'][array_search($row, $settings['rows'])]) && is_array($settings['points'][array_search($row, $settings['rows'])])) {
                    if (!empty($settings['multiple_choice'])) {
                        $rowMaxPoints = array_sum($settings['points'][array_search($row, $settings['rows'])]);
                    } else {
                        $rowMaxPoints = max($settings['points'][array_search($row, $settings['rows'])]);
                    }
                } else {
                    $rowMaxPoints = 0;
                }
                $percentPoints = $rowMaxPoints ? round($rowPoints / $rowMaxPoints * 100) : 0;
                $points[] = [
                    (string)$rowPoints,
                    (string)$rowMaxPoints,
                    (string)$percentPoints
                ];
            }
        }
        if ($rowsCount === 0) {
            $rowsCount = 1;
            $points[] = ['', '', ''];
            return [[$question->description, '']];
        }
        return $result;
    }

    /**
     * Возвращает ответ на вопрос типа Звездный рейтинг для вариантов
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @param array $answerItems
     * @param $rowsCount
     * @return array[]
     */
    public function getVariantStarAnswer(FoquzQuestion $question, FoquzPollAnswerItem $answerItem, array $answerItems, &$rowsCount): array
    {
        if ($answerItem->skipped && !$question->detail_question) {
            return [[$question->description, $question->skip_text ?: 'Не готов(а) оценить']];
        }
        if ($answerItem->skipped) {
            return [[$question->description, $question->skip_text ?: 'Не готов(а) оценить', '']];
        }
        $result = [];
        $answer = json_decode($answerItem->answer ?? '', true) ?? [];
        if (!$question->donor) {
            $details = $question->questionDetails;
        } elseif ($question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
            $details = $question->recipientsQuestionDetails;
            ArrayHelper::multisort($details, 'position');
            $donorAnswerItem = $answerItems[$question->donor] ?? null;
            if (isset($answer['-1'])) {
                $detail = new FoquzQuestionDetail();
                $detail->id = -1;
                $detail->question = $donorAnswerItem->self_variant;
                $details[] = $detail;
            }
        } else {
            $details = [];
            $recipientsDetails = $question->recipientQuestionDetails;
            foreach ($recipientsDetails as $recipientsDetail) {
                $detail = new FoquzQuestionDetail();
                $detail->id = $recipientsDetail->dictionary_element_id;
                $detail->question = $recipientsDetail->dictionaryElement->fullPath;
                $details[] = $detail;
            }
        }
        foreach ($details as $detail) {
            if ($detail->extra_question || !isset($answer[$detail->id])) {
                continue;
            }
            if ($answer[$detail->id] !== 'null') {
                $rowsCount++;
                if (!$question->detail_question) {
                    if ($answer[$detail->id] === '-1') {
                        continue;
                    }
                    $result[] = [$question->description . ': '.  ($detail->question ?? ""), $answer[$detail->id] ?: ''];
                } else {
                    if ($question->variants_element_type  === 2) {
                        $detailAnswer = $answer['extra'][$detail->id]['answer'] ?? '';
                    } else {
                        $detailAnswer = [];
                        $variants = ArrayHelper::map($question->questionDetails, 'id', 'question');
                        foreach ($answer['extra'][$detail->id]?? [] as $detailKey => $item) {
                            if ($detailKey === 'answer' || $detailKey === 'self_variant') {
                                continue;
                            }
                            $detailAnswer[] = $variants[$item] ?? '';
                        }
                        if (!empty($answer['extra'][$detail->id]['self_variant'])) {
                            $detailAnswer[] = $answer['extra'][$detail->id]['self_variant'];
                        }
                        $detailAnswer = implode(';', $detailAnswer);
                    }
                    $result[] = [$question->description . ': '. $detail->question, $answer[$detail->id] ?: '', $detailAnswer];
                }
            } elseif ($question->skip_variant) {
                $rowsCount++;
                if (!$question->detail_question) {
                    $result[] = [$question->description . ': '. $detail->question, $question->skip_text ?: 'Не готов(а) оценить'];
                } else {
                    $result[] = [$question->description . ': '. $detail->question, $question->skip_text ?: 'Не готов(а) оценить', ''];
                }
            }
        }
        if ($rowsCount === 0) {
            $rowsCount = 1;
            return [[$question->description, '']];
        }
        return $result;
    }


    /**
     * Возвращает ответ на вопрос типа Закрытая карточная сортировка
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @return array[]
     */
    public function getCardSortingClosed(FoquzQuestion $question, FoquzPollAnswerItem $answerItem): array
    {
        return (new CardSortingClosed())->getAnswerSheetRowsExport3($question, $answerItem);
    }
}
