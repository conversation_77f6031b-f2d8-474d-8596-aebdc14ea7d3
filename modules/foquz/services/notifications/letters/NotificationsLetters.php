<?php

namespace app\modules\foquz\services\notifications\letters;

use app\models\company\Company;
use app\modules\foquz\models\CompanyFeedback;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\v2\answers\Answer;
use app\modules\foquz\services\notifications\enums\NotificationType;
use app\modules\foquz\services\notifications\NotificationsAnswerProcessingService;
use Yii;

class NotificationsLetters
{
    private static function createBodyLetter(string $header, string $body, string $url, string $clientId = ''): string
    {
        $text = Yii::$app->mailer->render('site_notification_template', ['clientId' => $clientId]);
        $text = str_replace('{header}', $header, $text);
        $text = str_replace('{text}', $body, $text);
        return str_replace( '{url}', $url, $text);
    }

    public static function getUrlAnswerProcessing(string $role, FoquzPollAnswer $answer): string
    {
        if ($role == NotificationsAnswerProcessingService::ROLE_MODERATOR) {
            return self::getUrlAnswer($answer);
        }
        $url = Yii::$app->params['protocol'] . '://' . $answer->foquzPoll->company->alias . '/foquz';
        $url .= '/foquz-poll/answer?id=' . $answer->foquz_poll_id;
        return $url;
    }

    public static function letterAnswerProcessingNew(string $role, FoquzPollAnswer $answer): array
    {
        $url = self::getUrlAnswerProcessing($role, $answer);
        $subject = 'Новая анкета для обработки';
        $text = 'Вам добавлена новая анкета для обработки в опросе <b>' . $answer->foquzPoll->name . '</b>.';
        return [
            'subject' => $subject,
            'message' => self::createBodyLetter($subject, $text, $url)
        ];
    }

    public static function letterAnswerProcessingChangeStatus(string $role, FoquzPollAnswer $answer): array
    {
        $url = self::getUrlAnswerProcessing($role, $answer);
        $subject = 'Изменился статус обработки анкеты';
        $text = 'Изменился статус обработки анкеты <b>' . $answer->foquzPoll->name . '</b>.';
        return [
            'subject' => $subject,
            'message' => self::createBodyLetter($subject, $text, $url)
        ];
    }

    public static function letterFeedbackNew(CompanyFeedback $feedback, bool $withAnswers): array
    {
        return [
            'subject' => 'Новое сообщение в форме обратной связи',
            'message' => Yii::$app->view->render('@app/mail/new-feedback-answers.twig', [
                'feedback'    => $feedback,
                'withAnswers' => $withAnswers
            ])
        ];
    }

    public static function getUrlAnswer($answer): string
    {
        return Yii::$app->params['protocol'] . '://' . $answer->foquzPoll->company->alias . '/foquz/foquz-poll/answer?id=' . $answer->foquz_poll_id . "&reviewId=" . $answer->id;
    }

    public static function letterAnswerNew(FoquzPollAnswer $answer): array
    {
        return [
            'subject' => 'Новый ответ в опросе',
            'message' => self::createBodyLetter(
                'Новый ответ в опросе',
                'В опросе <b>' . $answer->foquzPoll->name . '</b> появился новый ответ.',
                self::getUrlAnswer($answer),
                $answer->contact?->company_client_id ?? ''
            ),
        ];
    }

    public static function letterAnswerLessAssessment(FoquzPollAnswer $answer, ?int $percent): array
    {
        return [
            'subject' => 'Получен ответ с низкой оценкой',
            'message' => self::createBodyLetter(
                'Получен ответ с низкой оценкой',
                'В опросе <b>' . $answer->foquzPoll->name . '</b> есть ответ с оценкой меньше ' . ($percent ?? '') . '% от максимальной.',
                self::getUrlAnswer($answer)
            ),
        ];
    }

    public static function letterComplaintNew(FoquzPollAnswer $answer): array
    {
        return [
            'subject' => 'Ответ с жалобой',
            'message' => self::createBodyLetter(
                'Ответ с жалобой',
                'В опросе <b>' . $answer->foquzPoll->name . '</b> появился новый ответ с жалобой.',
                self::getUrlAnswer($answer)
            ),
        ];
    }

    public static function letterAnswerLessAssessmentExtended(FoquzPollAnswer $answer, ?int $percent): array
    {
        return [
            'subject' => 'Получен ответ с низкой оценкой',
            'message' => str_replace('{percent}', ((string)$percent ?? ''),
                Yii::$app->view->render('@app/mail/new-answers-extended.twig', [
                    'answer' => $answer,
                    'type'   => NotificationType::ANSWER_ASSESSMENT_LESS->value,
                ])),
        ];
    }

    public static function letterAnswerNewExtended(FoquzPollAnswer $answer): array
    {
        return [
            'subject' => 'Новый ответ в опросе',
            'message' => Yii::$app->view->render('@app/mail/new-answers-extended.twig', [
                'answer' => $answer,
                'type'   => 'new_answers_extended',
            ]),
        ];
    }

    public static function letterDailyReport(array $total, array $data, Company $company): array
    {
        return [
            'subject' => 'Сводный отчёт за сутки',
            'message' => \Yii::$app->mailer->render('daily-mail-report', [
                'total' => $total,
                'data'  => $data,
                'url'   => \Yii::$app->params['protocol'] . '://' . $company->alias
            ])
        ];

    }

}