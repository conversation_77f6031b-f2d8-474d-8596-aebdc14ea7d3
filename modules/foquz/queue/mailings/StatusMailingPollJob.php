<?php

namespace app\modules\foquz\queue\mailings;

use app\models\company\Company;
use app\modules\foquz\models\channels\GlobalEmailSettings;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollMailingList;
use app\modules\foquz\services\custom\HRManagerService;
use Yii;
use yii\base\BaseObject;
use yii\queue\amqp_interop\Queue;
use yii\queue\JobInterface;
use yii\redis\Connection;

/**
 * Запуск рассылки
 */
class StatusMailingPollJob extends BaseObject implements JobInterface
{
    public const LOG_CATEGORY = 'yii\queue\mailing_list\StatusMailingPollJob';

    public int $id;
    public string $uidTask;
    private ?FoquzPollMailingList $list = null;

    /**
     * Кэшируем необходимые данные рассылки в Redis
     * @return array
     */
    private function getDataList(bool $isHRList): array
    {
        $list = $this->list;
        $data = [
            'poll'     => [
                'id'                      => $list->foquzPoll->id,
                'dontSendIfPassed'        => (bool)$list->foquzPoll->dont_send_if_passed,
                'stopSending'             => $list->foquzPoll->stop_sending,
                'dontSendIfPromocodeUsed' => $list->foquzPoll->dont_send_if_promocode_used,
                'limit'                   => (bool)$list->foquzPoll->mailing_limit,
                'frequency'               => $list->foquzPoll->company->mailing_frequency,
                'name'                    => $list->foquzPoll->name,
                'processingTimeInMinutes' => $list->foquzPoll->processing_time_in_minutes,
            ],
            'company'  => [
                'id'        => $list->foquzPoll->company->id,
                'limit'             => (bool)$list->foquzPoll->company->mailing_limit,
                'frequency' => $list->foquzPoll->company->mailing_frequency,
                'alias'     => $list->foquzPoll->company->alias,
                'shortLink' => $list->foquzPoll->company->shortLink,
            ],
            'channels' => [

            ],
            'list'     => [
                'id'          => $list->id,
                'status'      => $list->status,
                'launched_at' => $list->launched_at,
                'name'        => $list->name,
                'isHRList'    => $isHRList
            ],
        ];

        $quotes = $this->list->foquzPoll->quotes;
        foreach ($quotes as $quote) {
            $data['poll']['quotes'][$quote['id']]['key'] = $quote['key'];
            $data['poll']['quotes'][$quote['id']]['short_link'] = $quote->short_link;
        }

        $channels = empty($list->channelsMailing) ? $list->foquzPoll->channels : $list->channelsMailing;

        foreach ($channels as $channel) {
            $row = $channel->attributes;
            $senderSetting = $channel->getGlobalSetting()->one();
            if (empty($senderSetting)) {
                $senderSetting = GlobalEmailSettings::find()->where([
                    "company_id" => $list->foquzPoll->company_id,
                    'sender'     => $channel->sender
                ])->one();
            }
            $row['senderSettings'] = $senderSetting->attributes ?? null;
            $row['repeats'] = [];
            foreach ($channel->repeats as $repeat) {
                $row['repeats'][] = $repeat->attributes;
            }
            $data['channels'][] = $row;
        }
        return $data;
    }

    /**
     * @param string $text
     * @return void
     */
    private function printMessage(string $text): void
    {
        $message = 'list=' . $this->id . ' uid=' . $this->uidTask;
        if ($this->list) {
            $model = $this->list;
            $message .= ' ' . $model->foquzPoll->company->alias . ' poll=' . $model->foquz_poll_id . ' ' . $model->foquzPoll->name . '  listName=' . $model->name . ' ';
        }
        $message .= ' ' . $text;
        Yii::info($message, self::LOG_CATEGORY);
        print($message . PHP_EOL);
    }

    /**
     * @param $queue
     * @return bool
     */
    public function execute($queue): bool
    {
        if (empty($this->id)) {
            return false;
        }

        /** @var FoquzPollMailingList|null $list */
        $list = FoquzPollMailingList::find()
            ->leftJoin(FoquzPoll::tableName(), 'foquz_poll_mailing_list.foquz_poll_id=foquz_poll.id')
            ->leftJoin(Company::tableName(), 'company.id=foquz_poll.company_id')
            ->with([
              /*  "listContacts"    => function ($q) {
                    $q->with("foquzContact")->orderBy("id");
            },*/
                "foquzPoll"       => function ($q) {
                    $q->with([
                        "channels" => function ($q) {
                            $q->with("repeats")->where(["active" => 1])
                                ->orderBy("position")
                                ->andWhere("ifnull(passed_trigger, 0)=0 and mailing_id is null");
                        },
                        "company",
                        "quotes",
                    ]);
                },
                "channelsMailing" => function ($q) {
                    $q->with("repeats")->where(["active" => 1])->orderBy("position")->andWhere("ifnull(passed_trigger, 0)=0");
                },
            ])
            ->where(['foquz_poll_mailing_list.id' => $this->id])
            ->one();

        if (empty($list)) {
            Yii::info('Рассылка не найдена list=' . $this->id, self::LOG_CATEGORY);
            return false;
        }

        if ($list->queue_job_uid !== $this->uidTask) {
            $this->printMessage('Задача отменена ');
            return false;
        }

        $this->list = $list;
        if (!$list->foquzPoll->company->allow_mailing_send) {
            $this->printMessage('Компании не разрешены рассылки ');
            return false;
        }

        $isMailingHR = false;
        if (HRManagerService::isCompanyHR($list->foquzPoll->company_id)) {
            $this->printMessage('Особая рассылка HR');
            $channels = $list->channelsMailing;
            if (count($channels) == 0) {
                $channels = $list->foquzPoll->channels;
            }
            if (HRManagerService::isMailingHR($list, $channels)) {
                $isMailingHR = true;
                $this->printMessage('Особая рассылка HR');
                //return false;
            }
        }

        /** @var Connection $redis */
        /** @noinspection PhpUndefinedFieldInspection */
        //   $redis = Yii::$app->redis_mailing;

        /** @var Queue $queue */
        /** @noinspection PhpUndefinedFieldInspection */
        $queueMailingExternal = Yii::$app->queue_mailings_out;

        if ($list->redis_key) {
            $queueMailingExternal->priority(9)->push([
                'job'     => 'invites\\MailingStopJob',
                'listKey' => $list->redis_key,
            ]);
        }


        if ($list->status == FoquzPollMailingList::STATUS_STOPPED) {
            $this->printMessage('Рассылка остановлена');
            return false;
        }

        if ($list->status == FoquzPollMailingList::STATUS_STOPPED && empty($list->launched_at)) {
            $this->printMessage('Рассылка не запущена');
            return false;
        }

        if ($list->status == FoquzPollMailingList::STATUS_NEW) {
            $list->status = FoquzPollMailingList::STATUS_STARTED;
            $list->save(false);
            $this->printMessage('Запущена рассылка по расписанию ' . $list->launched_at ?? '');
        }

        $list->launched_at = date("y-m-d H:i:s");
        $list->save(false);

        $data = $this->getDataList($isMailingHR);
        $list->redis_key = $list->id . '-' . time();
        $list->save(false);
        $queueMailingExternal->priority(9)->push([
            'job'         => 'invites\\MailingStartJob',
            'listKey'     => $list->redis_key,
            'mailingData' => $data,
            'id'          => $list->id,
            'queueJobUID' => $this->uidTask,
        ]);
        return true;
    }
}