<?php

namespace app\modules\foquz\models\polls\forms;

use app\components\crypt\JsonCrypt;
use app\models\Dictionary;
use app\models\DictionaryElement;
use app\models\Filial;
use app\modules\foquz\models\ContactAdditionalField;
use app\modules\foquz\models\FoquzFile;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollCode;
use app\modules\foquz\models\FoquzPollDesign;
use app\modules\foquz\models\FoquzPollDisplayPage;
use app\modules\foquz\models\FoquzPollDisplayPageQuestion;
use app\modules\foquz\models\FoquzPollDisplaySetting;
use app\modules\foquz\models\FoquzPollLang;
use app\modules\foquz\models\FoquzPollPage;
use app\modules\foquz\models\FoquzPollPageSocialNetworksOptions;
use app\modules\foquz\models\FoquzPollQuestionsLogic;
use app\modules\foquz\models\FoquzPollQuestionViewLogic;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionAddressCodes;
use app\modules\foquz\models\FoquzQuestionCardSortingCategory;
use app\modules\foquz\models\FoquzQuestionCardSortingCategoryLang;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionDetailLang;
use app\modules\foquz\models\FoquzQuestionDifferentialRow;
use app\modules\foquz\models\FoquzQuestionEndScreenLogo;
use app\modules\foquz\models\FoquzQuestionFile;
use app\modules\foquz\models\FoquzQuestionFileLang;
use app\modules\foquz\models\FoquzQuestionFirstClick;
use app\modules\foquz\models\FoquzQuestionFirstClickArea;
use app\modules\foquz\models\FoquzQuestionFirstClickAreaLang;
use app\modules\foquz\models\FoquzQuestionFirstClickLang;
use app\modules\foquz\models\FoquzQuestionFormField;
use app\modules\foquz\models\FoquzQuestionFormFieldLang;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSetting;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSettingLang;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSettingSocNetworks;
use app\modules\foquz\models\FoquzQuestionLang;
use app\modules\foquz\models\FoquzQuestionMatrixElement;
use app\modules\foquz\models\FoquzQuestionMatrixElementLang;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariant;
use app\modules\foquz\models\FoquzQuestionMatrixElementVariantLang;
use app\modules\foquz\models\FoquzQuestionNpsRatingSetting;
use app\modules\foquz\models\FoquzQuestionPrioritySettings;
use app\modules\foquz\models\FoquzQuestionRightAnswer;
use app\modules\foquz\models\FoquzQuestionScaleRatingSetting;
use app\modules\foquz\models\FoquzQuestionSemDifSetting;
use app\modules\foquz\models\FoquzQuestionSmile;
use app\modules\foquz\models\FoquzQuestionStarRatingOptions;
use app\modules\foquz\models\RecipientQuestionDetail;
use app\modules\foquz\models\ScoresInterpretationRange;
use app\modules\foquz\services\api\ExtraQuestionService;
use app\modules\foquz\services\FileService;
use InvalidArgumentException;
use yii\base\Model;
use yii\db\ActiveRecord;
use yii\helpers\Json;

class PollJsonForm extends Model
{
    public const ALLOWED_CLASSES = [
        FoquzFile::class,
        FoquzPoll::class,
        FoquzPollPage::class,
        FoquzPollDisplayPage::class,
        ScoresInterpretationRange::class,
        FoquzPollLang::class,
        FoquzPollDesign::class,
        FoquzPollDisplaySetting::class,
        FoquzPollCode::class,
        FoquzPollQuestionsLogic::class,
        FoquzPollQuestionViewLogic::class,
        FoquzPollPageSocialNetworksOptions::class,
        FoquzQuestion::class,
        FoquzQuestionDetail::class,
        FoquzQuestionDifferentialRow::class,
        FoquzQuestionSmile::class,
        FoquzQuestionFormFieldLang::class,
        FoquzQuestionFormField::class,
        FoquzQuestionAddressCodes::class,
        FoquzQuestionSemDifSetting::class,
        FoquzQuestionNpsRatingSetting::class,
        FoquzQuestionScaleRatingSetting::class,
        FoquzQuestionStarRatingOptions::class,
        FoquzQuestionIntermediateBlockSetting::class,
        FoquzQuestionRightAnswer::class,
        FoquzQuestionPrioritySettings::class,
        FoquzQuestionFileLang::class,
        FoquzQuestionFile::class,
        FoquzPollDisplayPageQuestion::class,
        FoquzQuestionEndScreenLogo::class,
        FoquzQuestionLang::class,
        RecipientQuestionDetail::class,
        FoquzQuestionIntermediateBlockSetting::class,
        FoquzQuestionIntermediateBlockSettingLang::class,
        FoquzQuestionIntermediateBlockSettingSocNetworks::class,
        ContactAdditionalField::class,
        FoquzQuestionDetailLang::class,
        FoquzQuestionMatrixElement::class,
        FoquzQuestionMatrixElementLang::class,
        FoquzQuestionMatrixElementVariant::class,
        FoquzQuestionMatrixElementVariantLang::class,
        FoquzQuestionCardSortingCategory::class,
        FoquzQuestionCardSortingCategoryLang::class,
        FoquzQuestionFirstClick::class,
        FoquzQuestionFirstClickLang::class,
        FoquzQuestionFirstClickArea::class,
        FoquzQuestionFirstClickAreaLang::class,
    ];

    public $file;
    public $companyId;
    private $sourceCompanyId;
    /** @var FoquzPoll */
    private $poll;
    /** @var FoquzQuestion */
    private $question;

    private $questionLinks = [];
    private $recipientLinks = [];
    private $variantLinks = [];
    private $pagesLinks = [];
    private $matrixElementLinks = [];
    private $matrixElementVariantLinks = [];
    private $pollLangs = [];
    private $fileLangs = [];
    private $formLangs = [];
    private $differentialRows = [];
    private $questionSmiles = [];
    private array $sourceDetailsTemp = [];

    public function rules()
    {
        return [
            ['file', 'file', 'skipOnEmpty' => false, 'checkExtensionByMimeType' => false,  'extensions' => ['json']],
        ];
    }

    public function save()
    {
        $extraQuestionService = new ExtraQuestionService();
        $jsonCrypt = new JsonCrypt();
        try {
            $pollArray = Json::decode($jsonCrypt->decrypt(file_get_contents($this->file->tempName)));

        } catch (InvalidArgumentException) {
            throw new \Exception('Некорректный формат файла');
        } catch (\Exception $e) {
            throw new \Exception('Cинтаксическая ошибка');
        }

        if (!isset($pollArray[FoquzPoll::class])) {
            throw new \Exception('Данные опроса не найдены');
        }

        if ($pollArray[FoquzPoll::class]['is_auto'] === 1) {
            throw new \Exception('Невозможно загрузить тестовый опрос');
        }

        $this->sourceCompanyId = $pollArray[FoquzPoll::class]['company_id'];
        $pollArray[FoquzPoll::class]['company_id'] = $this->companyId;
        $pollArray[FoquzPoll::class]['key'] = uniqid('F');
        $pollArray[FoquzPoll::class]['sent_answers_count'] = 0;
        $pollArray[FoquzPoll::class]['opened_answers_count'] = 0;
        $pollArray[FoquzPoll::class]['in_progress_answers_count'] = 0;
        $pollArray[FoquzPoll::class]['filled_answers_count'] = 0;
        $pollArray[FoquzPoll::class]['answers_count'] = 0;
        $pollArray[FoquzPoll::class]['is_active'] = 1;
        $pollArray[FoquzPoll::class]['is_published'] = 0;

        $this->saveAll($pollArray);

        $extraQuestionService->fixQuestionDetailId($this->sourceDetailsTemp);
        $this->sourceDetailsTemp = [];
    }

    public function getPoll() : FoquzPoll
    {
        return $this->poll;
    }

    private function saveAll($data, $parentId = null)
    {
        foreach ($data as $key => $fields) {
            if (class_exists($key) && in_array($key, self::ALLOWED_CLASSES, true)) {
                if ($fields === null || $fields === []) {
                    continue;
                }
                if(isset($fields[0])) {
                    foreach ($fields as $elem) {
                        $this->saveAll([$key => $elem], $parentId);
                    }
                }else {
                    $parent = $this->saveEntity($key, $fields, $parentId);

                    if ($key === FoquzQuestionDetail::class) {
                        $sourceDetailsTemp[$fields['id']] = $parent;
                        $this->sourceDetailsTemp[$fields['id']] = $parent;
                    }

                    $this->saveAll($fields, $parent->id);
                }
            }
        }
    }

    private function saveEntity($className, $attrs, $parentId = null) : ActiveRecord
    {
        /** @var ActiveRecord $model */
        $model = new $className();
        $model->setAttributes($attrs);
        $this->setLink($model, $attrs);

        if ($className === FoquzQuestionIntermediateBlockSetting::class && $this->sourceCompanyId !== $this->companyId) {
            $model->pool_id = null;
        }
        if ($parentId !== null) {
            if(!isset($attrs['parent_identity_field']) || $attrs['parent_identity_field'] === null) {
                throw new \Exception('Не указан родительский аттрибут');
            }
            $parentAttr = $attrs['parent_identity_field'];
            $model->$parentAttr = $parentId;
        }

        if ($className === FoquzPoll::class && $folder_id = \Yii::$app->request->getBodyParam('folder_id')) {
            $model->folder_id = $folder_id;
        }

        if ($className === FoquzFile::class) {
            /** @var FoquzFile $model */
            $oldFile = FoquzFile::findOne([
                'entity_type' => $model->entity_type,
                'entity_id' => $model->entity_id,
                'file_path' => $model->file_path
            ]);
            switch ($model->entity_type) {
                case FoquzFile::TYPE_DETAIL:
                    if (!$oldFile || empty($this->variantLinks[$model->entity_id])) {
                        return new FoquzFile();
                    }
                    $newModel = (new FileService())->copyFile($oldFile, $this->variantLinks[$model->entity_id]);
                    break;
                case FoquzFile::TYPE_SELF_VARIANT:
                    $newModel = null;
                    if ($oldFile && !empty($this->variantLinks[$model->entity_id])) {
                        $newModel = (new FileService())->copyFile($oldFile, $this->variantLinks[$model->entity_id]);
                    }
                    if ($oldFile && !empty($this->questionLinks[$model->entity_id])) {
                        $newModel = (new FileService())->copyFile($oldFile, $this->questionLinks[$model->entity_id]);
                    }
                    if (!$newModel) {
                        return new FoquzFile();
                    }
                    break;
            }
            if (empty($newModel)) {
                return new FoquzFile();
            }
            return $newModel;
        }

        if ($model instanceof FoquzQuestionDetail) {
            $parent = FoquzQuestionDetail::findOne($model->question_detail_id);
            if (!$parent) {
                $model->question_detail_id = null;
            }
        }

        if ($model instanceof FoquzQuestionCardSortingCategoryLang) {
            $model->foquz_poll_lang_id = $this->pollLangs[$attrs['foquz_poll_lang_id']];
        }

        if(!$model->save()) {
            \Yii::error($model->errors);
//            echo '<pre>' . print_r($model->errors, true) . '</pre>';
//            echo '<pre>' . print_r($model, true) . '</pre>'; die;
            throw new \RuntimeException("Ошибка сохранения модели");
        }

        $this->collectLinks($model, $attrs);
        return $model;
    }

    private function collectLinks($model, $attrs)
    {
        if ($model instanceof FoquzPoll) {
            $this->poll = $model;
        }

        if ($model instanceof FoquzPollDisplayPage) {
            $this->pagesLinks[$attrs['id']] = $model->id;
        }

        if ($model instanceof FoquzQuestionDetail) {
            $this->variantLinks[$attrs['id']] = $model->id;
        }

        if ($model instanceof FoquzQuestion) {
            $this->question = $model;
            $this->questionLinks[$attrs['id']] = $model->id;
            if ($model->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX && $model->donor) {
                $matrixSettings = json_decode($model->matrix_settings, true);
                $newRows = [];
                foreach ($matrixSettings['donorRows'] as $donorRow) {
                    if (isset($this->variantLinks[$donorRow])) {
                        $newRows[] = $this->variantLinks[$donorRow];
                    } else {
                        $newRows[] = $donorRow;
                    }
                }
                $matrixSettings['donorRows'] = $newRows;
                $model->matrix_settings = json_encode($matrixSettings, JSON_UNESCAPED_UNICODE);
                $model->save();
            }
        }

        if ($model instanceof FoquzQuestionDetail) {
            $this->recipientLinks[$attrs['id']] = $model->id;
        }

        if ($model instanceof FoquzPollLang) {
            $this->pollLangs[$attrs['id']] = $model->id;
        }
        if ($model instanceof FoquzQuestionFileLang) {
            $this->fileLangs[$attrs['id']] = $model->id;
        }
        if ($model instanceof FoquzQuestionFormFieldLang) {
            $this->formLangs[$attrs['id']] = $model->id;
        }
        if ($model instanceof FoquzQuestionDifferentialRow) {
            $this->differentialRows[$attrs['id']] = $model->id;
        }
        if ($model instanceof FoquzQuestionSmile) {
            $this->questionSmiles[$attrs['id']] = $model->id;
        }
        if ($model instanceof FoquzQuestionMatrixElement) {
            $this->matrixElementLinks[$attrs['id']] = $model->id;
        }
        if ($model instanceof FoquzQuestionMatrixElementVariant) {
            $this->matrixElementVariantLinks[$attrs['id']] = $model->id;
        }
    }

    private function setLink($model, $attrs)
    {
        if ($model instanceof FoquzPoll) {
            $model->is_short_link = 0;
            if ($model->dictionary_id) {
                $found = Dictionary::find()
                    ->andWhere(['dictionaries.company_id' => $this->companyId])
                    ->andWhere(['dictionaries.deleted' => 0, 'dictionaries.is_active' => 1])
                    ->exists();
                $model->dictionary_id = $found ? $model->dictionary_id : null ;
            }
        }

        if ($model instanceof FoquzQuestion) {
            $model->answers_from = date('Y-m-d H:i:s');
            if (isset($attrs['donor']) && isset($this->questionLinks[$attrs['donor']])) {
                $model->donor = $this->questionLinks[$attrs['donor']];
            }
            if (isset($attrs['donor_rows'], $this->questionLinks[$attrs['donor_rows']])) {
                $model->donor_rows = $this->questionLinks[$attrs['donor_rows']];
                $donor = FoquzQuestion::findOne($this->questionLinks[$attrs['donor_rows']]);
                if ($donor && $donor->donor) {
                    $donor = $donor->getMainDonor();
                }
                if (
                    $donor &&
                    $donor->main_question_type === FoquzQuestion::TYPE_DICTIONARY &&
                    empty(json_decode($donor->detail_question, true) ?? [])
                ) {
                    $model->donor_rows = null;
                }
            } else {
                $model->donor_rows = null;
            }
            if (isset($attrs['donor_columns'], $this->questionLinks[$attrs['donor_columns']])) {
                $model->donor_columns = $this->questionLinks[$attrs['donor_columns']];
                $donor = FoquzQuestion::findOne($this->questionLinks[$attrs['donor_columns']]);
                if ($donor && $donor->donor) {
                    $donor = $donor->getMainDonor();
                }
                if (
                    $donor &&
                    $donor->main_question_type === FoquzQuestion::TYPE_DICTIONARY &&
                    empty(json_decode($donor->detail_question, true) ?? [])
                ) {
                    $model->donor_rows = null;
                }
            } else {
                $model->donor_columns = null;
            }
            if ($model->dictionary_element_id) {
                $found = DictionaryElement::find()
                    ->leftJoin('dictionaries', 'dictionaries.id = dictionaries_elements.dictionary_id')
                    ->where(['dictionaries_elements.id' => $model->dictionary_element_id])
                    ->andWhere(['dictionaries.company_id' => $this->companyId])
                    ->andWhere(['dictionaries.deleted' => 0, 'dictionaries.is_active' => 1])
                    ->andWhere(['dictionaries_elements.deleted' => 0, 'dictionaries_elements.is_active' => 1])
                    ->exists();
                $model->dictionary_element_id = $found ? $model->dictionary_element_id : null;
            }
            if ((int)$model->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                if ($model->dictionary_id) {
                    $found = Dictionary::find()
                        ->andWhere(['dictionaries.company_id' => $this->companyId])
                        ->andWhere(['dictionaries.deleted' => 0, 'dictionaries.is_active' => 1])
                        ->exists();
                    $model->dictionary_element_id = $found ? $model->dictionary_element_id : null;
                }
                if (!$model->dictionary_id) {
                    $model->detail_question = json_encode([]);
                } else {
                    $elements = json_decode($model->detail_question) ?? [];
                    $companyElements = DictionaryElement::find()
                        ->select('dictionaries_elements.id')
                        ->leftJoin('dictionaries', 'dictionaries.id = dictionaries_elements.dictionary_id')
                        ->where(['dictionaries_elements.id' => $elements])
                        ->andWhere(['dictionaries.company_id' => $this->companyId])
                        ->andWhere(['dictionaries.deleted' => 0, 'dictionaries.is_active' => 1])
                        ->andWhere(['dictionaries_elements.deleted' => 0, 'dictionaries_elements.is_active' => 1])
                        ->column();
                    $elements = array_intersect($elements, $companyElements);
                    $model->detail_question = json_encode(array_values($elements), JSON_UNESCAPED_UNICODE);
                }
                $model->deleted_detail_question = json_encode([]);
            }
            if ($model->main_question_type === FoquzQuestion::TYPE_FILIAL && $model->detail_question) {
                $filials = json_decode($model->detail_question) ?? [];
                foreach ($filials as $key => $value) {
                    $filialExist = Filial::find()->where([
                        'id' => $value,
                        'is_active' => 1,
                        'company_id' => $this->companyId,
                    ])->exists();

                    if (!$filialExist) {
                        unset($filials[$key]);
                    }
                }
                $model->detail_question = json_encode(array_values($filials), JSON_UNESCAPED_UNICODE);
            }
        }

        if ($model instanceof RecipientQuestionDetail && isset($this->recipientLinks[$attrs['question_detail_id']])) {
            $model->question_detail_id = $this->recipientLinks[$attrs['question_detail_id']];
        }

        if ($model instanceof RecipientQuestionDetail && !empty($attrs['dictionary_element_id'])) {
            $found = DictionaryElement::find()
                ->leftJoin('dictionaries', 'dictionaries.id = dictionaries_elements.dictionary_id')
                ->where(['dictionaries_elements.id' => $attrs['dictionary_element_id']])
                ->andWhere(['dictionaries.company_id' => $this->companyId])
                ->andWhere(['dictionaries.deleted' => 0, 'dictionaries.is_active' => 1])
                ->andWhere(['dictionaries_elements.deleted' => 0, 'dictionaries_elements.is_active' => 1])
                ->exists();
            $model->dictionary_element_id = $found ? $attrs['dictionary_element_id'] : null;
        }

        if ($model instanceof FoquzPollDisplayPageQuestion && isset($this->pagesLinks[$attrs['display_page_id']])) {
            $model->display_page_id = $this->pagesLinks[$attrs['display_page_id']];
        }

        if ($model instanceof FoquzPollQuestionsLogic) {
            if (isset($this->questionLinks[$attrs['question_id']])) {
                $model->question_id = $this->questionLinks[$attrs['question_id']];
            }
            if (isset($this->questionLinks[$attrs['jump_question_id']])) {
                $model->jump_question_id = $this->questionLinks[$attrs['jump_question_id']];
            }
            if (isset($this->pagesLinks[$attrs['jump_display_page_id']])) {
                $model->jump_display_page_id = $this->pagesLinks[$attrs['jump_display_page_id']];
            }
            if ($attrs['variants']) {
                foreach(Json::decode($attrs['variants']) as $variant) {
                    if(array_key_exists($variant, $this->variantLinks)) {
                        $newVariants[] = $this->variantLinks[$variant];
                    } else {
                        $newVariants[] = $variant;
                    }
                }
                if (isset($newVariants)) {
                    $model->variants = Json::encode(array_map('intval', $newVariants));
                }
            }
        }
        if ($model instanceof FoquzPollQuestionViewLogic) {
            if (isset($this->questionLinks[$attrs['question_id']])) {
                $model->question_id = $this->questionLinks[$attrs['question_id']];
            }
            if (isset($this->questionLinks[$attrs['condition_question_id']])) {
                $model->condition_question_id = $this->questionLinks[$attrs['condition_question_id']];
            }
            if (isset($attrs['variants']) && $attrs['variants']) {
                foreach($attrs['variants'] as $variant) {
                    if (isset($variant['row'])) {
                        if (array_key_exists($variant['row'], $this->variantLinks)) {
                            $newVariants[] = ['col' => $variant['col'], 'row' => $this->variantLinks[$variant['row']]];
                        } else {
                            $newVariants[] = $variant;
                        }
                    } elseif (array_key_exists($variant, $this->variantLinks)) {
                        $newVariants[] = $this->variantLinks[$variant];
                    } else {
                        $newVariants[] = $variant;
                    }
                }
                if (isset($newVariants)) {
                    $model->variants = $newVariants;
                }
            }
        }

        if ($model instanceof FoquzPollDisplaySetting) {
            $model->setScenario(FoquzPollDisplaySetting::SCENARIO_COPY);
            if (!empty($model->random_exclusion)) {
                $randomExclusion = $model->random_exclusion;
                $newRandomExclusion = [];
                foreach ($randomExclusion as $value) {
                    if (isset($this->questionLinks[$value])) {
                        $newRandomExclusion[] = $this->questionLinks[$value];
                    }
                }
                $model->random_exclusion = $newRandomExclusion;
            }
        }

        if ($model instanceof FoquzPollLang) {
            $model->foquz_poll_id = $this->poll->id;
        }
        if ($model instanceof FoquzQuestionLang) {
            if (isset($this->pollLangs[$attrs['foquz_poll_lang_id']])) {
                $model->foquz_poll_lang_id = $this->pollLangs[$attrs['foquz_poll_lang_id']];
            }
            if ($model->labels && in_array($this->question->main_question_type, [
                FoquzQuestion::TYPE_SEM_DIFFERENTIAL,
                FoquzQuestion::TYPE_SMILE_RATING
            ], true)) {
                $newLabels = [];
                if (!is_array($model->labels)) {
                    $model->labels = json_decode($model->labels, false);
                }
                foreach ($model->labels as $key => $label) {
                    if (
                        $this->question->main_question_type === FoquzQuestion::TYPE_SEM_DIFFERENTIAL &&
                        isset($this->differentialRows[$key])
                    ) {
                        $newLabels[$this->differentialRows[$key]] = $label;
                    } elseif (
                        $this->question->main_question_type === FoquzQuestion::TYPE_SMILE_RATING &&
                        isset($this->questionSmiles[$key])
                    ) {
                        $newLabels[$this->questionSmiles[$key]] = $label;
                    }
                }
                if ($newLabels) {
                    $model->labels = $newLabels;
                }
            }
        }
        if ($model instanceof FoquzQuestionFileLang) {
            if (isset($this->fileLangs[$attrs['foquz_question_file_id']])) {
                $model->foquz_question_file_id = $this->fileLangs[$attrs['foquz_question_file_id']];
            }
            if (isset($this->pollLangs[$attrs['foquz_poll_lang_id']])) {
                $model->foquz_poll_lang_id = $this->pollLangs[$attrs['foquz_poll_lang_id']];
            }
        }
        if ($model instanceof FoquzQuestionFormFieldLang) {
            if (isset($this->formLangs[$attrs['form_field_id']])) {
                $model->form_field_id = $this->formLangs[$attrs['form_field_id']];
            }
            if (isset($this->pollLangs[$attrs['foquz_poll_lang_id']])) {
                $model->foquz_poll_lang_id = $this->pollLangs[$attrs['foquz_poll_lang_id']];
            }
        }
        if ($model instanceof FoquzQuestionDetailLang) {
            if (isset($this->questionLinks[$attrs['foquz_question_id']])) {
                $model->foquz_question_id = $this->questionLinks[$attrs['foquz_question_id']];
            }
            if (isset($this->pollLangs[$attrs['foquz_poll_lang_id']])) {
                $model->foquz_poll_lang_id = $this->pollLangs[$attrs['foquz_poll_lang_id']];
            }
        }
        if (($model instanceof FoquzQuestionMatrixElement) && isset($this->questionLinks[$attrs['foquz_question_id']])) {
            $model->foquz_question_id = $this->questionLinks[$attrs['foquz_question_id']];
            if (!empty($attrs['donor_variant_id']) && isset($this->variantLinks[$attrs['donor_variant_id']])) {
                $model->donor_variant_id = $this->variantLinks[$attrs['donor_variant_id']];
            } else {
                $model->donor_variant_id = null;
            }
            if (!empty($attrs['donor_dictionary_element_id'])) {
                $found = DictionaryElement::find()
                    ->leftJoin('dictionaries', 'dictionaries.id = dictionaries_elements.dictionary_id')
                    ->where(['dictionaries_elements.id' => $attrs['donor_dictionary_element_id']])
                    ->andWhere(['dictionaries.company_id' => $this->companyId])
                    ->andWhere(['dictionaries.deleted' => 0, 'dictionaries.is_active' => 1])
                    ->andWhere(['dictionaries_elements.deleted' => 0, 'dictionaries_elements.is_active' => 1])
                    ->exists();
                $model->donor_dictionary_element_id = $found ? $attrs['donor_dictionary_element_id'] : null;
            }
        }
        if (($model instanceof FoquzQuestionMatrixElementVariant) && isset($this->matrixElementLinks[$attrs['matrix_element_id']])) {
            $model->matrix_element_id = $this->matrixElementLinks[$attrs['matrix_element_id']];
        }
        if ($model instanceof FoquzQuestionMatrixElementLang) {
            if (isset($this->matrixElementLinks[$attrs['matrix_element_id']])) {
                $model->matrix_element_id = $this->matrixElementLinks[$attrs['matrix_element_id']];
            }
            if (isset($this->pollLangs[$attrs['poll_lang_id']])) {
                $model->poll_lang_id = $this->pollLangs[$attrs['poll_lang_id']];
            }
        }
        if ($model instanceof FoquzQuestionMatrixElementVariantLang) {
            if (isset($this->matrixElementVariantLinks[$attrs['variant_id']])) {
                $model->variant_id = $this->matrixElementVariantLinks[$attrs['variant_id']];
            }
            if (isset($this->pollLangs[$attrs['poll_lang_id']])) {
                $model->poll_lang_id = $this->pollLangs[$attrs['poll_lang_id']];
            }
        }
        if (($model instanceof FoquzQuestionDetail) && $model->dictionary_element_id) {
            $found = DictionaryElement::find()
                ->leftJoin('dictionaries', 'dictionaries.id = dictionaries_elements.dictionary_id')
                ->where(['dictionaries_elements.id' => $model->dictionary_element_id])
                ->andWhere(['dictionaries.company_id' => $this->companyId])
                ->andWhere(['dictionaries.deleted' => 0, 'dictionaries.is_active' => 1])
                ->andWhere(['dictionaries_elements.deleted' => 0, 'dictionaries_elements.is_active' => 1])
                ->exists();
            $model->dictionary_element_id = $found ?  $model->dictionary_element_id : null;
        }

        if (($model instanceof FoquzQuestionIntermediateBlockSettingLang) && isset($this->pollLangs[$attrs['lang_id']])) {
            $model->lang_id = $this->pollLangs[$attrs['lang_id']];
        }
    }
}
