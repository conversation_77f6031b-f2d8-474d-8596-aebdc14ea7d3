<?php

namespace app\modules\foquz\models;

use yii\db\ActiveRecord;

/**
 * Class for table foquz_poll_display_page_questions
 * @property int $id
 * @property int $display_page_id
 * @property int $question_id
 *
 * @property FoquzPollDisplayPage $displayPage
 * @property FoquzQuestion $question
 */
class FoquzPollDisplayPageQuestion extends BaseModel
{
    public static function tableName()
    {
        return 'foquz_poll_display_page_questions';
    }

    public function getDisplayPage()
    {
        return $this->hasOne(FoquzPollDisplayPage::class, ['id' => 'display_page_id']);
    }

    public function getQuestion()
    {
        return $this->hasOne(FoquzQuestion::class, ['id' => 'question_id'])->orderBy(['position' => SORT_ASC]);
    }
}