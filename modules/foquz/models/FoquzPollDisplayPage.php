<?php

namespace app\modules\foquz\models;

use yii\db\ActiveRecord;
use yii\helpers\ArrayHelper;

/**
 * Class for table foquz_poll_display_pages
 *
 * @property int $id
 * @property int $foquz_poll_id
 * @property string $name
 * @property boolean $random_order
 * @property int $order
 * @property int $random_exclusion Страница исключена из случайного порядка
 *
 * @property FoquzQuestion[] $questions;
 * @property FoquzPoll $poll
 * @property FoquzPollDisplayPageQuestion[] $displayPageQuestions
 */
class FoquzPollDisplayPage extends BaseModel
{
    public static function tableName()
    {
        return 'foquz_poll_display_pages';
    }

    public function fields()
    {
        return parent::fields()+[
            'questions' => function($model) {
                $result = [];
                foreach ($model->displayPageQuestions as $question) {
                    $result[] = ['id' => $question->question_id];
                }
                return $result;
            }
        ];
    }

    public function rules()
    {
        return [
            [['name'], 'string'],
            [['random_order', 'random_exclusion'], 'boolean'],
            [['foquz_poll_id', 'order'], 'integer']
        ];
    }

    public function getQuestions()
    {
        return $this->hasMany(FoquzQuestion::className(), ['id' => 'question_id'])
            ->viaTable('foquz_poll_display_page_questions', ['display_page_id' => 'id']);
    }

    public function getPoll()
    {
        return $this->hasOne(FoquzPoll::className(), ['id' => 'foquz_poll_id']);
    }

    public function getDisplayPageQuestions()
    {
        return $this->hasMany(FoquzPollDisplayPageQuestion::class, ['display_page_id' => 'id']);
    }
}