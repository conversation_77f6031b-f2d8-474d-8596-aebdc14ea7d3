<?php

namespace app\modules\foquz\models;

use app\models\Filial;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\quotes\QuoteService;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\BaseActiveRecord;
use yii\helpers\ArrayHelper;
use yii\httpclient\Client;
use yii\web\ServerErrorHttpException;

/**
 * This is the model class for table "foquz_poll_widget".
 *
 * @property int $id
 * @property bool $is_active Виджет активен (0 - нет, 1 - да)
 * @property int $poll_id ID опроса
 * @property string|null $name Название виджета
 * @property int|null $filial_id ID филиала
 * @property int $appearance Появление виджета (0-без клика, 1-по клику)
 * @property int $form Форма виджета
 * @property int $button_type Тип кнопки
 * @property int $position Позиция виджета
 * @property string|null $button_text Текст кнопки
 * @property string|null $font Шрифт
 * @property int|null $font_size Размер шрифта
 * @property int $bold Жирный шрифт
 * @property int $italic Курсивный шрифт
 * @property string|null $text_color Цвет текста
 * @property string|null $background_color Цвет фона кнопки
 * @property int $stroke Обводка
 * @property int $simple Упрощенный вид опроса
 * @property int $show_until Логика отображения виджета (0 - не показывать если кнопка / опрос показан, 1 - не показывать если опрос показан, 2 - не показывать если респондент ответил на хотя бы один вопрос, 3 - не показывать если респондент заполнил опрос до конца, 4 - показывать всегда)
 * @property boolean $close_by_finish_button Закрывать виджет по кнопке «Завершить»
 * @property int|null $autoclose_delay Автоматически закрыть виджет после завершения опроса через N секунд
 * @property int $targeting Таргетинг
 * @property int|null $priority Приоритет показа виджета
 * @property string|null $triggers Триггеры
 * @property string|null $triggers_status Статус таргетингов
 * @property string|null $window_settings Настройки окна виджета
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $scroll_depth Глубина прокрутки для таргетинга
 * @property int|null $poll_link_id Id ссылки на опрос
 *
 * @property Filial $filial
 * @property FoquzPoll $poll
 * @property FoquzPollLinkQuotes $pollLink
 * @property string $code
 */
class FoquzPollWidget extends BaseModel
{
    public const FORM_BUTTON = 0;
    public const FORM_SCALE = 1;
    public const FORM_POPUP = 2;
    public const FORM_PAGE_STOP = 3;
    public const FORM_SIMPLE_PAGE_STOP = 4;
    public const FORM_HELLO_BOARD = 5;

    public const BUTTON_TYPE_TEXT = 0;
    public const BUTTON_TYPE_ICON = 1;
    public const BUTTON_TYPE_SCALE = 2;

    public const POSITION_FIXED = 0;
    public const POSITION_FLOAT_LEFT = 1;
    public const POSITION_FLOAT_RIGHT = 2;
    public const POSITION_BOTTOM_LEFT = 3;
    public const POSITION_BOTTOM_RIGHT = 4;

    public const DONT_SHOW_IF_BUTTON_SHOWED = 0;
    public const DONT_SHOW_IF_POLL_OPENED = 1;
    public const DONT_SHOW_IF_HAS_ANSWER = 2;
    public const DONT_SHOW_IF_POLL_PASSED = 3;
    public const SHOW_ALWAYS = 4;

    public const TYPE_PERCENT = 0;
    public const TYPE_ABSOLUT = 1;

    public const WINDOW_POSITION_LEFT_BOTTOM = 'left-bottom';
    public const WINDOW_POSITION_RIGHT_BOTTOM = 'right-bottom';
    public const WINDOW_POSITION_LEFT_TOP = 'left-top';
    public const WINDOW_POSITION_RIGHT_TOP = 'right-top';
    public const WINDOW_POSITION_CENTER_LEFT = 'left-center';
    public const WINDOW_POSITION_CENTER_RIGHT = 'right-center';
    public const WINDOW_POSITION_MIDDLE = 'center-center';

    public function getForms()
    {
        return [
            self::FORM_BUTTON => 'Кнопка',
            self::FORM_SCALE => 'Шкала оценок',
            self::FORM_POPUP => 'Всплывающее окно',
            self::FORM_PAGE_STOP => 'Page-stop',
            self::FORM_SIMPLE_PAGE_STOP => 'Упрощенный Page-stop',
            self::FORM_HELLO_BOARD => 'Hello-board',
        ];
    }

    public function getButtonTypes()
    {
        return [
            self::BUTTON_TYPE_TEXT => 'Текст',
            self::BUTTON_TYPE_ICON => 'Иконка',
            self::BUTTON_TYPE_SCALE => 'Шкала',
        ];
    }

    public function getPositions()
    {
        return [
            self::POSITION_FIXED => 'Фиксированное положение',
            self::POSITION_FLOAT_LEFT => 'Плавающая слева',
            self::POSITION_FLOAT_RIGHT => 'Плавающая справа',
            self::POSITION_BOTTOM_LEFT => 'Внизу слева',
            self::POSITION_BOTTOM_RIGHT => 'Внизу справа',
        ];
    }

    public function getShowUntil()
    {
        return [
            self::DONT_SHOW_IF_BUTTON_SHOWED => 'Не показывать если кнопка / опрос показан',
            self::DONT_SHOW_IF_POLL_OPENED => 'Не показывать если опрос показан',
            self::DONT_SHOW_IF_HAS_ANSWER => 'Не показывать если респондент ответил на хотя бы один вопрос',
            self::DONT_SHOW_IF_POLL_PASSED => 'Не показывать если респондент заполнил опрос до конца',
            self::SHOW_ALWAYS => 'Показывать всегда',
        ];
    }

    public function getWindowPositions()
    {
        return [
            self::WINDOW_POSITION_LEFT_BOTTOM => 'Слева внизу',
            self::WINDOW_POSITION_RIGHT_BOTTOM => 'Справа внизу',
            self::WINDOW_POSITION_LEFT_TOP => 'Слева вверху',
            self::WINDOW_POSITION_RIGHT_TOP => 'Справа вверху',
            self::WINDOW_POSITION_CENTER_LEFT => 'Слева по центру',
            self::WINDOW_POSITION_CENTER_RIGHT => 'Справа по центру',
            self::WINDOW_POSITION_MIDDLE => 'Посередине',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    BaseActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    BaseActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at']
                ],
                'value' => (new \DateTime())->format('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'foquz_poll_widget';
    }

    /**
     * {@inheritdoc}
     */
    public function fields()
    {
        return parent::fields()+[
                'filial' => function($model) {
                    return [
                        'id' => $model->filial->id ?? null,
                        'name' => $model->filial->name ?? null,
                    ];
                },
                'code' => function($model) {
                    return $model->code;
                },
                'poll_name' => function ($model) {
                    return $model->poll->name;
                },
            ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['poll_id', 'name'], 'required'],
            [['poll_id', 'filial_id', 'form', 'button_type', 'position', 'font_size', 'bold', 'italic', 'stroke',
                'simple', 'targeting', 'appearance', 'show_until'], 'integer'],
            [['triggers', 'triggers_status', 'created_at', 'updated_at', 'scroll_depth'], 'safe'],
            [['window_settings'], 'windowSettingsValidator'],
            [['name', 'button_text', 'font', 'text_color', 'background_color'], 'string', 'max' => 255],
            [['is_active', 'bold', 'italic', 'stroke', 'simple', 'targeting', 'close_by_finish_button'], 'boolean'],
            [['filial_id'], 'exist', 'skipOnError' => true, 'targetClass' => Filial::class, 'targetAttribute' => ['filial_id' => 'id'], 'filter' => ['company_id' => Yii::$app->user->identity->company->id]],
            [['priority'], 'integer', 'min' => 0],
            [['poll_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPoll::class, 'targetAttribute' => ['poll_id' => 'id'], 'filter' => ['company_id' => Yii::$app->user->identity->company->id]],
            [['form'], 'in', 'range' => array_keys($this->getForms())],
            [['button_type'], 'in', 'range' => array_keys($this->getButtonTypes())],
            [['position'], 'in', 'range' => array_keys($this->getPositions())],
            [['show_until'], 'in', 'range' => array_keys($this->getShowUntil())],
            [['scroll_depth'], 'scrollDepthValidator' ],
            [['poll_link_id'], 'integer'],
            [['poll_link_id'], 'validateLink'],
            [['autoclose_delay'], 'integer', 'min' => 0, 'max' => 999, 'skipOnEmpty' => true],
        ];
    }

    public function validateLink($attribute): void
    {
        $poll_link_id = $this->$attribute;
        $m = FoquzPollLinkQuotes::find()
            ->where(['id' => $poll_link_id])
            ->andWhere(['poll_id' => $this->poll_id])
            ->exists();
        if (!$m) {
            $this->addError($attribute, 'Значение «Poll Link Id» неверно.');
        }
    }

    public function scrollDepthValidator($attribute)
    {
        try {
            $scrollDepth = $this->$attribute;
            if (!in_array($scrollDepth['vertical']['type'], [self::TYPE_PERCENT, self::TYPE_ABSOLUT]) ||
                !in_array($scrollDepth['horizontal']['type'], [self::TYPE_PERCENT, self::TYPE_ABSOLUT])) {
                throw new \InvalidArgumentException(Yii::t('main', 'Неверное значение единицы измерения'));
            }

            $value = explode(',', $scrollDepth['vertical']['value']);
            if (count(array_filter($value, fn($n) => is_numeric($n) && (int)$n == $n)) !== count($value)) {
                throw new \InvalidArgumentException(Yii::t('main', 'Нужно указать целое число, если чисел несколько, то через запятую'));
            }
            $value = explode(',', $scrollDepth['horizontal']['value']);
            if (count(array_filter($value, fn($n) => is_numeric($n) && (int)$n == $n)) !== count($value)) {
                throw new \InvalidArgumentException(Yii::t('main', 'Нужно указать целое число, если чисел несколько, то через запятую'));
            }
        } catch (\Throwable $ex) {
            $this->addError($attribute, $ex->getMessage());
        }
    }

    /**
     * {@inheritdoc}
     */
    public function beforeValidate()
    {
        if ($this->name) {
            /** @var FoquzPollWidget $lastWidget */
            $lastWidget = self::find()
                ->where(['poll_id' => $this->poll_id])
                ->andWhere(['OR',
                    ['name' => $this->name],
                    ['REGEXP', 'name', $this->name . ' \([0-9]+\)$']
                ])
                ->andFilterWhere(['<>', 'id', $this->id])
                ->orderBy(['id' => SORT_DESC])
                ->one();

            if ($lastWidget && $lastWidget->name === $this->name) {
                $this->name .= ' (' . 2 . ')';
            } elseif ($lastWidget && preg_match('/' . $this->name . ' \((\d+)\)/', $lastWidget->name, $matches)) {
                $this->name .= ' (' . ((int)$matches[1] + 1) . ')';
            }
        }
        return parent::beforeValidate();
    }

    public function windowSettingsValidator($attribute, $params)
    {
        $window_settings = $this->$attribute;
        $height = $window_settings['height'];
        if (is_null($height) || $height === 'null') {
            return;
        }
        $height = (int)$height;
        if ($height < 0 || $height > 100) {
            $this->addError($attribute, Yii::t('main', 'Неправильное значение высоты окна'));
        }

        $width = $window_settings['width'];
        if (is_null($width['value']) || $width['value'] === 'null') {
            return;
        }
        $width['type'] = (int)$width['type'];
        $width['value'] = (int)$width['value'];
        if (array_search($width['type'], [0, 1]) === false) {
            $this->addError($attribute, Yii::t('main', 'Неправильное значение единицы измерения ширины окна'));
        }

        if ($width['value'] < 0) {
            $this->addError($attribute, Yii::t('main', 'Неправильное значение ширины окна'));
        }

        if ($width['type'] === self::TYPE_PERCENT) {
            if ($width['value'] > 100) {
                $this->addError($attribute, Yii::t('main', 'Неправильное значение ширины окна в процентах'));
            }
        } elseif ($width['type'] === self::TYPE_ABSOLUT) {
            // Полноэкранный режим - минимальное ограничение 680.
            if ($this->form === self::FORM_PAGE_STOP) {
                if ($width['value'] < 680) {
                    $this->addError($attribute, Yii::t('main', 'Неправильное значение ширины окна для полноэкранного режима'));
                }
            }
            // Упрощённый режим - минимальное ограничение 480.
            if ($this->simple || $this->form === self::FORM_SIMPLE_PAGE_STOP) {
                if ($width['value'] < 480) {
                    $this->addError($attribute, Yii::t('main', 'Неправильное значение ширины окна для упрощенного режима'));
                }
            }
            // Hello-board - минимальное ограничение 360.
            if ($this->form === self::FORM_HELLO_BOARD) {
                if ($width['value'] < 360) {
                    $this->addError($attribute, Yii::t('main', 'Неправильное значение ширины окна для Hello-board'));
                }
            }
        }

        $round = $this->window_settings['round'];
        if ($round['value'] === 'null') {
            $round['value'] = null;
        }
        if (is_null($round['value']) || $round['value'] === 'null') {
            return;
        }

        $round['type'] = (int)$round['type'];
        $round['value'] = (int)$round['value'];
        if (array_search($round['type'], [0, 1]) === false) {
            $this->addError($attribute, Yii::t('main', 'Неправильное значение единицы измерения радиуса округления окна'));
        }

        if($round['value'] < 0) {
            $this->addError($attribute, Yii::t('main', 'Неправильное значение радиуса округления окна'));
        }
        if ($round['type'] === self::TYPE_PERCENT) {
            if ($round['value'] > 50) {
                $this->addError($attribute, Yii::t('main', 'Неправильное значение радиуса округления окна'));
            }
        }

        $position = $this->window_settings['position'];
        if (is_null($position) || $position === 'null') {
            return;
        }

        if (!array_key_exists($position, $this->getWindowPositions())) {
            $this->addError($attribute, Yii::t('main', 'Неправильное значение позиции окна'));
        }
    }

    public function afterFind()
    {
        parent::afterFind();

        // для старых виджетов типа hello-board переносим настройки позиции окна в window_settings
        if ($this->form === self::FORM_HELLO_BOARD) {
            if (is_null($this->window_settings)) {
                $position = self::WINDOW_POSITION_LEFT_BOTTOM;
                if ($this->position === 2) {
                    $position = self::WINDOW_POSITION_RIGHT_BOTTOM;
                }

                $this->window_settings = [
                    'round' => [
                        'type' => self::TYPE_ABSOLUT,
                        'value' => null,
                    ],
                    'width' => [
                        'type' => self::TYPE_ABSOLUT,
                        'value' => null,
                    ],
                    'height' => null,
                    'position' => $position,
                ];
            }
        }
        if (is_null($this->poll_link_id)) {
            $this->poll_link_id = $this->getFirstPollLinkId();
        }
    }
    public function beforeSave($insert)
    {
        if ($insert) {
            $this->poll_link_id = $this->getFirstPollLinkId();
        }
        return parent::beforeSave($insert);
    }

    /**
     * {@inheritdoc}
     */
    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);
        if (!empty(Yii::$app->params['widgetsUrl']) && !empty(Yii::$app->params['widgetsAuthKey'])) {
            $isQuotaFull = false;
            $quotaEnd = null;
            if ($this->poll_link_id) {
                $quotaService = QuoteService::getById($this->poll_link_id);
                $isQuotaFull = $quotaService->isQuotaLinkFull();
                $quotaEnd = $quotaService->getQuotaEnd();

            }

            $data = [
                'is_quota_full' => $isQuotaFull,
                'quota_end' => $quotaEnd,
                'widgets_ids' => [$this->id],
            ];
            $data['message'] = 'Обновление статусов квот для виджетов';
            Yii::info($data, 'app\widgets   ');

            $client = new Client();
            $response = $client->createRequest()
                ->setMethod('POST')
                ->setUrl(Yii::$app->params['widgetsUrl'] . '/v1/widget/update?authKey=' . urlencode(Yii::$app->params['widgetsAuthKey']))
                ->setData(ArrayHelper::merge($this->attributes, [
                    'is_quota_full' => $isQuotaFull,
                    'quota_end' => $quotaEnd,
                    'code' => $this->code,
                    'company_id' => $this->poll->company_id,
                    'is_active' => $this->poll->status === FoquzPoll::STATUS_NEW && !$this->poll->deleted && $this->poll->is_active && $this->is_active ? 1 : 0,
                ]))
                ->send();
            if (!$response->isOk) {
                throw new ServerErrorHttpException('Ошибка сохранения виджета');
            }
        }
        if (
            isset(Yii::$app->rabbit_queue, Yii::$app->rabbit) &&
            !empty($this->triggers_status['client_tags']) &&
            !WidgetCompanyContacts::find()->where(['company_id' => $this->poll->company_id])->exists()
        ) {
            $widgetCompanyContacts = new WidgetCompanyContacts([
                'company_id' => $this->poll->company_id,
            ]);
            $widgetCompanyContacts->save();
        }
    }

    /**
     *
     * {@inheritdoc}
     */
    public function afterDelete()
    {
        parent::afterDelete();
        $this->changeWidgetStatus('delete');
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'is_active' => 'Виджет активен',
            'poll_id' => 'ID опроса',
            'name' => 'Название виджета',
            'filial_id' => 'ID филиала',
            'appearance' => 'Появление виджета',
            'form' => 'Форма виджета',
            'button_type' => 'Тип кнопки',
            'position' => 'Позиция виджета',
            'button_text' => 'Текст кнопки',
            'font' => 'Шрифт',
            'font_size' => 'Размер шрифта',
            'bold' => 'Жирный шрифт',
            'italic' => 'Курсивный шрифт',
            'text_color' => 'Цвет текста',
            'background_color' => 'Цвет фона кнопки',
            'stroke' => 'Обводка',
            'simple' => 'Упрощенный вид опроса',
            'show_until' => 'Логика отображения виджета',
            'close_by_finish_button' => 'Закрывать виджет по кнопке «Завершить»',
            'priority' => 'Приоритет показа виджета',
            'targeting' => 'Таргетинг',
            'triggers' => 'Триггеры',
            'triggers_status' => 'Статус таргетингов',
            'window_settings' => 'Настройки окна',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'scroll_depth' => 'Глубина прокрутки',
        ];
    }

    /**
     * Gets query for [[Filial]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFilial()
    {
        return $this->hasOne(Filial::class, ['id' => 'filial_id']);
    }

    /**
     * Gets query for [[Poll]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPoll()
    {
        return $this->hasOne(FoquzPoll::class, ['id' => 'poll_id']);
    }

    public function getPollLinks()
    {
        return $this->hasMany(FoquzPollLinkQuotes::class, ['poll_id' => 'poll_id'])
            ->orderBy(['id' => SORT_ASC]);
    }

    public function getPollLink()
    {
        return $this->hasOne(FoquzPollLinkQuotes::class, ['id' => 'poll_link_id']);
    }

    public function getFirstPollLinkId(): int|null
    {
        $links = $this->pollLinks;
        return (isset($links[0])) ? $links[0]['id'] : null;
    }

    public function getCode()
    {
        return md5($this->poll->company_id . Yii::$app->params['widgetCodeSalt']);
    }

    public function changeWidgetStatus($action): void
    {
        self::changeWidgetsStatus([$this->id], $action);
    }

    public static function changeWidgetsStatus($IDs, $action): void
    {
        if (empty(Yii::$app->params['widgetsUrl']) || empty(Yii::$app->params['widgetsAuthKey'])) {
            return;
        }
        if (!in_array($action, ['delete', 'restore'])) {
            throw new \InvalidArgumentException('Incorrect action');
        }
        $client = new Client();
        $response = $client->createRequest()
            ->setMethod('POST')
            ->setUrl(Yii::$app->params['widgetsUrl'] . '/v1/widget/' . $action . '?authKey=' . Yii::$app->params['widgetsAuthKey'])
            ->setData(['id' => $IDs])
            ->send();
        if (!$response->isOk) {
            if ($action === 'delete') {
                throw new ServerErrorHttpException('Ошибка удаления виджетов');
            }

            throw new ServerErrorHttpException('Ошибка восстановления виджетов');
        }
    }
}
