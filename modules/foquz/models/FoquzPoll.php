<?php

namespace app\modules\foquz\models;

use app\components\RabbitMQComponent;
use app\helpers\DateTimeHelper;
use app\models\Client;
use app\models\company\Company;
use app\models\company\CompanyStaff;
use app\models\company\RequestSettings2Poll;
use app\models\Dictionary;
use app\models\DictionaryElement;
use app\models\Dish;
use app\models\Filial;
use app\models\google\sheets\GoogleSheet;
use app\models\User;
use app\modules\foquz\behaviors\UtmBehavior;
use app\modules\foquz\models\notifications\NotificationScript;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessing;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\answers\search\AnswerSearchService;
use app\modules\foquz\services\api\ExtraQuestionService;
use app\modules\foquz\services\export\usecase\CardSortingClosed;
use app\modules\foquz\services\export\usecase\FirstClick;
use app\modules\foquz\services\FileService;
use FFMpeg\Coordinate\TimeCode;
use FFMpeg\FFMpeg;
use mohorev\file\UploadImageBehavior;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\console\Application;
use yii\db\ActiveQuery;
use yii\db\ActiveRecord;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use app\models\Order;
use yii\web\NotFoundHttpException;

/**
 * This is the model class for table "{{%foquz_poll}}".
 *
 * @property int $id
 * @property int $created_at
 * @property int $updated_at
 * @property int $created_by
 * @property int $updated_by
 * @property int $status
 * @property int $company_id
 * @property string $name
 * @property string $image
 * @property string $description
 * @property string $key
 * @property boolean $is_tmp
 * @property boolean $is_qr_code
 * @property boolean $is_auto
 * @property boolean $date_start
 * @property boolean $date_end
 * @property boolean $trigger
 * @property boolean $trigger_time
 * @property int $goals_count
 * @property int $limit_count
 * @property boolean $goal_text
 * @property boolean $end_of_question
 * @property boolean $is_folder
 * @property integer $folder_id
 * @property integer $trigger_days
 * @property integer $notification_script_id
 * @property boolean $kiosk_mode
 * @property boolean $is_template
 * @property string $send_time
 * @property string $title
 * @property boolean $point_system
 * @property int $max_points
 * @property boolean $dont_send_if_passed
 * @property boolean $dont_send_if_passed_link Не отправлять опрос по ссылке, если он уже был пройден клиентом
 * @property boolean $dont_send_if_promocode_used
 * @property boolean $deleted
 * @property boolean $show_foquz_link
 * @property string $deleted_at
 * @property int $deleted_by
 * @property boolean $personal_data
 * @property string $time_to_pass
 * @property string $datetime_start
 * @property string $datetime_end
 * @property boolean $is_published
 * @property boolean $is_short_link
 * @property int|null $expiration_in_minutes
 * @property string $utm_source
 * @property string $utm_medium
 * @property string $utm_campaign
 * @property int|null $processing_time_in_minutes
 * @property int|null $processing_time_by_link_in_minutes
 * @property int|null $processing_time_for_client_in_minutes
 * @property string $code
 * @property int $time_to_restart_screen_type_end
 * @property string $stop_sending
 * @property string|null $stop_sending_link Не отправлять повторно опрос клиенту по ссылке
 * @property string|null $stop_sending_link_time Дополнительно чч:мм к stop_sending_link
 * @property boolean $is_active
 * @property bool $mailing_limit Игнорировать ограничение отправки опросов одному клиенту
 * @property int $mailing_frequency Отправлять этот опрос клиенту, если с момента отправки любого опроса прошло
 * @property int|null $widget_display_ignore_limit Игнорировать ограничения показа виджета для одного посетителя сайта и показывать ему виджет, если с момента показа любого виджета прошло N дней
 * @property int $widget_display_limit_type Не показывать повторно опрос в виджете посетителю сайта (0 - выключено, 1 - если он уже был показан, 2 - в течение периода)
 * @property int|null $widget_display_limit_days Не показывать повторно опрос в виджете посетителю сайта в течение N дней
 * @property string|null $widget_display_limit_time Не показывать повторно опрос в виджете посетителю сайта в течение времени (чч:мм)
 * @property int|null $widget_create_new_answer_limit Создавать новую анкету для респондента, если с момента прохождения предыдущей прошло N дней
 * @property int $answers_count Количество ответов (включая удаленные, нужно для тарифных лимитов)
 * @property int $default_moderator_id Модератор по умолчанию (ID пользователя)
 * @property int $default_executor_id Исполнитель по умолчанию (ID пользователя)
 * @property string|null $first_answer_at Время первого ответа
 * @property string|null $last_answer_at Время последнего ответа
 * @property int $sent_answers_count Количество отправленных анкет
 * @property int $opened_answers_count Количество открытых анкет
 * @property int $in_progress_answers_count Количество анкет в процессе
 * @property int $filled_answers_count Количество заполненных анкет
 * @property int $processing_new_answers_count Количество анкет в статусе обработки Новая
 * @property int $processing_inprocess_answers_count Количество анкет в статусе обработки В процессе
 * @property int $processing_work_answers_count Количество анкет в статусе обработки Обрабатывается исполнителем
 * @property int|null $processing_delayed_answers_count Количество анкет в статусе обработки Отложена
 * @property int|null $processing_done_answers_count Количество анкет в статусе обработки Обработана
 * @property int|null $dictionary_id ID справочника для опроса
 * @property int|null $editing_duration Кол-во дней после опроса, в течение которых может быть отредактирована анкета респондентом
 * @property boolean $need_auth Нужна авторизация для прохождения опроса
 * @property boolean $is_answer_limited Получение новых ответов ограничено
 *
 * @property int $avgPoints Среднее количество баллов
 * @property float $avgPercent Средний процент баллов
 * @property string $triggerString Наименование триггера (для автоматического опроса)
 * @property int $css_self_type Тип своих css-стилей
 * @property string|null $css_self_url Url своего css-файла
 * @property string|null $css_self_text Текст своего css
 *
 * @property FoquzPollAnswer[] $foquzAnswer
 * @property FoquzPollDesign $design
 * @property FoquzPollFavorite[] $foquzPollFavorites
 * @property User[] $users
 * @property FoquzQuestion[] $foquzQuestions
 * @property FoquzQuestion[] $foquzQuestionsWithDetails
 * @property FoquzPollPage $startPage
 * @property User $createdBy
 * @property FoquzPollPage $endPage
 * @property FoquzPollPage[] $pages
 * @property FoquzPoll[] $folders
 * @property FoquzPoll $parentFolder
 * @property Company $company
 * @property NotificationScript $notificationScript
 * @property FoquzPollStatsLink $statsLink
 * @property FoquzPollNoOrderAsUsualTriggerSettings $noOrderAsUsualTriggerSetting
 * @property FoquzPollDisplaySetting $displaySetting
 * @property FoquzPollDisplayPage[] $displayPages
 * @property ScoresInterpretationRange[] $scoresInterpretationRanges
 * @property FoquzPollCode[] $pollCodes
 * @property EditorFolder[] $editorFolders
 * @property FoquzPollLang[] $foquzPollLangs
 * @property FoquzComplaint[] $complaints
 * @property RequestSettings2Poll[] $requestSettings2Polls
 * @property MailingFilterSettings $mailingFilterSettings
 * @property-read Channel $channels
 * @property-read User $defaultModerator Модератор по умолчанию
 * @property-read User $defaultExecutor Исполнитель по умолчанию
 * @property-read Dictionary $dictionary Справочник для опроса
 * @property-read DictionaryElement[] $dictionaryElements Справочник для опроса
 * @property-read FoquzFile[] $detailFiles Файлы вариантов ответов
 * @property-read FoquzFile[] $detailSelfVariantFiles Файлы своих вариантов ответов
 * @property-read FoquzFile[] $selfVariantFiles Файлы для своих вариантов
 * @property-read FoquzPollWidget[] $widgets Виджеты опроса
 * @property-read Webhook[] $answerWebhooks Вебхуки опроса
 * @property-read bool $isAnswerLimit Получение новых ответов ограничено
 * @property-read FoquzPollLinkQuotes[] $quotes Квоты ссылок без филиалов для прохождения опроса
 */
class FoquzPoll extends BaseModel
{
    public $favorite;
    const STATUS_NEW = 0;
    const STATUS_ARCHIVE = 10;
    public $createFirstQuestion = false;
    public $withDeletedQuestions = false;

    public $avgPoints;
    public $avgPercent;

    const TRIGGER_ORDER_CONFIRM = 1;
    const TRIGGER_ORDER_DELIVERED = 2;
    const TRIGGER_NO_ORDER = 3;
    const TRIGGER_NO_ORDER_AS_USUAL = 5;
    const TRIGGER_ORDERS = 6;

    const END_QUESTION_AND = 1;
    const END_QUESTION_OR = 2;

    const NOT_PUBLUSHED = 0;
    const IS_PUBLUSHED = 1;

    const ACCESS_TEST_ANSWERS = 10;

    public const WIDGET_DISPLAY_LIMIT_TYPE_OFF = 0;
    public const WIDGET_DISPLAY_LIMIT_TYPE_TWICE = 1;
    public const WIDGET_DISPLAY_LIMIT_TYPE_PERIOD = 2;

    public const CSS_SELF_TYPE_URL = 0;
    public const CSS_SELF_TYPE_TEXT = 1;

    public $countAnswers;
    public $lastAnswerDate;
    public $hasDictionaryLinks = null;
    public $dictionarySelectDisabled = null;

    /**
     * @return bool
     */
    public function isCreateFirstQuestion()
    {
        return $this->createFirstQuestion;
    }

    /**
     * @param bool $createFirstQuestion
     */
    public function setCreateFirstQuestion(bool $createFirstQuestion)
    {
        $this->createFirstQuestion = $createFirstQuestion;
    }

    /**
     * @param bool $withDeletedQuestions
     * @return FoquzPoll
     */
    public function setWithDeletedQuestions(bool $withDeletedQuestions)
    {
        $this->withDeletedQuestions = $withDeletedQuestions;
        return $this;
    }

    /** @inheritdoc */
    public function behaviors()
    {
        return [
            TimestampBehavior::class,
            //BlameableBehavior::class,
            [
                'class' => UploadImageBehavior::class,
                'attribute' => 'image',
                'scenarios' => ['default'],
                'placeholder' => '@app/modules/foquz/public/assets/img/fqz_quest_cover.jpg',
                'path' => '@app/web/' . User::AVATARS_FOLDER . '/{id}',
                'url' => '/' . User::AVATARS_FOLDER . '/{id}',
                'thumbs' => [
                    'thumb' => ['width' => 400, 'quality' => 90],
                    'preview' => ['width' => 438, 'height' => 410],
                    'news_thumb' => ['width' => 200, 'height' => 200, 'bg_color' => '000'],
                ],
            ],
            UtmBehavior::class
        ];
    }

    /**
     * {@inheritdoc}
     */

    public static function tableName()
    {
        return '{{%foquz_poll}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name'], 'required'],
            [['created_at','updated_at','created_by','updated_by','status','company_id','goals_count','limit_count',
                'end_of_question','folder_id','trigger','notification_script_id','max_points','deleted_by',
                'expiration_in_minutes','processing_time_in_minutes','processing_time_by_link_in_minutes',
                'processing_time_for_client_in_minutes','is_active', 'default_moderator_id', 'default_executor_id',
                'dictionary_id', 'editing_duration', 'sent_answers_count', 'opened_answers_count', 'in_progress_answers_count',
                'filled_answers_count', 'processing_new_answers_count', 'processing_inprocess_answers_count',
                'processing_work_answers_count', 'processing_delayed_answers_count', 'processing_done_answers_count',],
                'integer'],
            [['widget_display_ignore_limit'], 'integer', 'min' => 0],
            [['widget_display_limit_type'], 'integer'],
            [['widget_display_limit_type'], 'in', 'range' => [0, 1, 2]],
            [['widget_display_limit_days'], 'integer', 'min' => 0],
            [['widget_create_new_answer_limit'], 'integer', 'min' => 1],
            [[
                'description', 'goal_text', 'send_time', 'title', 'utm_source', 'utm_medium', 'utm_campaign',
                'widget_display_limit_time',
            ], 'string'],
            [[
                'is_tmp', 'is_folder', 'is_qr_code', 'is_auto', 'kiosk_mode', 'point_system', 'dont_send_if_passed',
                'dont_send_if_promocode_used', 'deleted', 'personal_data', 'is_published', 'show_foquz_link',
                'is_short_link', 'mailing_limit', 'is_answer_limited'], 'boolean'],
            [['trigger_days', 'deleted_at'], 'safe'],
            [['name'], 'string', 'max' => 255],
            [['key', 'stop_sending', 'stop_sending_link'], 'string', 'max' => 25],
            [['date_start', 'date_end'], 'date', 'format' => 'php:Y-m-d'],
            [['datetime_start', 'datetime_end'], 'datetime', 'format' => 'php:Y-m-d H:i:s'],
            [['time_to_pass'], 'time', 'format' => 'php:H:i:s'],
            [['time_to_restart', 'time_to_restart_screen_type_end'], 'integer', 'max' => 300],
            [['trigger_orders'], 'integer'],
            [['utm_source', 'utm_medium', 'utm_campaign'], 'filter', 'filter' => 'trim'],
            [['time_to_restart', 'time_to_restart_screen_type_end', 'utm_source', 'utm_medium', 'utm_campaign'], 'default', 'value' => null],
            [['mailing_frequency'], 'integer', 'min' => 0],
            [['dictionary_id'], 'exist', 'skipOnError' => true, 'targetClass' => Dictionary::class, 'targetAttribute' => ['dictionary_id' => 'id']],
            [['dont_send_if_passed_link', 'need_auth'], 'boolean'],
            [['stop_sending_link_time'], 'string', 'max' => 5],
            [['css_self_type'],'integer',],
            ['css_self_type','in', 'range' => [self::CSS_SELF_TYPE_URL, self::CSS_SELF_TYPE_TEXT]],
            [['css_self_url'],'string', 'max' => 255],
            [['css_self_text'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'name' => 'Наименование',
            'description' => 'Описание',
            'company_id' => 'Компания',
            'is_auto' => 'Автоматический',
            'date_start' => 'Продолжительность от',
            'date_end' => 'Продолжительность до',
            'goals_count' => 'Цель (кол-во ответов)',
            'limit_count' => 'Лимит кол-ва ответов',
            'end_of_question' => 'Заканчивать опрос',
            'trigger' => 'Триггер',
            'trigger_time' => '',
            'goal_text' => 'Цель',
            'is_published' => 'Опубликован',
            'time_to_pass' => 'Время на прохождение',
            'datetime_start' => 'Дата старта',
            'datetime_end' => 'Дата окончания',
            'show_foquz_link' => 'Показывать ссылку "Создано в Foquz"',
            'is_short_link' => 'Короткая ссылка',
            'dont_send_if_passed' => 'Не отправлять опрос в приглашениях, если он уже был пройден контактом',
            'dont_send_if_passed_link' => 'Не отправлять опрос по ссылке, если он уже был пройден контактом',
            'stop_sending' => 'Не отправлять повторно опрос контакту в приглашениях',
            'stop_sending_link' => 'Не отправлять повторно опрос контакту по ссылке',
            'default_moderator_id' => 'Модератор по умолчанию',
            'default_executor_id' => 'Исполнитель по умолчанию',
            'dictionary_id' => 'ID справочника для опроса',
            'first_answer_at' => 'Время первого ответа',
            'last_answer_at' => 'Время последнего ответа',
            'sent_answers_count' => 'Количество отправленных анкет',
            'opened_answers_count' => 'Количество открытых анкет',
            'in_progress_answers_count' => 'Количество анкет в процессе',
            'filled_answers_count' => 'Количество заполненных анкет',
            'processing_new_answers_count' => 'Количество анкет в статусе обработки Новая',
            'processing_inprogress_answers_count' => 'Количество анкет в статусе обработки В процессе',
            'processing_work_answers_count' => 'Количество анкет в статусе обработки Обрабатывается исполнителем',
            'processing_delayed_answers_count' => 'Количество анкет в статусе обработки Отложена',
            'processing_done_answers_count' => 'Количество анкет в статусе обработки Обработана',
            'editing_duration' => 'Кол-во дней после опроса, в течение которых может быть отредактирована анкета респондентом',
            'need_auth' => 'Нужна авторизация для прохождения опроса',
            'widget_display_ignore_limit' => 'Игнорировать ограничения показа виджета для одного посетителя сайта и показывать ему виджет, если с момента показа любого виджета прошло N дней',
            'is_answer_limited' => 'Получение новых ответов ограничено',
        ];
    }

    public function fields()
    {
        return ArrayHelper::merge(parent::fields(), [
            'trigger_time' => function($model) {
                return $model->trigger_time ? date('H : i', strtotime($model->trigger_time)) : '';
            },
            'avg_points' => function($model) {
                if ($model->point_system) {
                    if ($model->avgPoints === null) {
                        $this->calcAvgPoints();
                    }
                    return $model->avgPoints;
                }
                return null;
            },
            'avg_percent' => function($model) {
                if ($model->point_system) {
                    if ($model->avgPercent === null) {
                        $this->calcAvgPoints();
                    }
                    $percent = rtrim(rtrim($model->avgPercent, '0'), '.');
                    return $percent ?: 0;
                }
                return null;
            },
            'min_points' => function($model) {
                return $model->point_system ? $model->minPoints : null;
            },
            'countAnswers' => function($model) {
                return $model->in_progress_answers_count + $model->filled_answers_count;
                return (int)$model->getFoquzAnswer()->count();
            },
            'apiAnswers' => function($model) {
                return 1;
                $user = Yii::$app->user->identity;
                $query = FoquzPoll::find()
                    ->leftJoin('foquz_poll_answer', 'foquz_poll_answer.foquz_poll_id = foquz_poll.id')
                    ->leftJoin('api_send_polls', 'api_send_polls.answer_id = foquz_poll_answer.id')
                    ->where(['is not', 'api_send_polls.id', null])
                    ->andWhere(['foquz_poll.id' => $model->id]);

                if($user && $user->isFilialEmployee() && $user->getUserFilials()->count() > 0) {
                    $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
                    $query
                        ->leftJoin('foquz_poll_answer FPAF', 'FPAF.foquz_poll_id = foquz_poll.id')
                        ->leftJoin('orders', 'orders.id = FPAF.order_id')
                        ->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, FPAF.answer_filial_id)', $filials])
                        ->groupBy('foquz_poll.id')
                        ->having(new Expression("COUNT(FPAF.id) > 0"));
                }
                return (int)$query->count();
            },
            'statsLink' => function($model) {
                return $model->statsLink;
            },
            'triggerSetting' => function($model) {
                return $model->trigger == self::TRIGGER_NO_ORDER_AS_USUAL ? $model->noOrderAsUsualTriggerSetting : [];
            },
            'displaySetting' => function($model) {
                return $model->displaySetting;
            },
            'displayPages' => function($model) {
                return $model->displayPages;
            },
            'stopSendingCondition' => function () {
                if ($this->stop_sending) {
                    if ($this->stop_sending === 'double') {
                        return 'double';
                    }
                    return 'period';
                }
                return null;
            },
            'stopSendingPeriod' => function () {
                if ($this->stop_sending && $this->stop_sending !== 'double') {
                    return $this->stop_sending;
                }
                return null;
            },
            'stopSendingLinkCondition' => function () {
                if ($this->stop_sending_link) {
                    if ($this->stop_sending_link === 'double') {
                        return 'double';
                    }
                    return 'period';
                } elseif ($this->stop_sending_link_time) {
                    return 'period';
                }
                return null;
            },
            'stopSendingLinkPeriod' => function () {
                if ($this->stop_sending_link && $this->stop_sending_link !== 'double') {
                    return $this->stop_sending_link;
                }
                return null;
            },
            'stopSendingLinkPeriodTime' => function () {
                return (!empty($this->stop_sending_link_time)) ? $this->stop_sending_link_time : null;
            },
            'widget_display_limit_days' => function() {
                return (!empty($this->widget_display_limit_days)) ? $this->widget_display_limit_days : null;
            },
            'widget_create_new_answer_limit' => function() {
                return $this->widget_create_new_answer_limit;
            },
            'foquzPollLangs' => function () {
                return $this->foquzPollLangs;
            },
            'scoresInterpretationRanges' => function () {
                return $this->scoresInterpretationRanges;
            },
            'hasDictionaryLinks' => function () {
                return $this->hasDictionaryLinks;
            },
            'dictionarySelectDisabled' => function () {
                return $this->dictionarySelectDisabled;
            },
            'dictionary_name' => function () {
                if ($this->dictionary_id) {
                    return Dictionary::findOne($this->dictionary_id)->title;
                }
                return null;
            },
            'dictionary_count' => function () {
                if ($this->dictionary_id) {
                    return Dictionary::findOne($this->dictionary_id)->count;
                }
                return null;
            },
        ]);
    }

    /**
     * Проверяет существование, активность пользователя и принадлежность его к компании опроса
     * @param $attribute
     * @param $params
     * @return void
     */
    public function staffValidator($attribute, $params): void
    {
        if (!User::find()->where(['id' => $this->$attribute, 'status' => 1])->exists() ||
            !CompanyStaff::find()->where(['user_id' => $this->$attribute, 'company_id' => $this->company_id])->exists()) {
            $this->addError($attribute, 'Указанный пользователь не может быть выбран');
        }
    }

    /**
     * перевод для опроса
     * @param PollLang|null $lang
     * @return array|ActiveRecord|null
     */
    public function getTranslate(PollLang $lang = null)
    {
        if ($lang) {
            return FoquzPollLang::find()->where(["foquz_poll_id"=>$this->id, "poll_lang_id"=>$lang->id])->one();
        }
        return FoquzPollLang::find()->where(["foquz_poll_id"=>$this->id, "default"=>1])->one();
    }

    public function getFoquzPollLangs()
    {
        return $this->hasMany(FoquzPollLang::class, ['foquz_poll_id' => 'id'])->orderBy("poll_lang_id");
    }

    /**
     * @return ActiveQuery | MailingFilterSettings
     */
    public function getMailingFilterSettings()
    {
        return $this->hasOne(MailingFilterSettings::class, ['related_id' => 'id'])->andWhere([
            'related_type' => MailingFilterSettings::TYPE_POLL,
            'user_id' => Yii::$app->user->identity->getId()
        ]);
    }

    public function getOrCreatePollKey()
    {
        if (!empty($this->key)) {
            return $this->key;
        }
        $quote = FoquzPollLinkQuotes::find()->where(['poll_id' => $this->id])
            ->orderBy('created_at ASC')->one();
        if (!$quote) {
            $quote = FoquzPollLinkQuotes::createQuote(
                $this->id,
                $this->created_by
            );
        }
        return $quote->key;
    }

    public function getMailings()
    {
        return $this->hasMany(FoquzPollMailingList::class, ['foquz_poll_id' => 'id']);
    }

    public function widgetPollInfo($new = false)
    {
        $firstRatingQuestion = $this->getFirstRatingQuestion($new);

        $result = [
            'filialPollKey' => $this->getOrCreatePollKey(),
            'ratingScale' => null,
        ];
        if($firstRatingQuestion) {
            $npsRatingSetting = $firstRatingQuestion->npsRatingSetting;
            $ratingScale = Yii::$app->controller->renderPartial('@app/mail/templates/foquz-widget-rating.php', [
                'question' => $firstRatingQuestion,
                'questionName' => $firstRatingQuestion->description ? $firstRatingQuestion->description : $firstRatingQuestion->name,
                'questionType' => $firstRatingQuestion->main_question_type,
                'starsCount' => $firstRatingQuestion->starRatingOptions ? $firstRatingQuestion->starRatingOptions->count : null,
                'starsColor' => $firstRatingQuestion->starRatingOptions ? $firstRatingQuestion->starRatingOptions->color : null,
                'starsLabels' => $firstRatingQuestion->starRatingOptions ? json_decode($firstRatingQuestion->starRatingOptions->labels) : null,
                'smilesCount' => count($firstRatingQuestion->questionSmiles),
                'smiles' => $firstRatingQuestion->questionSmiles,
                'smileType' => $firstRatingQuestion->smile_type,
                'fromOne' => $firstRatingQuestion->from_one,
                'pollLink' => '',
                'assessmentType' => 0,
                'npsRating' => $npsRatingSetting ? [
                    'design' => $npsRatingSetting->design,
                    'end_point_color' => $npsRatingSetting->end_point_color,
                    'start_point_color' => $npsRatingSetting->start_point_color,
                    'start_label' => $npsRatingSetting->start_label,
                    'end_label' => $npsRatingSetting->end_label,
                ] : null,
            ]);

            $result['ratingScale'] = $ratingScale;
        }
        return $result;
    }

    public static function getFolderName($f, $name='')
    {
        if (!$f) return $name;
        $name = $name ? $f->name . ' / ' . $name : $f->name;
        return self::getFolderName($f->getParentFolder()->one(), $name);
    }

    public function getScoresInterpretationRanges()
    {
        return $this->hasMany(ScoresInterpretationRange::class, ['foquz_poll_id' => 'id']);
    }

    /**
     * @return ActiveQuery | FoquzQuestion[]
     */
    public function getFoquzQuestions()
    {
        $query = $this->hasMany(FoquzQuestion::class, ['poll_id' => 'id']);
        if (!$this->withDeletedQuestions) {
            $query->where(['is_deleted' => false]);
        }
        return $query->orderBy("is_deleted, position asc");
    }

    /**
     * @return ActiveQuery | FoquzQuestion[]
     */
    public function getFoquzQuestionsWithDetails()
    {
        return $this->getFoquzQuestions()
            ->with(['questionDetails', 'recipientsQuestionDetails']);
    }


    public function getFoquzQuestions_count()
    {
        return FoquzQuestion::find()->where(['poll_id' => $this->id])->count();
    }

    public function getNoOrderAsUsualTriggerSetting()
    {
        return $this->hasOne(FoquzPollNoOrderAsUsualTriggerSettings::className(), ['foquz_poll_id' => 'id']);
    }

    public function getStatsLink()
    {
        return $this->hasOne(FoquzPollStatsLink::className(), ['foquz_poll_id' => 'id']);
    }

    public function getDisplaySetting()
    {
        return $this->hasOne(FoquzPollDisplaySetting::className(), ['poll_id' => 'id']);
    }

    public function getDisplayPages()
    {
        return $this->hasMany(FoquzPollDisplayPage::className(), ['foquz_poll_id' => 'id'])->orderBy(['order' => SORT_ASC]);
    }

    /**
     * @return ActiveQuery
     */
    public function getFoquzAnswer()
    {
        return $this->hasMany(FoquzPollAnswer::class, ['foquz_poll_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getFoquzAnswerFilials()
    {
        return $this->hasMany(Filial::class, ['id' => 'answer_filial_id'])->viaTable('foquz_poll_answer', ['foquz_poll_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getMailingConditions()
    {
        return $this->hasMany(FoquzPollMailingCondition::class, ['foquz_poll_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getChannels()
    {
        return $this->hasMany(Channel::class, ['poll_id' => 'id'])->where(['mailing_id' => null])->orderBy('position');
    }

    /**
     * @return ActiveQuery
     */
    public function getNotificationScript()
    {
        return $this->hasOne(NotificationScript::className(), ['id' => 'notification_script_id'])->where(['deleted' => false]);
    }

    /**
     * @return ActiveQuery
     */
    public function getFavorites()
    {
        return $this->hasMany(FoquzPollFavorite::class, ['foquz_poll_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getFolders()
    {
        return $this->hasMany(FoquzPoll::class, ['folder_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getParentFolder()
    {
        return $this->hasOne(FoquzPoll::class, ['id' => 'folder_id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getDesign()
    {
        return $this->hasOne(FoquzPollDesign::class, ['foquz_poll_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    public function getCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'company_id']);
    }

    public function getComplaints()
    {
        return $this->hasMany(FoquzComplaint::class, ['foquz_poll_id' => 'id']);
    }

    public function getPointSelected()
    {
        return $this->hasMany(FoquzPointSelected::class, ['foquz_poll_id' => 'id']);
    }

    public function getDictionaryElements()
    {
        return $this->hasMany(DictionaryElement::class, ['dictionary_id' => 'dictionary_id'])->onCondition(['type' => 'element']);
    }

    public function getWidgets()
    {
        return $this->hasMany(FoquzPollWidget::class, ['poll_id' => 'id']);
    }

    public function getQuotes(): ActiveQuery
    {
        return $this->hasMany(FoquzPollLinkQuotes::class, ['poll_id' => 'id'])
            ->andWhere(['is not', 'key', null])
            ->orderBy(['created_at' => SORT_DESC]);
    }

    public function getDetailFiles()
    {
        return FoquzFile::find()->where([
            'entity_type' => FoquzFile::TYPE_DETAIL,
            'entity_id' => FoquzQuestionDetail::find()->select('id')->where([
                    'foquz_question_id' => FoquzQuestion::find()->select('id')->where(['poll_id' => $this->id, 'is_deleted' => false]),
                    'is_deleted' => false
            ])
        ])->all();
    }

    public function getDetailSelfVariantFiles()
    {
        return FoquzFile::find()->where([
            'entity_type' => FoquzFile::TYPE_SELF_VARIANT,
            'entity_id' => FoquzQuestionDetail::find()->select('id')->where([
                'foquz_question_id' => FoquzQuestion::find()->select('id')->where(['poll_id' => $this->id, 'is_deleted' => false]),
                'is_deleted' => false
            ])
        ])->all();
    }

    public function getSelfVariantFiles()
    {
        return FoquzFile::find()->where([
            'entity_type' => FoquzFile::TYPE_SELF_VARIANT,
            'entity_id' => FoquzQuestion::find()->select('id')->where(['poll_id' => $this->id, 'is_deleted' => false]),
        ])->all();
    }

        public function getAnswerWebhooks()
    {
        return $this->hasMany(Webhook::class, ['entity_id' => 'id'])->onCondition(['entity_type' => Webhook::TYPE_NEW_ANSWER]);
    }

    /**
     * Gets query for [[FoquzPollCodes]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPollCodes()
    {
        return $this->hasMany(FoquzPollCode::class, ['poll_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getIsFavorite()
    {
        return $this
            ->hasOne(FoquzPollFavorite::class, ['foquz_poll_id' => 'id'])
            ->andWhere(['user_id' => Yii::$app->getUser()->getId()]);
    }

    /**
     * @return ActiveQuery
     */
    public function getDefaultModerator(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'default_moderator_id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getDefaultExecutor(): ActiveQuery
    {
        return $this->hasOne(User::class, ['id' => 'default_executor_id']);
    }

    /**
     * Gets query for [[Dictionary]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDictionary()
    {
        return $this->hasOne(Dictionary::class, ['id' => 'dictionary_id']);
    }

    public function beforeSave($insert)
    {
        if(!is_a(Yii::$app, Application::className()) && Yii::$app->user->identity !== null && $insert) {
            $this->company_id = Yii::$app->user->identity->company->id;
            $this->created_by = Yii::$app->user->id;
            $this->updated_by = Yii::$app->user->id;
        }

        return parent::beforeSave($insert);
    }

    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);

        if ($insert && $this->createFirstQuestion) {
            $question = new FoquzQuestion(['poll_id' => $this->id, 'rating_type' => FoquzQuestion::RATING_TYPE_START]);
            $question->save();
        }

        if (!$insert && isset($changedAttributes['dictionary_id']) && $this->dictionary_id != $changedAttributes['dictionary_id']) {
            FoquzQuestion::updateAll(['dictionary_element_id' => null], ['poll_id' => $this->id]);
            FoquzQuestionDetail::updateAll(
                ['dictionary_element_id' => null],
                ['foquz_question_id' => FoquzQuestion::find()->where(['poll_id' => $this->id])->select('id')]
            );
            $simpleMatrixQuestions = FoquzQuestion::find()
                ->where(['poll_id' => $this->id])
                ->andWhere(['main_question_type' => FoquzQuestion::TYPE_SIMPLE_MATRIX])
                ->all();
            /** @var FoquzQuestion $question */
            foreach ($simpleMatrixQuestions as $question) {
                $matrixSettings = json_decode($question->matrix_settings, false) ?? [];
                if (is_object($matrixSettings)) {
                    $matrixSettings->rows_dictionary = [];
                    $question->matrix_settings = json_encode($matrixSettings, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                    $question->save();
                }
            }
        }

        if (
            !$insert &&
            (
                ($this->deleted && $this->status === self::STATUS_NEW) ||
                $this->status === self::STATUS_ARCHIVE ||
                (isset($changedAttributes['is_active']) && !$this->is_active)
            ) &&
            $widgets = $this->widgets
        ) {
            FoquzPollWidget::changeWidgetsStatus(ArrayHelper::getColumn($widgets, 'id'), 'delete');
        } elseif (
            !$insert &&
            (
                ($this->status === self::STATUS_NEW && isset($changedAttributes['status'])) ||
                ($this->is_active && isset($changedAttributes['is_active']))
            ) &&
            $widgets = $this->widgets
        ) {
            $widgets = array_filter($widgets, static function ($widget) {
                return $widget->is_active;
            });
            FoquzPollWidget::changeWidgetsStatus(ArrayHelper::getColumn($widgets, 'id'), 'restore');
        }
        if ($this->isAnswerLimit !== (bool) $this->is_answer_limited) {
            $this->is_answer_limited = $this->isAnswerLimit;
            $this->save();
        }
        if (isset(Yii::$app->rabbit)) {
            /** @var RabbitMQComponent $rabbit */
            $rabbit = Yii::$app->rabbit;
            $displayLimitTime = 0;
            if ((int)$this->widget_display_limit_type === self::WIDGET_DISPLAY_LIMIT_TYPE_PERIOD) {
                $displayLimitTime = (int)$this->widget_display_limit_days * 24 * 60;
                if (preg_match('/^(\d{2}):(\d{2})$/', $this->widget_display_limit_time, $matches)) {
                    $displayLimitTime += (int)$matches[1] * 60 + (int)$matches[2];
                }
            }
            $rabbit->queue('widget.poll_settings')
                ->type('upsert_settings')
                ->push([
                    'poll_id' => $this->id,
                    'is_answer_limited' => (int) $this->is_answer_limited,
                    'is_published' => (int) $this->is_published,
                    'display_ignore_limit' => $this->widget_display_ignore_limit,
                    'create_new_answer_limit' => $this->widget_create_new_answer_limit,
                    'display_limit_type' => $this->widget_display_limit_type ?? 0,
                    'display_limit_time' => $displayLimitTime,
                    'date_start' => $this->datetime_start ? date('Y-m-d H:i', strtotime($this->datetime_start)) : null,
                    'date_end' => $this->datetime_end ? date('Y-m-d H:i', strtotime($this->datetime_end)) : null,
                ]);
        }
    }

    public function getViewUrl($questions_link=false)
    {
        $question_count = FoquzQuestion::find()->where(['poll_id'=>$this->id])->count();

        if($this->is_auto) {
            if(!$questions_link && !$question_count) {
                return ['/foquz/foquz-poll/launch', 'id' => $this->id];
            } else {
                $foquzQuestions = FoquzQuestion::find()->where(['poll_id'=>$this->id])->andWhere(['is_deleted' => false, 'is_tmp' => false])->all();
                if(count($foquzQuestions) > 0) {
                    return ['/foquz/foquz-question/update', 'id' => $foquzQuestions[0]->id,'pollId' => $this->id];
                } else {
                    return ['/foquz/foquz-poll/launch', 'id' => $this->id, 'tab' => 'points'];
                }

            }
        }

        if($questions_link){
            $foquzQuestions = FoquzQuestion::find()->where(['poll_id'=>$this->id])->andWhere(['is_deleted' => false])->all();
            if(isset($foquzQuestions[0])){
                return ['/foquz/foquz-question/update', 'id' => $foquzQuestions[0]->id,'pollId' => $this->id];
            }
        }

        $firstQuestion = ArrayHelper::getValue($this->foquzQuestions, '0.id');
        $url = ['/foquz/foquz-question/create', 'pollId' => $this->id];

        if ($firstQuestion) {
            $url = ['/foquz/foquz-question/update', 'id' => $firstQuestion,'pollId' => $this->id];
        }

        return $url;
    }

    public function getStartPage()
    {
        return $this->hasOne(FoquzPollPage::class, ['foquz_poll_id' => 'id'])->andWhere([
            'type' => FoquzPollPage::TYPE_START,
        ]);
    }

    public function getEndPage()
    {
        return $this->hasOne(FoquzPollPage::class, ['foquz_poll_id' => 'id'])->andWhere([
            'type' => FoquzPollPage::TYPE_END,
        ]);
    }

    public function getPages()
    {
        return $this->hasMany(FoquzPollPage::class, ['foquz_poll_id' => 'id']);
    }

    public function getPoints()
    {
        return $this->hasMany(FoquzPointItem::class, ['foquz_poll_id' => 'id']);
    }

    public function getPointConditions()
    {
        return $this->hasMany(FoquzPointCondition::class, ['foquz_poll_id' => 'id']);
    }

    private static $results = [
        [
            'id' => 0,
            'level' => 0,
            'text' => 'Главная страница',
        ],
    ];

    public static function getFolderRecursive($id, $level)
    {
        $folders = self::findAll([
            'company_id' => Yii::$app->user->identity->company->id,
            'folder_id' => $id,
            'is_folder' => true,
            'is_tmp' => false,
            'deleted' => false,
        ]);

        if ($id) {
            $level++;
        }

        foreach ($folders as $folder) {
            $inactive = false;
            if (Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher()) {
                $editorFolders = EditorFolder::find()
                    ->andWhere(['user_id' => Yii::$app->user->identity->id])
                    ->select('folder_id')
                    ->column();
                if (!in_array($folder->id, $editorFolders)) {
                    $inactive = true;
                }
            }
            self::$results[] = [
                'id' => $folder->id,
                'text' => $folder->name,
                'level' => $level,
                'inactive' => $inactive,
                'description' => $folder->description,
            ];

            if (!empty($folder->folders)) {
                self::getFolderRecursive($folder->id, $level);
            }
        }

        return self::$results;
    }

    public static $resultList = [];

    public static function getFolderListRecursive($id, $childInactive = true)
    {
        /** @var FoquzPoll[] $folders */
        $folders = self::find()
            ->where([
                'is_folder' => true,
                'folder_id' => $id,
                'is_tmp' => false,
                'deleted' => false,
                'company_id' => Yii::$app->user->identity->company->id
            ])
            ->all();

        if ($id) {
            $data = [];
            foreach($folders as $folder) {
                if ($childInactive) {
                    $inactive = false;
                    if (Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher()) {
                        $editorFolders = EditorFolder::find()
                            ->andWhere(['user_id' => Yii::$app->user->identity->id])
                            ->select('folder_id')
                            ->column();

                        if (!in_array($folder->id, $editorFolders)) {
                            $inactive = true;
                        }
                    }
                } else {
                    $inactive = false;
                }
                $data[] = [
                    'id' => $folder->id,
                    'name' => $folder->name,
                    'description' => $folder->description,
                    'content' => self::getFolderListRecursive($folder->id, $inactive),
                    'inactive' => $inactive,
                ];
            }
            return $data;
        }

        foreach ($folders as $folder) {
            $inactive = false;
            if (Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher()) {
                $editorFolders = EditorFolder::find()
                    ->andWhere(['user_id' => Yii::$app->user->identity->id])
                    ->select('folder_id')
                    ->column();
                if (!in_array($folder->id, $editorFolders)) {
                    $inactive = true;
                }
            }
            self::$resultList[] = [
                'id' => $folder->id,
                'name' => $folder->name,
                'description' => $folder->description,
                'content' => self::getFolderListRecursive($folder->id),
                'inactive' => $inactive,
            ];
        }

        return self::$resultList;
    }

    public static function getPollFoldersList($model_id)
    {
        $model = self::findOne(['id' => $model_id]);
        $bread=[];
        $row = self::findOne(['id' => $model->folder_id]);
        while($row){
            $html = "<a class='breadcrumbs__item' href='/foquz/foquz-poll/index?id={$row->id}'>{$row->name}</a>";
            array_unshift($bread,$html);
            if($row->folder_id)
                $row = self::findOne(['id' => $row->folder_id]);
            else
                $row = false;
        }
        return implode('',$bread);


    }

    public function getEditorFolders()
    {
        $this->hasMany(EditorFolder::class, ['folder_id' => 'id']);
    }

    /**
     * @param int $userId
     * @return array
     */
    public static function getEditorFolderIds($userId = null)
    {
        $user = $userId ?: Yii::$app->user->identity->id;
        $editorFolders = EditorFolder::find()->andWhere(['user_id' => $user])->all();

        $parentFolderIds = $folderIds = $recursiveFolderIds = [];
        self::$idList = [];
        foreach ($editorFolders as $editorFolder) {
            $folderIds[] = $editorFolder->folder_id;
            if ($parentFolder = $editorFolder->folder->folder_id) {
                $parentFolderIds[] = $parentFolder;
            }
            $recursiveFolderIds = ArrayHelper::merge(
                $recursiveFolderIds,
                self::getIdsFromFolderListRecursive(FoquzPoll::getFolderListRecursive($editorFolder->folder_id))
            );
        }
        $parentFolders = FoquzPoll::find()->select('id')->where(['folder_id' => $parentFolderIds, 'is_folder' => true])->column();

        foreach ($recursiveFolderIds as $key => $folderId) {
            if (in_array($folderId, $folderIds)) {
                unset($recursiveFolderIds[$key]);
            }
        }

        $all = array_values(array_unique(ArrayHelper::merge($parentFolderIds, $folderIds)));
        //$all = array_values(array_unique(ArrayHelper::merge($all, $recursiveFolderIds)));

        return [
            'all' => $all,
            'exclude' => ArrayHelper::merge(array_diff($parentFolders, $folderIds), $recursiveFolderIds),
        ];
    }

    /**
     * @param int|null $userID
     * @param int|null $companyID
     * @return array
     */
    public static function getEditorFolderIdsAll(int $userID = null, int $companyID = null)
    {
        if (!$userID) {
            $userID = Yii::$app->user->identity->id;
        }
        $editorFolders = EditorFolder::find()->andWhere(['user_id' => $userID])->all();

        $ids = []; $parentIds = [];
        foreach ($editorFolders as $editorFolder) {
            $ids[] = $editorFolder->folder->id;
            $ids = $editorFolder->folder->getChildsFolders($ids, $companyID);
            $parentIds = $editorFolder->folder->getParentFolders($parentIds, $companyID);
        }
        $ids = array_unique($ids);
        $parentIds = array_unique($parentIds);
        return ['folders'=>$ids, 'parents'=>$parentIds];
    }

    private function getChildsFolders($ids=[], int $companyID = null)
    {
        $folders = self::find()
        ->where([
            'is_folder' => true,
            'folder_id' => $this->id,
            'is_tmp' => false,
            'deleted' => false,
            'company_id' => $companyID ?: Yii::$app->user->identity->company->id,
        ])
        ->all();

        foreach ($folders as $folder) {
            $ids[] = $folder->id;
            $ids = $folder->getChildsFolders($ids, $companyID);
        }
        return $ids;
    }

    private function getParentFolders($ids=[], int $companyID = null)
    {
        if ($this->folder_id) {
            $folder = self::find()
            ->where([
                'is_folder' => true,
                'id' => $this->folder_id,
                'is_tmp' => false,
                'deleted' => false,
                'company_id' => $companyID ?: Yii::$app->user->identity->company->id,
            ])
            ->one();
            if ($folder) {
                $ids[] = $folder->id;
                $ids = $folder->getParentFolders($ids, $companyID);
            }
        }

        return $ids;

    }


    private static $idList = [];
    private static function getIdsFromFolderListRecursive($list)
    {
        foreach ($list as $item) {
            self::$idList[] = $item['id'];
            if ($item['content']) {
                self::getIdsFromFolderListRecursive($item['content']);
            }
        }

        return self::$idList;
    }

    public static function getPollFoldersListArray($id)
    {
        $model = self::findOne(['id' => $id]);
        $bread=[];
        $row = self::findOne(['id' => $model->folder_id]);
        while($row){
            array_unshift($bread,
                [
                    'name' => $row->name,
                    'link' => "/foquz/foquz-poll/index?id={$row->id}"
                ]
            );
            if($row->folder_id)
                $row = self::findOne(['id' => $row->folder_id]);
            else
                $row = false;
        }
        return json_encode($bread);
    }

    public function hasLogic()
    {
        return FoquzPollQuestionsLogic::find()
            ->where(['in', 'question_id', ArrayHelper::getColumn($this->foquzQuestions, 'id')])
            ->exists() || FoquzPollQuestionViewLogic::find()
                ->where(['in', 'question_id', ArrayHelper::getColumn($this->foquzQuestions, 'id')])
                ->exists();
    }

    public static function getPollData(self $model)
    {
        $foldersArray = self::getPollFoldersListArray($model->id);

        $questionsCount = $model->foquzQuestions ? count($model->foquzQuestions) : 0;
        $pagesCount = $model->displayPages ? count($model->displayPages) + FoquzQuestion::find()
            ->where(['poll_id' => $model->id])
            ->andWhere(['main_question_type' => FoquzQuestion::TYPE_INTERMEDIATE_BLOCK])
                ->joinWith('intermediateBlock')
                ->andWhere(['in', 'foquz_question_intermediate_block_setting.screen_type', [
                    FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_START,
                    FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_END
                ]])
                ->count() : $questionsCount;

        $passed = $model->in_progress_answers_count + $model->filled_answers_count; /* (int)FoquzPollAnswer::find()
            ->where(['foquz_poll_id' => $model->id])
            ->andWhere(['in', 'status', ['done', 'in-progress']])
            ->count();*/

        $testAnswersLimit = self::ACCESS_TEST_ANSWERS;
        $publishedAnswersLimit = $model->limit_count;

        $testCompleted = false;
        $publishedCompleted = false;

        if (!$model->is_published) {
            $testCompleted = $passed >= $testAnswersLimit;
        } else {
            $publishedCompleted = $publishedAnswersLimit && $passed >= $publishedAnswersLimit;
        }

        $result = [
            'id' =>  $model->id,
            'folders' => $foldersArray,
            'name' => $model->name,
            'isActive' => $model->is_active,
            'isAuto' => $model->is_auto,
            'isNew' => $model->is_tmp,
            'isPublished' => $model->is_published,
            'datetime_start' => DateTimeHelper::addGMT3($model->datetime_start),
            'datetime_end' => DateTimeHelper::addGMT3($model->datetime_end),
            'pagesCount' => $pagesCount,
            'questionsCount' => $questionsCount,
            'timeToPass' => $model->time_to_pass,
            'showFoquzLink' => $model->show_foquz_link,
            'createdAt' => $model->created_at,
            'passed' => $passed,
            'testAnswersLimit' => $testAnswersLimit,
            'testCompleted' => $testCompleted,
            'publishedAnswersLimit' => $publishedAnswersLimit,
            'publishedCompleted' => $publishedCompleted,
        ];

        return json_encode($result);
    }

    public static $resultBread = [];

    public static function createPath($id, $reset = false)
    {
        if ($reset) {
            self::$resultBread = [];
        }

        $row = self::findOne(['id' => $id]);

        if (!$row->folder_id) {
            self::$resultBread[] = [
                'id' => $row->id,
                'label' => $row->name,
                'is_parent' => true,
            ];
        } else {
            self::$resultBread[] = [
                'id' => $row->id,
                'label' => $row->name,
                'is_parent' => false,
            ];
            return self::createPath($row->folder_id);
        }

        return self::$resultBread;
    }

    public static function getPathForSelect2($id)
    {
        $results = self::createPath($id, true);

        $outPutResult = [];

        krsort($results);

        $level = 0;

        foreach ($results as $result) {
            $outPutResult[] = [
                'id' => $result['id'],
                'type' => 0,
                'name' => $result['label'],
                'category' => 'folder',
                'level' => $level++,
            ];
        }

        return $outPutResult;
    }

    public static $folderPollCount = 0;
    public static $folderChildIds = [];

    public static function getRecursiveCount($id, $filials=null, $allFilials=null, $folders=null)
    {
        $folderModel = FoquzPoll::findOne($id);

        $query = FoquzPoll::find()->where([
            'folder_id' => $id,
            'is_folder' => false,
            'foquz_poll.status' => FoquzPoll::STATUS_NEW,
            'is_tmp' => 0,
            'deleted' => 0
        ]);
        //if (Yii::$app->user->identity->isEditor()) {
          //  $query->andWhere(['folder_id' => EditorFolder::find()->select('folder_id')->where(['user_id' => Yii::$app->user->identity->id])->column()]);
        //}
        if ($folders) {
            $query->andWhere(['folder_id' => $folders]);
        }

        if ($filials!=null && count($filials)>0) {
            $query
                ->leftJoin('foquz_poll_answer FPAF', 'FPAF.foquz_poll_id = foquz_poll.id')
                ->leftJoin('orders', 'orders.id = FPAF.order_id')
                ->groupBy('foquz_poll.id')
                ->having(new Expression("COUNT(FPAF.id) > 0"));
            if (!$allFilials) {
                $query->andWhere( ['in', 'IF(foquz_poll.is_auto, orders.filial_id, FPAF.answer_filial_id)', $filials]);
            }

        }
        self::$folderPollCount += $query->count();

        foreach ($folderModel->folders as $folder) {
            $folderModel->getRecursiveCount($folder->id, $filials, $allFilials);
        }

        return self::$folderPollCount;
    }

    public function getRequestSettings2Polls()
    {
        return $this->hasMany(RequestSettings2Poll::class, ['foquz_poll_id' => 'id']);
    }

    public static function getChildIds($id)
    {
        $results = self::getTreeIds($id);
        return array_map(function ($item) {
            return $item['id'];
        }, $results);
    }
    public static function getChildPollIds(int $id)
    {
        $ids = [];
        $results = self::getTreeIds($id);
        foreach ($results as $result) {
            if ($result['is_folder'] === 0) {
                $ids[] = $result['id'];
            }
        }
        return $ids;
    }

    private static function getTreeIds(int $id)
    {
        $sql = "WITH RECURSIVE category_tree AS (
            SELECT id, folder_id, is_folder
            FROM foquz_poll
            WHERE id = :id AND status != :status AND deleted = 0 AND is_tmp != 1
            UNION ALL
            
            SELECT c.id, c.folder_id, c.is_folder
            FROM foquz_poll c
            JOIN category_tree ct ON c.folder_id = ct.id 
            WHERE c.status != :status AND c.deleted = 0 AND c.is_tmp != 1
        )
        SELECT * FROM category_tree WHERE id != :id;";

        return Yii::$app->db->createCommand($sql)
            ->bindValues([':id' => $id, ':status' => FoquzPoll::STATUS_ARCHIVE])
            ->queryAll();
    }


    public static function prepareText($text,$fio,$number,$date, $code){
        $text = str_replace('{ФИО}',mb_convert_case(mb_strtolower($fio), MB_CASE_TITLE),$text);
        $text = str_replace('{№}',$number,$text);
        $date = date("d.m.Y H:i", strtotime($date));
        $text = str_replace('{Дата}',$date,$text);

        if (is_string($code)) {
            $text = str_replace('{Промокод}', $code ? $code : "", $text);
        }
        return $text;
    }

    public static function goalsCount()
    {
        $user = Yii::$app->user->identity;
        if (!$user) {
            return 0;
        }
        $query = self::find()
            ->leftJoin('foquz_poll_answer', 'foquz_poll_answer.foquz_poll_id = foquz_poll.id')
            ->where([
                'foquz_poll.is_tmp' => false,
                'foquz_poll.status' => self::STATUS_NEW,
                'foquz_poll.company_id' => Yii::$app->user->identity->company->id,
                'foquz_poll.deleted' => false,
                'foquz_poll_answer.status' => [
                    FoquzPollAnswer::STATUS_IN_PROGRESS,
                    FoquzPollAnswer::STATUS_DONE
                ]
            ])
            ->andWhere(['not', ['goals_count' => null]])
            ->groupBy('foquz_poll.id')
            ->having(new Expression("COUNT(foquz_poll_answer.id) >= foquz_poll.goals_count"));

        if ($user->isFilialEmployee() && $user->getUserFilials()->count() > 0) {
            $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
            $query->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id');
            $query->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)', $filials]);
        }
        if ($user->isEditor() || $user->isWatcher()) {
            $editorFolders = EditorFolder::find()
                ->andWhere(['user_id' => $user->id])
                ->select('folder_id')
                ->column();
            $query->andWhere(['foquz_poll.folder_id' => $editorFolders]);
        }

        return $query->count();
        //TODO: 21.09.2023 метод оптимизирован, убрать старый код, если все норм
        $count = 0;
        $manualPollsWithGoalsQuery = self::find()->where([
            'is_tmp' => false,
            'foquz_poll.status' => self::STATUS_NEW,
            'foquz_poll.company_id' => Yii::$app->user->identity->company->id,
            'foquz_poll.deleted' => false,

        ])->andWhere(['not', ['goals_count' => null]])->asArray();

        $user = Yii::$app->user->identity;

        if($user && $user->isFilialEmployee() && $user->getUserFilials()->count() > 0) {
            $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
            $manualPollsWithGoalsQuery
                ->leftJoin('foquz_poll_answer', 'foquz_poll_answer.foquz_poll_id = foquz_poll.id')
                ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
                ->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)', $filials])
                ->groupBy('foquz_poll.id')
                ->having(new Expression("COUNT(foquz_poll_answer.id) > 0"));
        }
        if ($user->isEditor()) {
            $editorFolders = EditorFolder::find()
                ->andWhere(['user_id' => $user->id])
                ->select('folder_id')
                ->column();
            $manualPollsWithGoalsQuery->andWhere(['foquz_poll.folder_id' => $editorFolders]);
        }

        foreach($manualPollsWithGoalsQuery->all() as $manualPollWithGoal) {

            $answersCount = FoquzPollAnswer::find()->where([
                'status' => [
                    FoquzPollAnswer::STATUS_IN_PROGRESS,
                    FoquzPollAnswer::STATUS_DONE
                ],
                'foquz_poll_id' => $manualPollWithGoal['id'],
            ])->count();
            if($answersCount >= $manualPollWithGoal['goals_count']) $count++;
        }
        return $count;
    }

    public function getTakeGoalAttribute()
    {
        if($this->goals_count > 0) {
            return $this->getAnswers()->where(['in', 'status', [FoquzPollAnswer::STATUS_DONE, FoquzPollAnswer::STATUS_IN_PROGRESS]])->count() >= $this->goals_count;
        }
        return false;
    }

    public function fillAnswers($count, $full = null, $part = null, $filialId = null)
    {
        if($full === null && $part === null) {
            $totallyFilled = round($count * 0.7);
        } else {
            $totallyFilled = round($count * $full/100);
        }

        $semiFilled = $count - $totallyFilled;

        for($i = 0; $i < $totallyFilled; $i++) {
            $this->createAnswer(false, $filialId);
        }
        for($j = 0; $j < $semiFilled; $j++) {
            $this->createAnswer(count($this->foquzQuestions) >= 2, $filialId);
        }
    }

    public function getFirstRatingQuestion($new = false)
    {
        $firstRatingQuestion = null;

        if ($new) {
            $questionTypes = [
                FoquzQuestion::TYPE_ASSESSMENT,
                FoquzQuestion::TYPE_STAR_RATING,
                FoquzQuestion::TYPE_SMILE_RATING,
                FoquzQuestion::TYPE_NPS_RATING,
                FoquzQuestion::TYPE_RATING,
            ];
        } else {
            $questionTypes = [
                FoquzQuestion::TYPE_ASSESSMENT,
                FoquzQuestion::TYPE_STAR_RATING,
                FoquzQuestion::TYPE_SMILE_RATING,
                FoquzQuestion::TYPE_NPS_RATING,
            ];
        }

        foreach($this->foquzQuestions as $question) {
            if($question->is_deleted) {
                continue;
            }

            if(in_array($question->main_question_type, $questionTypes)) {
                if($question->main_question_type == FoquzQuestion::TYPE_ASSESSMENT and $question->rating_type != 1) {
                    break;
                }

                $firstRatingQuestion = $question;
                break;
            }
            elseif($question->main_question_type == FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
                continue;
            }
            else {
                break;
            }
        }
        return $firstRatingQuestion;
    }

    /** генерация ответов */
    public function createAnswer($random = false, $filialId = null)
    {
        $answerModel = new FoquzPollAnswer();
        $answerModel->auth_key = md5(time() . uniqid());
        $answerModel->foquz_poll_id = $this->id;
        $answerModel->status = $random ? FoquzPollAnswer::STATUS_IN_PROGRESS : FoquzPollAnswer::STATUS_DONE;
        if($this->is_auto) {
            $client = Client::find()->where(['company_id' =>  Yii::$app->user->identity->company->id])->orderBy('RAND()')->one();
            $answerModel->client_id = $client->id;
            $where = $client->clientEmail ? 'client_id = '.$client->id.' OR email = "'.$client->clientEmail->email.'"' : 'client_id ='.$client->id.' OR email = "'.$client->id.'@foquz.ru"';
            $foquzContact = FoquzContact::find()
                ->where(['company_id' => Yii::$app->user->identity->company->id])
                ->andWhere($where)
                ->one();
            if(!$foquzContact) {
                $foquzContact = new FoquzContact(['company_id' => Yii::$app->user->identity->company->id]);
                $foquzContact->first_name = $client->name;
                $foquzContact->email = $client->clientEmail ? $client->clientEmail->email : $client->id.'@foquz.ru';
            }
            $foquzContact->client_id = $client->id;
            if(!$foquzContact->save()) {
                print_r($client->id);
                echo '<br>';
                print_r($foquzContact->email);
                echo '<br>';
                print_r($foquzContact->errors);
                die();
            }
            $foquzContact->refresh();
            $answerModel->contact_id = $foquzContact->id;
            $answerModel->save();
            $dishesCount = rand(1, 5);
            $dishes = Dish::find()
                ->where(['company_id' => Yii::$app->user->identity->company->id])
                ->orderBy('RAND()')
                ->limit($dishesCount)
                ->all();
            $orderId = FoquzPollDishScore::generateOrder($answerModel->id, $dishes, $filialId);
            $answerModel->order_id = $orderId;
        } else {
            $answerModel->order_id = 0;
            $answerModel->delivery_id = 0;
            $answerModel->order_sum = 0;
        }
        $answerModel->answer_filial_id = $filialId;
        $answerModel->save();
        $answerModel->refresh();
        $answerModel->addProcessingIfNeeded();

        $listSend = new FoquzPollMailingListSend([
            'answer_id' => $answerModel->id,
            'status' => FoquzPollMailingListSend::STATUS_OPEN,
            'key' => md5(time() . uniqid()),
            'sended' => date('Y-m-d H:i:s'),
            'channel_name' => 'Тест',
        ]);
        $listSend->save();
        $answerItems = [];
        /** @var FoquzQuestion $question */
        foreach($this->getFoquzQuestions()->where(['is_tmp' => false])->all() as $question) {
            if($random) {
                $number = rand(1, 3);
                if($number === 1) {
                    continue;
                }
            }
            if($question->point && count($question->point->foquzPointTypeRelations) > 0 && $answerModel->order) {
                $conditions = ArrayHelper::getColumn($question->point->foquzPointTypeRelations, 'foquz_order_type_id');
                if(!in_array($answerModel->order->delivery_type, $conditions)) {
                    continue;
                }
            }

            $answer = null;
            $rating = null;
            $detail_item = null;
            $is_self_variant = false;
            $skipped = $question->skip && rand(1, 5) === 3 ? 1 : 0;

            if (!$skipped) {
                if ($question->main_question_type === FoquzQuestion::TYPE_ASSESSMENT) {
                    if ($question->rating_type === FoquzQuestion::RATING_TYPE_START) {
                        $rating = rand(1, 5);
                        if ($rating < 5 || $question->for_all_rates) {
                            if ($question->detail_question !== '' && $question->detail_question !== null) {
                                if ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO && $question->is_self_answer) {
                                    if (rand(1, 3) === 1) {
                                        $is_self_variant = true;
                                    } else {
                                        $detail_item = $question->generateVariants();
                                    }
                                } else {
                                    $detail_item = $question->generateVariants();
                                }
                            }
                            if ($question->is_self_answer && $question->variants_element_type !== FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO) {
                                if (rand(1, 2) === 2) {
                                    $is_self_variant = true;
                                }
                            }
                        }
                        if ($question->comment_enabled) {
                            if ($question->comment_required || rand(1, 2) === 2) {
                                $answer = $question->randomTextAnswers(true);
                            }
                        }
                    } elseif ($question->rating_type === FoquzQuestion::RATING_OPTIONS) {
                        $detail_item = $question->generateVariants();
                        if ($question->is_self_answer) {
                            $is_self_variant = true;
                        }
                    } else {
                        $dishScore = 0;
                        foreach ($answerModel->foquzPollDishes as $dish) {
                            $rate = rand(1, 5);
                            $dishScore += $rate;
                            $dish->score = $rate;
                            $dish->save();
                        }
                        $rating = number_format($dishScore / $dishesCount);
                        if ($question->is_self_answer) {
                            if (rand(1, 3) !== 3) {
                                $answer = $question->randomTextAnswers(true);
                            }
                        }
                    }
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                    $detail_item = $question->generateVariants();
                    if ($question->is_self_answer) {
                        if (rand(1, 2) === 2) {
                            $is_self_variant = true;
                        }
                    }
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_FILIAL) {
                    $selectedVariants = $question->generateFilialVariants();
                    $detail_item = $selectedVariants;

                    $answerModel->answer_filial_id = $selectedVariants[0];
                    $answerModel->save();

                } elseif ($question->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                    $selectedVariants = [];
                    $detailQuestion = json_decode($question->detail_question);
                    shuffle($detailQuestion);
                    if ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO) {
                        $selectedVariants[] = $detailQuestion[0];
                    } else {
                        $count = count($detailQuestion);
                        $needAnswers = random_int(1, $count);
                        for($i = 0; $i < $needAnswers; $i++) {
                            $selectedVariants[] = $detailQuestion[$i];
                        }
                    }
                    $detail_item = $selectedVariants;
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_TEXT_ANSWER) {
                    if ($question->mask === 5) {
                        $answer = json_encode($question->generateFakeTextAnswer($question->mask), JSON_UNESCAPED_UNICODE);
                    } else {
                        $answer = $question->generateFakeTextAnswer($question->mask);
                    }
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_DATE) {
                    list($date, $time) = $question->generateFakeDateTime();
                    $answer = json_encode([
                        'date' => $date,
                        'time' => $time
                    ]);
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_ADDRESS) {
                    $answer = $question->randomAddressAnswers(true);
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_FILE_UPLOAD) {
                    $answerItemModel = new FoquzPollAnswerItem();
                    $answerItemModel->foquz_poll_answer_id = $answerModel->id;
                    $answerItemModel->foquz_question_id = $question->id;
                    $answerItemModel->question_name = $question->service_name;
                    $filesCount = rand(1, $question->files_length);
                    $files = [];
                    $fileTypes = $question->file_types;
                    if (is_string($fileTypes)) {
                        $fileTypes = json_decode($question->file_types, true);
                    }
                    if (
                        empty($fileTypes) ||
                        (in_array('image', $fileTypes, true) && in_array('video', $fileTypes, true))
                    ) {
                        for ($i = 0; $i < $filesCount; $i++) {
                            $array = array_merge($question->randomImages(), $question->randomVideos());
                            $files[] = $array[array_rand($array)];
                        }
                    } elseif (in_array('image', $fileTypes, true)) {
                        for ($i = 0; $i < $filesCount; $i++) {
                            $files[] = $question->randomImages(true);
                        }
                    } elseif (in_array('video', $fileTypes, true)) {
                        for ($i = 0; $i < $filesCount; $i++) {
                            $files[] = $question->randomVideos(true);
                        }
                    }
                    if ($question->is_self_answer) {
                        if (rand(1, 2) === 2) {
                            $answerItemModel->answer = $question->randomTextAnswers(true);
                        }
                    }
                    $answerItemModel->save();
                    foreach ($files as $file) {
                        $basePath = "random-files";
                        $fullPath = Yii::getAlias("@app/web/{$basePath}");
                        $fileAbsName = '/' . $basePath . '/' . $file;
                        $fileModel = new FoquzPollAnswerItemFile([
                            'foquz_poll_answer_item_id' => $answerItemModel->id,
                            'file_path' => $fileAbsName,
                            'file_full_path' => $fullPath . '/' . $file,
                        ]);
                        if (stristr($file, '.mp4')) {
                            /*$ffmpeg = FFMpeg::create([
                                'ffmpeg.binaries' => Yii::$app->params['ffmpeg_binaries'],
                                'ffprobe.binaries' => Yii::$app->params['ffprobe_binaries']
                            ]);
                            $video = $ffmpeg->open($fullPath . '/' . $file);
                            $video
                                ->frame(TimeCode::fromSeconds(5))
                                ->save($fullPath . '/' . $file . '.jpg');*/
                            $fileModel->image_link = '/' . $basePath . '/' . $file . '.jpg';
                        }
                        $fileModel->save();
                    }
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_FORM) {
                    $answers = [];
                    foreach ($question->formFields as $formField) {
                        $answers[$formField->id] = $question->generateFakeTextAnswer($formField->mask_type);
                    }
                    $answer = json_encode($answers, JSON_UNESCAPED_UNICODE);
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_PRIORITY) {
                    $details = [];
                    if ($question->donor) {
                        foreach ($question->recipientQuestionDetails as $detail) {
                            $details[] = $detail->question_detail_id ?: -1;
                        }
                    } else {
                        foreach ($question->questionDetails as $detail) {
                            $details[] = $detail->id;
                        }
                    }
                    shuffle($details);
                    $detail_item = $details;
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_CHOOSE_MEDIA) {
                    $questionMedia = $question->grabMediaContent();
                    if ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO) {
                        $answer = json_encode([(string)$questionMedia[array_rand($questionMedia)]['id']]);
                    } else {
                        $answers = [];
                        $countAnswers = rand(1, count($questionMedia));
                        for ($i = 0; $i < $countAnswers; $i++) {
                            $randomId = (string)$questionMedia[array_rand($questionMedia)]['id'];
                            if (!in_array($randomId, $answers)) {
                                $answers[] = $randomId;
                            }
                        }
                        $answer = json_encode($answers);
                    }
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_GALLERY_RATING) {
                    foreach ($question->grabMediaContent() as $media) {
                        $answer[$media['id']] = rand(1, 5);
                    }
                    $answer = json_encode($answer);
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_SMILE_RATING) {
                    $answer = (string)$question->getQuestionSmiles()->orderBy(new Expression('RAND()'))->one()->id;
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR) {
                    $answer = $question->generateNewVariants();

                    // уточняющий вопрос
                    $settings = $question->starRatingOptions;
                    $extraVariants = FoquzQuestionDetail::find()
                        ->where(['foquz_question_id' => $question->id, 'extra_question' => 1])
                        ->andWhere(['<>','is_deleted', 1])
                        ->all();
                    if ($extraVariants) {
                        foreach ($answer as $detailId => $value) {
                            $detail = FoquzQuestionDetail::findOne($detailId);
                            if ($detail && $detail->need_extra) {
                                if ($settings->extra_question_rate_from || $settings->extra_question_rate_to) {
                                    if (
                                        ($settings->extra_question_rate_from && $value < $settings->extra_question_rate_from) ||
                                        ($settings->extra_question_rate_to && $value > $settings->extra_question_rate_to)
                                    ) {
                                        continue;
                                    }
                                }
                                $answer['extra'][$detailId][] = array_rand($extraVariants);
                            }
                        }
                    }

                    $answer = json_encode($answer, JSON_UNESCAPED_UNICODE);
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_SCALE) {
                    $answer = [];
                    $settings = $question->scaleRatingSetting;
                    $stepCount = ($settings->end - $settings->start) / $settings->step;
                    $values = [null, $settings->start];
                    for ($i = 1; $i <= $stepCount; $i++) {
                        $values[] = $settings->step * $i;
                    }
                    $values[] = $settings->end;
                    foreach ($question->questionDetails as $detail) {
                        $answer[$detail->id] = $values[array_rand($values)];
                    }
                    $answer = json_encode($answer, JSON_UNESCAPED_UNICODE);
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_NPS_RATING) {
                    $rating = $question->genNpsRating();

                    if ($question->isNpsWithVariants()){
                        $rating = null;
                        $answer = json_encode($question->generateNewVariants(), JSON_UNESCAPED_UNICODE);
                    }
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
                    $settings = json_decode($question->matrix_settings);
                    if ($question->skip_variant) {
                        $settings->cols[] = null;
                    }
                    $answer = [];
                    if ($question->donor) {
                        foreach ($settings->donorRows as $row) {
                            if ($row === 'custom') {
                                $answer[-1] = $settings->cols[array_rand($settings->cols)];
                            } else {
                                $answer[$row] = $settings->cols[array_rand($settings->cols)];
                            }
                        }
                    }

                    $rows = $settings->rows;
                    shuffle($rows);
                    $variants = [];
                    foreach ($rows as $index => $row) {
                        if (isset($settings->minRowsReq) && $settings->minRowsReq <= $index) {
                            $variants[$row] = random_int(0, 1) ? $settings->cols[array_rand($settings->cols)] : null;
                        } else {
                            $variants[$row] = $settings->cols[array_rand($settings->cols)];
                        }
                    }
                    foreach ($settings->rows as $row) {
                        $answer[$row] = $variants[$row];
                    }

                    // уточняющий вопрос
                    $detail_item = [];
                    if (isset($settings->extra_question)) {
                        $variants = $question->questionDetails;
                        foreach ($answer as $row => $col) {
                            if (isset($settings->extra_question->$row) && in_array($col, $settings->extra_question->$row)) {
                                $detail_item[$row][] = array_rand(ArrayHelper::getColumn($variants, 'id'));
                            }
                        }
                    }

                    $answer = json_encode($answer, JSON_UNESCAPED_UNICODE);
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_SEM_DIFFERENTIAL) {
                    $answer = [];
                    foreach ($question->differentialRows as $row) {
                        $answer[$row->id] = rand(1, 5);
                    }
                    $answer = json_encode($answer);
                } elseif (in_array($question->main_question_type, [FoquzQuestion::TYPE_STAR_RATING, FoquzQuestion::TYPE_RATING])) {
                    $rating = rand(1, $question->starRatingOptions->count);

                    if ($rating < $question->starRatingOptions->count || $question->for_all_rates) {
                        if ($question->detail_question !== '' && $question->detail_question !== null) {
                            if ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT) {
                                $is_self_variant = true;
                            } elseif ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO && $question->is_self_answer) {
                                if (rand(1, 3) === 1) {
                                    $is_self_variant = true;
                                } else {
                                    $detail_item = $question->generateVariants();
                                }
                            } else {
                                $detail_item = $question->generateVariants();
                            }
                        }
                        if ($question->is_self_answer && $question->variants_element_type !== FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO) {
                            if (rand(1, 2) === 2) {
                                $is_self_variant = true;
                            }
                        }
                    }
                    if ($question->comment_enabled) {
                        if (rand(1, 2) === 2) {
                            $answer = $question->randomTextAnswers(true);
                        }
                    }
                }
            }
            if($question->main_question_type !== FoquzQuestion::TYPE_FILE_UPLOAD) {
                $answerItems[] = [
                    date('Y-m-d H:i:s'),
                    $answerModel->id,
                    $question->id,
                    $answer,
                    $rating,
                    $detail_item,
                    $is_self_variant,
                    $question->genComment(),
                    $question->service_name,
                    $skipped,
                ];
            }
        }
        Yii::$app->db->createCommand()->batchInsert('foquz_poll_answer_item',
            ['created_at', 'foquz_poll_answer_id', 'foquz_question_id', 'answer', 'rating', 'detail_item', 'is_self_variant', 'self_variant', 'question_name', 'skipped'],
            $answerItems
        )->execute();
    }

    public function isEnded()
    {
        $countAnswers = $this->getFoquzAnswer()->where(['in', 'status', [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE]])->count();
        if(!$this->end_of_question)
            return false;
        return $this->end_of_question === 1 ? ($this->goals_count <= $countAnswers && $this->date_end < date('Y-m-d')) : ($this->goals_count <= $countAnswers || $this->date_end < date('Y-m-d'));
    }

    /**нужно ли продолжать рассылку**/
    public function isEndedAuto()
    {
        if (count($this->channels)==0) {
            return true;
        }

        $goalsCount  = $this->getFoquzAnswer()->where(['in', 'status', [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE]])->count();

        $isTimeEnd = false;
        if ($this->date_start && $this->date_start>date("Y-m-d")) {
            $isTimeEnd = true;
        }
        if ($this->date_end && $this->date_end<date("Y-m-d")) {
            $isTimeEnd = true;
        }

        $isTargetEnd = $this->goals_count && $this->goals_count<=$goalsCount;

        if ($this->end_of_question==FoquzPoll::END_QUESTION_AND) {
            if ($isTimeEnd && $isTargetEnd) {
                return true;
            }
        }

        if ($this->end_of_question==FoquzPoll::END_QUESTION_OR) {
            if ($isTimeEnd || $isTargetEnd) {
                return true;
            }
        }

        return false;
    }


    /**список автоматических для авто рассылок**/
    public static function getPollsForAutoSend()
    {
        return  self::find()->where("id>=849")
            ->andWhere([ "is_auto" => 1 ])
            ->andWhere([ "is_tmp" => 0 ])
            ->andWhere([ "status" => FoquzPoll::STATUS_NEW ])
            ->with([
                "channels" => function($q) { $q->with("repeats")->where(["active"=>1])->orderBy("position"); }
            ])
            ->all();
    }


    /** список заказов для автоматических рассылок **/
    public function getOrdersForAutoSend()
    {
        $seconds = $this->getDelaySeconds();

        $delay = 0;
        foreach ($this->channels as $channel)  {
            $delay += $channel->delaySeconds;
            foreach ($channel->repeats as $repeat) {
                $delay += $channel->delaySeconds;
            }
        }

        $startTime = date("Y-m-d H:i:s", time()-$seconds);
        $endTime = date("Y-m-d H:i:s", time()-$seconds-$delay-60*60*24);

        $orders = Order::find()->where([
            "and",
            ["orders.status" => "CLOSED"],
            ["<", "close_time", $startTime],
            [">=", "close_time", $endTime],
            ["source_type"=>[1,3]],
            "filial_id<>16"
        ])
        ->leftJoin("foquz_poll_answer", "foquz_poll_answer.foquz_poll_id={$this->id} AND foquz_poll_answer.order_id=orders.id")
        ->andWhere([
                "or",
                "foquz_poll_answer.id is null",
                "foquz_poll_answer.status in ('new', 'open')"
        ])
        ->with([
            "client"=>function($q) {
                $q->with("contact");
            },
            "dishes",
        ])
        ->all();

        return $orders;
    }


   /** список заказов для автоматических рассылок **/
    public function getContactsForAutoSend()
    {
        $seconds = $this->getDelaySeconds();

        $contacts = [];

        if ($this->trigger_days) {
            $date = date("Y-m-d H:i:s", time()-$this->trigger_days*60*60*24-$seconds);
            $contacts = FoquzContact::find()
                ->leftJoin("clients", "clients.id=foquz_contact.client_id")
                ->leftJoin("orders", "orders.client_id=clients.id")
                ->leftJoin("foquz_poll_answer", "foquz_poll_answer.foquz_poll_id={$this->id} AND foquz_poll_answer.contact_id=foquz_contact.id")
                ->andWhere(["foquz_contact.company_id"=>$this->company_id])
                ->andWhere([
                    "or",
                    "foquz_poll_answer.id is null",
                    "foquz_poll_answer.status in ('new', 'open')"
                ])
                ->groupBY(["foquz_contact.id"])
                ->having([
                    "or",
                    "MAX(orders.close_time) IS NULL",
                    ["<", "MAX(orders.close_time)", $date]
                ])
                ->all();
        }



        return $contacts;
    }


    public function getDelaySeconds()
    {
        $delay = 0;
        if ($this->trigger_time) {
            if (preg_match_all("@(\d+):(\d+):(\d+)@", $this->trigger_time, $arr)) {
                $delay = intval($arr[1][0]) * 60 * 60 + intval($arr[2][0]) * 60 + intval($arr[3][0]);
            }

        }
        return $delay;
    }



    public function refreshPromocodeUsage()
    {
        FoquzPollCode::deleteAll(['poll_id' => $this->id, 'where_use' => [FoquzPollCode::WHERE_CHANNEL, FoquzPollCode::WHERE_REPEAT_CHANNEL]]);
        foreach($this->channels as $channel) {
            if($channel->code !== null || $channel->pool_id !== null) {
                $pollCode = new FoquzPollCode([
                    'poll_id' => $this->id,
                    'code' => $channel->code,
                    'pool_id' => $channel->pool_id,
                    'where_use' => FoquzPollCode::WHERE_CHANNEL,
                    'type' => FoquzPollCode::TYPE_TEXT
                ]);
                $pollCode->save();
            }
            foreach($channel->repeats as $repeat) {
                if($repeat->code !== null || $repeat->pool_id !== null) {
                    $pollCode = new FoquzPollCode([
                        'poll_id' => $this->id,
                        'code' => $repeat->code,
                        'pool_id' => $repeat->pool_id,
                        'where_use' => FoquzPollCode::WHERE_REPEAT_CHANNEL,
                        'type' => FoquzPollCode::TYPE_TEXT
                    ]);
                    $pollCode->save();
                }
            }
        }
        return true;
    }

    public function collectPools($channel = null)
    {
        $pools = [];
        $poolIds = [];
        if($this->startPage->enabled && $this->startPage->titlePromocode && $this->startPage->titlePromocode->pool_id !== null && !in_array($this->startPage->titlePromocode->pool_id, $poolIds)) {
            $pools[] = $this->startPage->titlePromocode->pool;
            $poolIds[] = $this->startPage->titlePromocode->pool_id;
        }
        if($this->startPage->enabled && $this->startPage->textPromocode && $this->startPage->textPromocode->pool_id !== null  && !in_array($this->startPage->textPromocode->pool_id, $poolIds)) {
            $pools[] = $this->startPage->textPromocode->pool;
            $poolIds[] = $this->startPage->textPromocode->pool_id;
        }
        if($this->endPage->enabled && $this->endPage->titlePromocode && $this->endPage->titlePromocode->pool_id !== null  && !in_array($this->endPage->titlePromocode->pool_id, $poolIds)) {
            $pools[] = $this->endPage->titlePromocode->pool;
            $poolIds[] = $this->endPage->titlePromocode->pool_id;
        }
        if($this->endPage->enabled && $this->endPage->textPromocode && $this->endPage->textPromocode->pool_id !== null  && !in_array($this->endPage->textPromocode->pool_id, $poolIds)) {
            $pools[] = $this->endPage->textPromocode->pool;
            $poolIds[] = $this->endPage->textPromocode->pool_id;
        }
        if($channel && $channel->pool  && !in_array($channel->pool_id, $poolIds)) {
            $pools[] = $channel->pool;
            $poolIds[] = $channel->pool_id;
        }
        return $pools;
    }

    public function checkPoolUsage($channel = null)
    {
        $poolsForCheck = $this->collectPools($channel);
        $errors = [];
        if($this->is_auto) {
            /** @var DiscountPool $pool */
            foreach($poolsForCheck as $pool) {

                if ($pool->isPoolType()) {
                    if(count($pool->availableCoupons) === 0 && $pool->stop_pool_send) {
                        $errors[] = 'Отправка опроса невозможна, так как закончились промокоды в пуле купонов <b>'.$pool->title.'</b>';
                    } else {
                        if(count($pool->availableCoupons) === 0) {
                            $errors[] = 'У пула <b>'.$pool->title.'</b> закончились прокомоды';
                        } elseif(count($pool->availableCoupons) < $pool->coupon_less_than_value && $pool->coupon_less_than_value !== null) {
                            $errors[] = 'У пула <b>'.$pool->title.'</b> количество промокодов меньше '.$pool->coupon_less_than_value;
                        }
                    }
                }

                if($pool->available_to !== null && $pool->available_to < date('Y-m-d') && $pool->stop_pool_send) {
                    $errors[] = 'Отправка опроса невозможна, так как закончился срок действия пула купонов <b>'.$pool->title.'</b>';
                } else {
                    if($pool->available_to !== null) {
                        if($pool->available_to < date('Y-m-d')) {
                            $errors[] = 'У пула <b>'.$pool->title.'</b> истёк срок действия';
                        } else {
                            $now = time();
                            $dateEnd = strtotime($pool->available_to);
                            $dateDiff = $dateEnd - $now;
                            $diffInDays = floor($dateDiff / (60 * 60 * 24));
                            if($pool->count_days !== null && $diffInDays <= $pool->count_days && $diffInDays > 0) {
                                $errors[] = 'У пула <b>'.$pool->title.'</b> истекает срок действия через '.$diffInDays.$pool->pluralDays($diffInDays);
                            }
                        }
                    }
                }
                if($pool->stop_pool_send && count($errors) > 0) {
                    return [false, $errors];
                }
            }
        } else {
            /** @var DiscountPool $pool */
            $pool = $channel->pool ?? null;
            if($pool) {
                if ($pool->isPoolType()) {
                    if(count($pool->availableCoupons) === 0 && $pool->stop_pool_send) {
                        $errors[] = 'Отправка опроса на <b>'.$channel->name.'</b> невозможна, так как закончились промокоды в пуле купонов <b>'.$pool->title.'</b>';
                    } else {
                        if(count($pool->availableCoupons) === 0) {
                            $errors[] = 'У пула <b>'.$pool->title.'</b> закончились прокомоды';
                        } elseif(count($pool->availableCoupons) < $pool->coupon_less_than_value && $pool->coupon_less_than_value !== null) {
                            $errors[] = 'У пула <b>'.$pool->title.'</b> количество промокодов меньше '.$pool->coupon_less_than_value;
                        }
                    }
                }


                if($pool->available_to !== null && $pool->available_to < date('Y-m-d') && $pool->stop_pool_send) {
                    $errors[] = 'Отправка опроса на <b>'.$channel->name.'</b> невозможна, так как закончился срок действия пула купонов <b>'.$pool->title.'</b>';
                } else {
                    if($pool->available_to !== null) {
                        if($pool->available_to < date('Y-m-d')) {
                            $errors[] = 'У пула <b>'.$pool->title.'</b> истёк срок действия';
                        } else {
                            $now = time();
                            $dateEnd = strtotime($pool->available_to);
                            $dateDiff = $dateEnd - $now;
                            $diffInDays = floor($dateDiff / (60 * 60 * 24));
                            if($pool->count_days !== null && $diffInDays <= $pool->count_days && $diffInDays > 0) {
                                $errors[] = 'У пула <b>'.$pool->title.'</b> истекает срок действия через '.$diffInDays.$pool->pluralDays($diffInDays);
                            }
                        }
                    }
                }
                if($pool->stop_pool_send && count($errors) > 0) {
                    return [false, $errors];
                }
            }
        }
        return [true, $errors];
    }

    public function getFirstAnswer()
    {
        return $this->getFoquzAnswer()->andWhere([
            'status' => [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE]
        ])->orderBy('updated_at ASC')
            ->limit(1)
            ->one();
    }

    public function getLastAnswer()
    {
        return $this->getFoquzAnswer()->andWhere([
            'status' => [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE]
        ])->orderBy('updated_at DESC')
            ->limit(1)
            ->one();
    }

    public function getAvgTimeForAnswer()
    {
        $avgTimeForAnswer = (new Query())
            ->select(new Expression("AVG(userTime) AS avgTime"))
            ->from(new Expression("(SELECT TIME_TO_SEC(TIMEDIFF(
               (SELECT max(created_at) FROM foquz_poll_answer_item fpai1 WHERE fpai1.foquz_poll_answer_id = fpai.foquz_poll_answer_id),
                IFNULL(first_question_showed_at, (SELECT min(created_at) FROM foquz_poll_answer_item fpai1 WHERE fpai1.foquz_poll_answer_id = fpai.foquz_poll_answer_id))
                )) AS userTime
                FROM foquz_poll_answer_item fpai
                LEFT JOIN foquz_poll_answer fpa ON fpai.foquz_poll_answer_id = fpa.id
                WHERE fpa.foquz_poll_id = ".$this->id." AND fpa.status = '".FoquzPollAnswer::STATUS_DONE."'
                GROUP BY fpai.foquz_poll_answer_id
                HAVING userTime < ".$this->getFoquzQuestions()->count()*FoquzPollAnswer::AVG_TIME_PER_QUESTION."
                ) timeSelector"))->scalar();
        $minutes = intdiv($avgTimeForAnswer, 60);
        if($minutes < 10) {
            $minutes = '0'.$minutes;
        }
        $seconds = number_format($avgTimeForAnswer % 60, 0);
        if($seconds < 10) {
            $seconds = '0'.$seconds;
        }
        return $minutes.':'.$seconds;
    }

    public static function recursiveMove($items, $to)
    {
        foreach($items as $poll) {
            if($poll->is_folder) {
                self::recursiveMove($poll->folders, $to);
            } else {
                $poll->folder_id = $to;
                $poll->save();
            }
        }
    }

    public static function recursiveArchive($items)
    {
        foreach($items as $poll) {
            if($poll->is_folder) {
                self::recursiveArchive($poll->folders);
            } else {
                $poll->status = FoquzPoll::STATUS_ARCHIVE;
                $poll->save();
            }
        }
    }

    public static function recursiveDelete($items)
    {
        foreach($items as $poll) {
            if($poll->is_folder) {
                self::recursiveDelete($poll->folders);
            } else {
                $poll->deleted = 1;
                $poll->deleted_at = date('Y-m-d H:i:s');
                $poll->deleted_by = Yii::$app->user->id;
                $poll->save();
            }
        }
    }

    public function getTriggerString()
    {
        $array = [
            FoquzPoll::TRIGGER_ORDER_CONFIRM => 'Заказ оформлен',
            FoquzPoll::TRIGGER_ORDER_DELIVERED => 'Заказ доставлен',
            FoquzPoll::TRIGGER_NO_ORDER => 'Не было заказа, дней',
            FoquzPoll::TRIGGER_NO_ORDER_AS_USUAL => 'Не сделал заказ как обычно',
            FoquzPoll::TRIGGER_ORDERS => 'Заказы'
        ];
        return $array[$this->trigger];
    }

    public function copyPages($newPollId)
    {
        foreach($this->pages as $tPage) {
            $page = new FoquzPollPage();
            $page->attributes = $tPage->attributes;
            $page->created_at = date('d.m.Y H:i:s');
            $page->updated_at = date('d.m.Y H:i:s');
            $page->foquz_poll_id = $newPollId;
            if($tPage->image_url) {
                $basePath = "uploads/foquz/".$newPollId;
                $fullPath = Yii::getAlias("@app/web/{$basePath}");

                if (false === file_exists($fullPath)) {
                    mkdir($fullPath, 0777, true);
                }

                $page->image_url = str_replace($tPage->foquz_poll_id, $newPollId, $tPage->image_url);

                try {
                    copy(Yii::getAlias("@app/web{$tPage->image_url}"), Yii::getAlias("@app/web{$page->image_url}"));
                } catch(\Exception $e) {
                    echo "Can't copy image for poll ".$newPollId."\n";
                    echo $e->getMessage()."\n";
                }
            }
            $page->save();

            if($tPage->socNetworksOptions) {
                $sno = new FoquzPollPageSocialNetworksOptions();
                $sno->attributes = $tPage->socNetworksOptions->attributes;
                $sno->foquz_poll_page_id = $page->id;
                $sno->save();
            }

            if(($titlePromo = $tPage->getTitlePromocode()) !== null) {
                $foquzPollCode = new FoquzPollCode();
                $foquzPollCode->poll_id = $newPollId;
                $foquzPollCode->pool_id = $titlePromo->pool_id;
                $foquzPollCode->where_use = $titlePromo->where_use;
                $foquzPollCode->type = $titlePromo->type;

                $foquzPollCode->save();
            }

            if(($titlePromo = $tPage->getTextPromocode()) !== null) {
                $foquzPollCode = new FoquzPollCode();
                $foquzPollCode->poll_id = $newPollId;
                $foquzPollCode->pool_id = $titlePromo->pool_id;
                $foquzPollCode->where_use = $titlePromo->where_use;
                $foquzPollCode->type = $titlePromo->type;

                $foquzPollCode->save();
            }
        }
    }

    public function copyScoresInterpretationRanges($newPollId)
    {
        foreach($this->scoresInterpretationRanges as $range) {
            $newRanges = new ScoresInterpretationRange();
            $newRanges->attributes = $range->attributes;
            $newRanges->foquz_poll_id = $newPollId;
            if(!$newRanges->save()) {
                Yii::error($newRanges->errors);
            }
        }

    }

    public function copyDesign($newPollId)
    {
        $design = $this->design;
        $newPollDesign = new FoquzPollDesign();
        $newPollDesign->attributes = $design->attributes;
        $newPollDesign->foquz_poll_id = $newPollId;
        if($design->background_image !== FoquzPollDesign::DEFAULT_BG_IMAGE) {
            $fullPath = Yii::getAlias("@app/web/uploads/foquz/poll/".$newPollId."/bg");
            if (false === file_exists($fullPath)) {
                mkdir($fullPath, 0777, true);
            }
            $newPollDesign->background_image = str_replace($design->foquz_poll_id, $newPollId, $design->background_image);
            if($design->background_image) {
                try {
                    copy(Yii::getAlias("@app/web{$design->background_image}"), Yii::getAlias("@app/web{$newPollDesign->background_image}"));
                } catch(\Exception $e) {
                    Yii::error("Can't copy background design image for poll ".$newPollId."\n");
                }
            }
        }
        if($design->logo_image !== FoquzPollDesign::DEFAULT_LOGO_IMAGE) {
            $fullPath = Yii::getAlias("@app/web/uploads/foquz/poll/".$newPollId."/logo");
            if (false === file_exists($fullPath)) {
                mkdir($fullPath, 0777, true);
            }
            $newPollDesign->logo_image = str_replace($design->foquz_poll_id, $newPollId, $design->logo_image);
            try {
                copy(Yii::getAlias("@app/web{$design->logo_image}"), Yii::getAlias("@app/web{$newPollDesign->logo_image}"));
            } catch(\Exception $e) {
               // echo "Can't copy logo design image for poll ".$newPollId."\n";
            }
        }
        $newPollDesign->save();
    }

    public function copyQuestions($company_user_id, $newPollId, $newCompany = false)
    {
        $extraQuestionService = new ExtraQuestionService();

        $pollLangs = [];
        $questionLinks = [];
        $variantLinks = [];
        $pagesLinks = [];
        $differentialRows = [];
        $smileRows = [];

        if($this->displaySetting) {
            $displaySettings = new FoquzPollDisplaySetting(['scenario' => FoquzPollDisplaySetting::SCENARIO_COPY]);
            $displaySettings->attributes = $this->displaySetting->attributes;
            $displaySettings->poll_id = $newPollId;
            $displaySettings->save();
        }
        foreach($this->displayPages as $tPage) {
            $pollDisplayPage = new FoquzPollDisplayPage();
            $pollDisplayPage->attributes = $tPage->attributes;
            $pollDisplayPage->foquz_poll_id = $newPollId;
            $pollDisplayPage->save();
            $pagesLinks[$tPage->id] = $pollDisplayPage->id;
        }
        if ($this->foquzPollLangs) {
            foreach ($this->foquzPollLangs as $pollLang) {
                $newPollLang = new FoquzPollLang();
                $newPollLang->setAttributes(ArrayHelper::toArray($pollLang));
                $newPollLang->foquz_poll_id = $newPollId;
                if ($newPollLang->save()) {
                    $pollLangs[$pollLang->id] = $newPollLang->id;
                }
            }
        }
        $donors = []; $newQuestions = [];
        foreach($this->foquzQuestions as $tQuestion) {
            $oldDonor = FoquzQuestion::findOne(['donor' => $tQuestion->id]);
            $pollQuestion = new FoquzQuestion();
            $pollQuestion->attributes = $tQuestion->attributes;
            $pollQuestion->created_at = time();
            $pollQuestion->updated_at = time();
            $pollQuestion->created_by = $company_user_id;
            $pollQuestion->updated_by = $company_user_id;
            $pollQuestion->poll_id = $newPollId;
            if ($tQuestion->donor) {
                $pollQuestion->donor = array_search($tQuestion->donor, $donors);
                if ($tQuestion->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX && $pollQuestion->donor) {
                    $matrixSettings = json_decode($pollQuestion->matrix_settings, true);
                    $newRows = [];
                    foreach ($matrixSettings['donorRows'] as $donorRow) {
                        if (isset($variantLinks[$donorRow])) {
                            $newRows[] = $variantLinks[$donorRow];
                        } else {
                            $newRows[] = $donorRow;
                        }
                    }
                    $matrixSettings['donorRows'] = $newRows;
                    $pollQuestion->matrix_settings = json_encode($matrixSettings, JSON_UNESCAPED_UNICODE);
                }
            }
            if ($tQuestion->donor_rows) {
                $pollQuestion->donor_rows = $questionLinks[$pollQuestion->donor_rows] ?? null;
            }
            if ($tQuestion->donor_columns) {
                $pollQuestion->donor_columns = $questionLinks[$pollQuestion->donor_columns] ?? null;
            }
            if ($pollQuestion->save()) {
                if ($oldDonor) {
                    $donors[$pollQuestion->id] = $tQuestion->id;
                }
                $questionLinks[$tQuestion->id] = $pollQuestion->id;
                if ($tQuestion->donor) {
                    $oldRecipientDetails = RecipientQuestionDetail::findAll(['recipient_id' => $tQuestion->id]);
                    if ($oldRecipientDetails) {
                        foreach ($oldRecipientDetails as $detail) {
                            $recipientQuestionDetail = new RecipientQuestionDetail();
                            $recipientQuestionDetail->recipient_id = $pollQuestion->id;
                            $recipientQuestionDetail->dictionary_element_id = $detail->dictionary_element_id;
                            $recipientQuestionDetail->question_detail_id = $variantLinks[$detail->question_detail_id] ?? null;
                            $recipientQuestionDetail->description = $detail->description;
                            $recipientQuestionDetail->type = $detail->type;
                            $recipientQuestionDetail->position = $detail->position;
                            $recipientQuestionDetail->points = $detail->points;
                            $recipientQuestionDetail->without_points = $detail->without_points;
                            $recipientQuestionDetail->need_extra = $detail->need_extra;
                            $recipientQuestionDetail->comment_required = $detail->comment_required;
                            $recipientQuestionDetail->save();
                        }
                    }
                }
                if ($tQuestion->cardSortingCategories) {
                    foreach ($tQuestion->cardSortingCategories as $category) {
                        $cardSortingCategory = new FoquzQuestionCardSortingCategory();
                        $cardSortingCategory->foquz_question_id = $pollQuestion->id;
                        $cardSortingCategory->name = $category->name;
                        $cardSortingCategory->position = $category->position;
                        $cardSortingCategory->is_deleted = $category->is_deleted;
                        $cardSortingCategory->save();

                        foreach ($category->langs as $lang) {
                            $cardSortingCategoryLang = new FoquzQuestionCardSortingCategoryLang();
                            $cardSortingCategoryLang->category_id = $cardSortingCategory->id;
                            $cardSortingCategoryLang->name = $lang->name;
                            $newLangId = FoquzPollLang::find()
                                ->select('id')
                                ->where(['foquz_poll_id' => $newPollId, 'poll_lang_id' => $lang->lang->poll_lang_id])
                                ->scalar();
                            $cardSortingCategoryLang->foquz_poll_lang_id = $newLangId;
                            $cardSortingCategoryLang->save();
                        }
                    }
                }
                if ($tQuestion->addressCodes) {
                    $pollQuestionAC = new FoquzQuestionAddressCodes();
                    $pollQuestionAC->attributes = $tQuestion->addressCodes->attributes;
                    $pollQuestionAC->question_id = $pollQuestion->id;
                    $pollQuestionAC->save();
                }
                if ($tQuestion->questionDetails) {

                    $sourceDetailsTemp = [];

                    foreach ($tQuestion->questionDetails as $tqd) {
                        $pollQuestionDetail = new FoquzQuestionDetail();
                        $pollQuestionDetail->attributes = $tqd->attributes;
                        $pollQuestionDetail->foquz_question_id = $pollQuestion->id;
                        $pollQuestionDetail->save();
                        $variantLinks[$tqd->id] = $pollQuestionDetail->id;

                        $sourceDetailsTemp[$tqd->id] = $pollQuestionDetail;

                        if ($tqd->file) {
                            (new FileService())->copyFile($tqd->file, $pollQuestionDetail->id);
                        }
                        if ($tqd->fileSelfVariant) {
                            (new FileService())->copyFile($tqd->fileSelfVariant, $pollQuestionDetail->id);
                        }

                        if ($tqd->foquzQuestionDetailLangs) {
                            foreach ($tqd->foquzQuestionDetailLangs as $detailLang) {
                                $questionDetailLang = new FoquzQuestionDetailLang();
                                $questionDetailLang->attributes = $detailLang->attributes;
                                if (isset($pollLangs[$detailLang->foquz_poll_lang_id])) {
                                    $questionDetailLang->foquz_poll_lang_id = $pollLangs[$detailLang->foquz_poll_lang_id];
                                }
                                if (isset($questionLinks[$detailLang->foquz_question_id])) {
                                    $questionDetailLang->foquz_question_id = $questionLinks[$detailLang->foquz_question_id];
                                }
                                if (isset($variantLinks[$detailLang->foquz_question_detail_id])) {
                                    $questionDetailLang->foquz_question_detail_id = $variantLinks[$detailLang->foquz_question_detail_id];
                                }
                                $questionDetailLang->save();
                            }
                        }
                    }

                    $extraQuestionService->fixQuestionDetailId($sourceDetailsTemp);
                    unset($sourceDetailsTemp);
                }

                if ($selfVariantFile = FoquzFile::findOne(['entity_type' => FoquzFile::TYPE_SELF_VARIANT, 'entity_id' => $tQuestion->id])) {
                    (new FileService())->copyFile($selfVariantFile, $pollQuestion->id);
                }

                if ($tQuestion->differentialRows) {
                    foreach ($tQuestion->differentialRows as $tqdr) {
                        $pollQuestionDifferentialRow = new FoquzQuestionDifferentialRow();
                        $pollQuestionDifferentialRow->attributes = $tqdr->attributes;
                        $pollQuestionDifferentialRow->question_id = $pollQuestion->id;
                        $pollQuestionDifferentialRow->save();
                        $differentialRows[$tqdr->id] = $pollQuestionDifferentialRow->id;
                    }
                }
                if ($tQuestion->questionSmiles) {
                    foreach ($tQuestion->questionSmiles as $qs) {
                        $questionSmile = new FoquzQuestionSmile();
                        $questionSmile->attributes = $qs->attributes;
                        $questionSmile->foquz_question_id = $pollQuestion->id;
                        $questionSmile->save();
                        $smileRows[$qs->id] = $questionSmile->id;
                    }
                }
                if ($tQuestion->foquzQuestionLangs) {
                    foreach ($tQuestion->foquzQuestionLangs as $foquzQuestionLang) {
                        if (isset($pollLangs[$foquzQuestionLang->foquz_poll_lang_id])) {
                            $questionLang = new FoquzQuestionLang();
                            $questionLang->setAttributes(ArrayHelper::toArray($foquzQuestionLang));
                            $questionLang->description = $foquzQuestionLang->description;
                            $questionLang->description_html = $foquzQuestionLang->description_html;
                            $questionLang->foquz_question_id = $pollQuestion->id;
                            $questionLang->foquz_poll_lang_id = $pollLangs[$foquzQuestionLang->foquz_poll_lang_id];
                            if ($questionLang->labels && in_array($pollQuestion->main_question_type, [
                                FoquzQuestion::TYPE_SEM_DIFFERENTIAL,
                                FoquzQuestion::TYPE_SMILE_RATING
                            ], true)) {
                                $newLabels = [];
                                if ($questionLang->labels) {
                                    if (!is_array($questionLang->labels)) {
                                        $questionLang->labels = json_decode($questionLang->labels);
                                    }
                                    foreach ($questionLang->labels as $key => $label) {
                                        if (
                                            $pollQuestion->main_question_type === FoquzQuestion::TYPE_SEM_DIFFERENTIAL &&
                                            isset($differentialRows[$key])
                                        ) {
                                            $newLabels[$differentialRows[$key]] = $label;
                                        } elseif (
                                            $pollQuestion->main_question_type === FoquzQuestion::TYPE_SMILE_RATING &&
                                            isset($smileRows[$key])
                                        ) {
                                            $newLabels[$smileRows[$key]] = $label;
                                        }
                                    }
                                }
                                if ($newLabels) {
                                    $questionLang->labels = $newLabels;
                                }
                            }
                            $questionLang->save();
                        }
                    }
                }
                if ($tQuestion->semDifSetting) {
                    $pollQuestionSemDifSetting = new FoquzQuestionSemDifSetting();
                    $pollQuestionSemDifSetting->attributes = $tQuestion->semDifSetting->attributes;
                    $pollQuestionSemDifSetting->foquz_question_id = $pollQuestion->id;
                    $pollQuestionSemDifSetting->save();
                }
                if ($tQuestion->npsRatingSetting) {
                    $pollQuestionNPS = new FoquzQuestionNpsRatingSetting();
                    $pollQuestionNPS->attributes = $tQuestion->npsRatingSetting->attributes;
                    $pollQuestionNPS->foquz_question_id = $pollQuestion->id;
                    $pollQuestionNPS->save();
                }
                if ($tQuestion->starRatingOptions) {
                    $pollQuestionStarRating = new FoquzQuestionStarRatingOptions();
                    $pollQuestionStarRating->attributes = $tQuestion->starRatingOptions->attributes;
                    $pollQuestionStarRating->foquz_question_id = $pollQuestion->id;
                    $pollQuestionStarRating->save();
                }
                if ($tQuestion->intermediateBlock) {
                    $pollQuestionIntermediateBlock = new FoquzQuestionIntermediateBlockSetting();
                    $pollQuestionIntermediateBlock->attributes = $tQuestion->intermediateBlock->attributes;
                    $pollQuestionIntermediateBlock->question_id = $pollQuestion->id;
                    if ($newCompany === true) {
                        $pollQuestionIntermediateBlock->pool_id = null;
                    }
                    $pollQuestionIntermediateBlock->save();
                    if ($tQuestion->intermediateBlock->socNetworks) {
                        $pollQuestionIntermediateBlockNetworks = new FoquzQuestionIntermediateBlockSettingSocNetworks();
                        $pollQuestionIntermediateBlockNetworks->attributes = $tQuestion->intermediateBlock->socNetworks->attributes;
                        $pollQuestionIntermediateBlockNetworks->intermediate_block_id = $pollQuestionIntermediateBlock->id;
                        $pollQuestionIntermediateBlockNetworks->save();
                    }
                    if ($tQuestion->intermediateBlock->langs) {
                        foreach ($tQuestion->intermediateBlock->langs as $ibLang) {
                            if (isset($pollLangs[$ibLang->lang_id])) {
                                $pollQuestionIntermediateBlockLang = new FoquzQuestionIntermediateBlockSettingLang();
                                $pollQuestionIntermediateBlockLang->setAttributes(ArrayHelper::toArray($ibLang));
                                $pollQuestionIntermediateBlockLang->setting_id = $pollQuestionIntermediateBlock->id;
                                $pollQuestionIntermediateBlockLang->lang_id = $pollLangs[$ibLang->lang_id];
                                $pollQuestionIntermediateBlockLang->save();
                            }
                        }
                    }
                }
                if ($tQuestion->rightAnswer) {
                    $pollQuestionRightAnswer = new FoquzQuestionRightAnswer();
                    $pollQuestionRightAnswer->attributes = $tQuestion->rightAnswer->attributes;
                    $pollQuestionRightAnswer->foquz_question_id = $pollQuestion->id;
                    $pollQuestionRightAnswer->save();
                }
                if ($tQuestion->foquzQuestionPrioritySettings) {
                    $foquzQuestionPrioritySettings = new FoquzQuestionPrioritySettings();
                    $foquzQuestionPrioritySettings->attributes = $tQuestion->foquzQuestionPrioritySettings->attributes;
                    $foquzQuestionPrioritySettings->foquz_question_id = $pollQuestion->id;
                    $foquzQuestionPrioritySettings->save();
                }
                if ($tQuestion->scaleRatingSetting) {
                    $pollQuestionScaleRating = new FoquzQuestionScaleRatingSetting();
                    $pollQuestionScaleRating->attributes = $tQuestion->scaleRatingSetting->attributes;
                    $pollQuestionScaleRating->foquz_question_id = $pollQuestion->id;
                    $pollQuestionScaleRating->save();
                }
                if ($tQuestion->formFields) {
                    foreach ($tQuestion->formFields as $tqff) {
                        $pollQuestionFormField = new FoquzQuestionFormField();
                        $pollQuestionFormField->attributes = $tqff->attributes;
                        $pollQuestionFormField->question_id = $pollQuestion->id;
                        if ($pollQuestionFormField->save() && $tqff->langs) {
                            foreach ($tqff->langs as $lang) {
                                if (isset($pollLangs[$lang->foquz_poll_lang_id])) {
                                    $formLang = new FoquzQuestionFormFieldLang();
                                    $formLang->setAttributes(ArrayHelper::toArray($lang));
                                    $formLang->form_field_id = $pollQuestionFormField->id;
                                    $formLang->foquz_poll_lang_id = $pollLangs[$lang->foquz_poll_lang_id];
                                    $formLang->save();
                                }
                            }
                        }
                    }
                }
                if ($tQuestion->questionFiles) {
                    foreach ($tQuestion->questionFiles as $tqqf) {
                        $pollQuestionFile = new FoquzQuestionFile();
                        $pollQuestionFile->attributes = $tqqf->attributes;
                        $pollQuestionFile->created_at = time();
                        $pollQuestionFile->question_id = $pollQuestion->id;
                        if ($tqqf->attachment_type !== 'link' && stristr('img/', $tqqf->file_path) === false) {
                            $pollQuestionFile->file_path = str_replace($tqqf->question_id, $pollQuestion->id, $tqqf->file_path);
                            $pollQuestionFile->file_full_path = str_replace($tqqf->question_id, $pollQuestion->id, $tqqf->file_full_path);
                            if (file_exists($tqqf->file_path)) {
                                $fullPath = Yii::getAlias("@app/web/uploads/foquz/" . $pollQuestion->id);
                                if (false === file_exists($fullPath)) {
                                    mkdir($fullPath, 0777, true);
                                }
                                try {
                                    copy(Yii::getAlias("@app/web/{$tqqf->file_path}"), Yii::getAlias("@app/web/{$pollQuestionFile->file_path}"));
                                } catch (\Exception $e) {
                                    echo "Can't copy question file for poll " . $newPollId . "\n";
                                    echo $fullPath . "\n";
                                    echo $e->getMessage() . "\n";
                                }
                            }
                            if ($tqqf->type === 'video' && file_exists(Yii::getAlias("@app/web/$pollQuestionFile->file_path"))) {
                                $ffmpeg = FFMpeg::create([
                                    'ffmpeg.binaries' => Yii::$app->params['ffmpeg_binaries'],
                                    'ffprobe.binaries' => Yii::$app->params['ffprobe_binaries']
                                ]);
                                $video = $ffmpeg->open(Yii::getAlias("@app/web/$pollQuestionFile->file_path"));
                                $video->frame(TimeCode::fromSeconds(5))
                                    ->save(Yii::getAlias("@app/web/$pollQuestionFile->file_path") . '.jpg');
                            }
                        }
                        if ($pollQuestionFile->save() && $tqqf->langs) {
                            foreach ($tqqf->langs as $lang) {
                                if (isset($pollLangs[$lang->foquz_poll_lang_id])) {
                                    $fileLang = new FoquzQuestionFileLang();
                                    $fileLang->setAttributes(ArrayHelper::toArray($lang));
                                    $fileLang->foquz_question_file_id = $pollQuestionFile->id;
                                    $fileLang->foquz_poll_lang_id = $pollLangs[$lang->foquz_poll_lang_id];
                                    $fileLang->save();
                                }
                            }
                        }
                    }
                }
                if ($tQuestion->foquzQuestionEndScreenLogos) {
                    foreach ($tQuestion->foquzQuestionEndScreenLogos as $tqqf) {
                        $pollQuestionFile = new FoquzQuestionEndScreenLogo();
                        $pollQuestionFile->attributes = $tqqf->attributes;
                        $pollQuestionFile->foquz_question_id = $pollQuestion->id;
                        if ($tqqf->logo) {
                            $pollQuestionFile->logo = str_replace('/' . $tqqf->foquz_question_id . '/', '/' . $pollQuestion->id . '/', $tqqf->logo);
                            if (file_exists($tqqf->logo)) {
                                $fullPath = Yii::getAlias("@app/web/uploads/foquz/end-screen-logos/" . $pollQuestion->id);
                                if (false === file_exists($fullPath)) {
                                    mkdir($fullPath, 0777, true);
                                }
                                try {
                                    copy(Yii::getAlias("@app/web{$tqqf->logo}"), Yii::getAlias("@app/web{$pollQuestionFile->logo}"));
                                } catch (\Exception $e) {
                                    echo "Can't copy question file for question " . $pollQuestion->id . "\n";
                                    echo $fullPath . "\n";
                                    echo $e->getMessage() . "\n";
                                }
                            }
                        }
                        $pollQuestionFile->save();
                    }
                }
                if ($tQuestion->pollDisplayPageQuestion) {
                    $displayPageQuestion = new FoquzPollDisplayPageQuestion();
                    $displayPageQuestion->question_id = $pollQuestion->id;
                    $displayPageQuestion->display_page_id = $pagesLinks[$tQuestion->pollDisplayPageQuestion->display_page_id];
                    $displayPageQuestion->save();
                }

                if ($tQuestion->activeMatrixElements) {
                    foreach ($tQuestion->activeMatrixElements as $tame) {
                        $pollQuestionActiveMatrixElement = new FoquzQuestionMatrixElement();
                        $pollQuestionActiveMatrixElement->attributes = $tame->attributes;
                        $pollQuestionActiveMatrixElement->foquz_question_id = $pollQuestion->id;

                        if ($tame->donor_variant_id) {
                            $pollQuestionActiveMatrixElement->donor_variant_id = $variantLinks[$tame->donor_variant_id] ?? null;
                        }

                        $pollQuestionActiveMatrixElement->save();

                        if ($tame->langs) {
                            foreach ($tame->langs as $taml) {
                                $pollQuestionActiveMatrixElementLang = new FoquzQuestionMatrixElementLang();
                                $pollQuestionActiveMatrixElementLang->attributes = $taml->attributes;
                                $pollQuestionActiveMatrixElementLang->matrix_element_id = $pollQuestionActiveMatrixElement->id;
                                $pollQuestionActiveMatrixElementLang->poll_lang_id = $pollLangs[$taml->poll_lang_id];
                                $pollQuestionActiveMatrixElementLang->save();
                            }
                        }

                        foreach ($tame->activeVariants as $tav) {
                            $pollQuestionActiveVariant = new FoquzQuestionMatrixElementVariant();
                            $pollQuestionActiveVariant->attributes = $tav->attributes;
                            $pollQuestionActiveVariant->matrix_element_id = $pollQuestionActiveMatrixElement->id;
                            $pollQuestionActiveVariant->save();

                            if ($tav->langs) {
                                foreach ($tav->langs as $tavl) {
                                    $pollQuestionActiveVariantLang = new FoquzQuestionMatrixElementVariantLang();
                                    $pollQuestionActiveVariantLang->attributes = $tavl->attributes;
                                    $pollQuestionActiveVariantLang->variant_id = $pollQuestionActiveVariant->id;
                                    $pollQuestionActiveVariantLang->poll_lang_id = $pollLangs[$tavl->poll_lang_id];
                                    $pollQuestionActiveVariantLang->save();
                                }
                            }
                        }
                    }
                }

                if ($tQuestion->dictionaryLangs) {
                    foreach ($tQuestion->dictionaryLangs as $tDictionaryLang) {
                        $dictionaryLang = new FoquzQuestionDetailLang();
                        $dictionaryLang->attributes = $tDictionaryLang->attributes;
                        $dictionaryLang->foquz_poll_lang_id = $pollLangs[$tDictionaryLang->foquz_poll_lang_id];
                        $dictionaryLang->foquz_question_id = $pollQuestion->id;
                        $dictionaryLang->save();
                    }
                }

                if ($tQuestion->firstClick) {
                    $tQuestion->firstClick->copy($pollQuestion->id);
                }
                if ($tQuestion->firstClickArea) {
                    foreach ($tQuestion->firstClickArea as $fca) {
                        $fca->copy($pollQuestion->id);
                    }
                }

                $newQuestions[$tQuestion->id] = $pollQuestion;

            }
        }

        //копироапние логиеи отдельно, нужны ссылки на все страницы
        foreach($this->foquzQuestions as $tQuestion) {
            if (isset($newQuestions[$tQuestion->id])) {
                $pollQuestion = $newQuestions[$tQuestion->id];
                foreach ($tQuestion->questionLogic as $tql) {
                    $questionLogic = new FoquzPollQuestionsLogic();
                    $questionLogic->attributes = $tql->attributes;
                    $questionLogic->question_id = $questionLinks[$tql->question_id];
                    $questionLogic->jump_question_id = $tql->jump_question_id && isset($questionLinks[$tql->jump_question_id]) ?
                        $questionLinks[$tql->jump_question_id] : null;
                    $questionLogic->jump_display_page_id = $tql->jump_display_page_id ? $pagesLinks[$tql->jump_display_page_id] : null;
                    $newVariants = [];
                    if ($tql->variants) {
                        foreach (json_decode($tql->variants) as $variant) {
                            if (in_array($variant, array_keys($variantLinks))) {
                                $newVariants[] = $variantLinks[$variant];
                            } else {
                                $newVariants[] = $variant;
                            }
                        }
                        $questionLogic->variants = json_encode(array_map('intval', $newVariants));
                    }
                    $questionLogic->save();
                }
                foreach ($tQuestion->foquzPollQuestionViewLogics as $tql) {
                    $questionLogic = new FoquzPollQuestionViewLogic();
                    $questionLogic->attributes = $tql->attributes;
                    $questionLogic->question_id = $questionLinks[$tql->question_id];
                    $questionLogic->condition_question_id = $tql->condition_question_id && isset($questionLinks[$tql->condition_question_id]) ?
                        $questionLinks[$tql->condition_question_id] : null;
                    if (isset($tql->variants) && $tql->variants) {
                        $newVariants = [];
                        foreach ($tql->variants as $variant) {
                            if (isset($variant['row'])) {
                                if (array_key_exists($variant['row'], $variantLinks)) {
                                    $newVariants[] = ['col' => $variant['col'], 'row' => $variantLinks[$variant['row']]];
                                } else {
                                    $newVariants[] = $variant;
                                }
                            } elseif (array_key_exists($variant, $variantLinks)) {
                                $newVariants[] = $variantLinks[$variant];
                            } else {
                                $newVariants[] = $variant;
                            }
                        }
                        $questionLogic->variants = $newVariants ?: null;
                    }
                    $questionLogic->save();
                }
            }
        }
        //Копирование ID вопросов-исключений для случайного порядка в логике отображения
        if (!empty($displaySettings->random_exclusion)) {
            $randomExclusion = $displaySettings->random_exclusion;
            $newRandomExclusion = [];
            foreach ($randomExclusion as $value) {
                if (isset($questionLinks[$value])) {
                    $newRandomExclusion[] = $questionLinks[$value];
                }
            }
            $displaySettings->random_exclusion = $newRandomExclusion;
            $displaySettings->save();
        }
    }

    public function copyChannels($newPollId)
    {
        foreach($this->channels as $tChannel) {
            $channel = new Channel();
            $channel->active = $tChannel->active;
            $channel->name = $tChannel->name;
            $channel->position = $tChannel->position;
            $channel->delay = $tChannel->delay;
            $channel->poll_id = $newPollId;
            $channel->sender = $tChannel->sender;
            $channel->sender_name = $tChannel->sender_name;
            $channel->image = $tChannel->image;
            $channel->subject = $tChannel->subject;
            $channel->text = $tChannel->text;
            $channel->delay_days = $tChannel->delay_days;
            $channel->code = $tChannel->code;
            $channel->pool_id = $tChannel->pool_id;
            $channel->sender_id = $tChannel->sender_id;
            $channel->processing_time_in_minutes = $tChannel->processing_time_in_minutes;
            $channel->passed_trigger = $tChannel->passed_trigger;
            $channel->link = $tChannel->link;
            $channel->payload = $tChannel->payload;

            if($channel->save()) {
                foreach($tChannel->repeats as $tRepeat) {
                    $repeat = new Repeat();
                    $repeat->channel_id = $channel->id;
                    $repeat->delay = $tRepeat->delay;
                    $repeat->image = $tRepeat->image;
                    $repeat->text = $tRepeat->text;
                    $repeat->delay_days = $tRepeat->delay_days;
                    $repeat->code = $tRepeat->code;
                    $repeat->pool_id = $tRepeat->pool_id;
                    $repeat->save();
                }
            }
        }
    }

    public function copyPoints($newPollId)
    {
        $conditionLinks = [];
        foreach($this->pointConditions as $condition)
        {
            $newCondition = new FoquzPointCondition();
            $newCondition->foquz_poll_id = $newPollId;
            $newCondition->order_type_id = $condition->order_type_id;
            $newCondition->order_source = $condition->order_source;
            $newCondition->save();
            $conditionLinks[$condition->id] = $newCondition->id;
        }
        foreach($this->pointSelected as $ps) {
            $newPS = new FoquzPointSelected();
            $newPS->foquz_point_item_id = $ps->foquz_point_item_id;
            $newPS->foquz_poll_id = $newPollId;
            $newPS->condition_id = $ps->condition_id ? $conditionLinks[$ps->condition_id] : null;
            $newPS->save();
        }
    }

    public function getMinPoints()
    {
        if(!$this->point_system)
            return null;
        $questionsWithPoint = $this->getFoquzQuestions()
            ->where(['in', 'main_question_type', [
                FoquzQuestion::TYPE_VARIANTS,
                FoquzQuestion::TYPE_DATE,
                FoquzQuestion::TYPE_PRIORITY,
                FoquzQuestion::TYPE_CHOOSE_MEDIA,
                FoquzQuestion::TYPE_SIMPLE_MATRIX
            ]])->all();
        $min = 0;
        foreach($questionsWithPoint as $question) {
            $min+= $question->minPoints;
        }
        return $min;
    }

    public function calculatePoints()
    {
        if(!$this->point_system)
            return null;
        $questionsWithPoint = $this->getFoquzQuestions()
            ->where(['in', 'main_question_type', [
                FoquzQuestion::TYPE_VARIANTS,
                FoquzQuestion::TYPE_DATE,
                FoquzQuestion::TYPE_PRIORITY,
                FoquzQuestion::TYPE_CHOOSE_MEDIA,
                FoquzQuestion::TYPE_SIMPLE_MATRIX
            ]])->andWhere(["is_deleted"=>0])->all();
        $max = 0;
        foreach($questionsWithPoint as $question) {
            $max+= $question->maxPoints;
        }
        return $max;
    }

    public function calcAvgPoints()
    {
        if(isset(Yii::$app->user->identity) && Yii::$app->user->identity->isFilialEmployee() && Yii::$app->user->identity->getUserFilials()->count() > 0) {
            $filials = ArrayHelper::getColumn(Yii::$app->user->identity->userFilials, 'filial_id');
            $result = (new Query())
                ->select(new Expression('ROUND(AVG(foquz_poll_answer.points)), ROUND(AVG(foquz_poll_answer.points/foquz_poll_answer.max_points*100),2)'))
                ->from('foquz_poll_answer')
                ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_poll_answer.id')
                ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
                ->where(['foquz_poll_id' => $this->id])
                ->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)', $filials])
                ->one();
        } else {
            $result = (new Query())
                ->select(new Expression('ROUND(AVG(points)), ROUND(AVG(points/max_points*100),2)'))
                ->from('foquz_poll_answer')
                ->where(['foquz_poll_id' => $this->id])
                ->one();
        }
        [$this->avgPoints, $this->avgPercent] = array_values($result);
    }

    public function getIntermediateCodes($answer)
    {
        $codes = [];
        foreach($this->foquzQuestions as $question) {
            if($question->main_question_type === FoquzQuestion::TYPE_INTERMEDIATE_BLOCK && $question->intermediateBlock) {
                $codes[$question->id] = $question->intermediateBlock->getCodeString($answer);
            }
        }
        return $codes;
    }

    public function addUtmToPages(?FoquzPollAnswer $answer) : void
    {
        if ($answer === null || $answer->id === null) {
            return;
        }

        if ($this->startPage) {
            $this->startPage->name = $this->addUtmToLinks($this->startPage->name, $answer->id);
            $this->startPage->description = $this->addUtmToLinks($this->startPage->description, $answer->id);
        }

        if ($this->endPage) {
            $this->endPage->name = $this->addUtmToLinks($this->endPage->name, $answer->id);
            $this->endPage->description = $this->addUtmToLinks($this->endPage->description, $answer->id);
        }
    }

    public function addUtmToQuestions($questions, ?FoquzPollAnswer $answer) : void
    {
        if ($answer === null || $answer->id === null) {
            return;
        }
        /** @var FoquzQuestion $question */
        foreach ($questions as $question) {
            if($question->intermediateBlock) {
                $question->intermediateBlock->text = $this->addUtmToLinks($question->intermediateBlock->text, $answer->id);
                $question->intermediateBlock->external_link = $this->addUtmToLink($question->intermediateBlock->external_link, $answer->id);
            }
            foreach($question->foquzQuestionEndScreenLogos as $endScreenLogo) {
                $endScreenLogo->link = $this->addUtmToLink($endScreenLogo->link, $answer->id);
            }
        }
    }

    public function checkCodesExpiration() : bool
    {
        foreach ($this->foquzQuestions as $question) {
            $intermediateBlock = $question->intermediateBlock;
            if ($intermediateBlock && $intermediateBlock->pool_id !== null && $intermediateBlock->pool->isExpired()) {
                return false;
            }
        }

        foreach ($this->pollCodes as $code) {
            if ($code->pool && $code->pool->isExpired()) {
                return false;
            }
        }

        return true;
    }

    public function getSheetHeader(array $columns): array
    {
        if (($index = array_search('points', $columns)) !== false) {
            unset($columns[$index]);
        }
        if (($index = array_search('comments', $columns)) !== false) {
            unset($columns[$index]);
        }

        $labels = $details = $additionalFields = [];
        foreach (ContactAdditionalField::arrayFields($this->company_id)['additional'] as $field) {
            $additionalFields['client' . $field['id']] = $field['text'];
        }
        foreach ($columns as $value) {
            if (isset(SettingTables::COLUMNS[$value])) {
                $labels[] = SettingTables::COLUMNS[$value];
                $details[] = '';
            }
            if (array_key_exists($value, $additionalFields)) {
                $labels[] = $additionalFields[$value];
                $details[] = '';
            }
        }
        if (!empty($this->company_id)) {
            switch ($this->company_id) {
                case 633:
                    $labels[] = "Менеджер";
                    break;
                case 1462:
                    $labels[] = "OTRS";
                    break;
                case 1055:
                    $labels[] = "Ключ анкеты";
                    break;
            }
            if (in_array($this->company_id, [633, 1462, 1055], true)) {
                $details[] = '';
            }
        }

        if (in_array($this->id, [ 380530, 380531 ])) {
            $labels[] = "Order ID";

        }
        if ($this->id == 262574) { //особый опрос касперского
            $labels[] = "task";
            $labels[] = "reporter";
            $labels[] = "assignee";
            $details[] = '';
            $details[] = '';
            $details[] = '';
        }
        if ($this->id == 253110) { //особый опрос касперского
            $labels[] = "JIRA";
            $labels[] = "engineer";
            $details[] = '';
            $details[] = '';
        }
        if ($this->id === 267388) { //особый опрос касперского
            $labels[] = 'ID компании';
            $details[] = '';
        }

        $questions = $this->getFoquzQuestions();
        if ($this->displaySetting?->type === FoquzPollDisplaySetting::MANUAL_SPLIT) {
            $questions->joinWith(['pollDisplayPageQuestion.displayPage'], false)
                ->orderBy(['foquz_poll_display_pages.order' => SORT_ASC, 'foquz_question.position' => SORT_ASC]);
        }

        foreach ($questions->all() as $question) {
            if ($question->main_question_type !== FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
                $labels[] = $question->description ?: '';
                switch ($question->main_question_type) {
                    case FoquzQuestion::TYPE_VARIANTS:
                        if ($question->variants_element_type) {
                            if ($question->donor) {
                                $recipientQuestionDetails = $question->recipientQuestionDetails;
                                ArrayHelper::multisort($recipientQuestionDetails, ['type', 'position'], [SORT_ASC, SORT_ASC]);
                                foreach ($recipientQuestionDetails as $key => $detail) {
                                    if ($key) {
                                        $labels[] = '';
                                    }
                                    if ($detail->question_detail_id === null && $detail->dictionary_element_id === null) {
                                        $details[] = $question->getMainDonor()->self_variant_text ?: 'Свой вариант';
                                    } elseif ($detail->dictionary_element_id) {
                                        $details[] = DictionaryElement::findOne($detail->dictionary_element_id)->title;
                                    } else {
                                        $details[] = FoquzQuestionDetail::findOne($detail->question_detail_id)->question;
                                    }
                                }
                            } else {
                                $questionDetails = $question->questionDetails;
                                ArrayHelper::multisort($questionDetails, 'position');
                                foreach ($questionDetails as $key => $detail) {
                                    if ($key) {
                                        $labels[] = '';
                                    }
                                    $details[] = $detail->question;
                                }
                            }
                            if ($question->is_self_answer) {
                                $labels[] = '';
                                if ($question->self_variant_text) {
                                    $details[] = $question->self_variant_text;
                                } else {
                                    $details[] = 'Свой вариант';
                                }
                            }
                            if ($question->skip) {
                                $labels[] = '';
                                $details[] = $question->skip_text ?: 'Затрудняюсь ответить';
                            }
                        } else {
                            $details[] = '';
                        }
                        break;
                    case FoquzQuestion::TYPE_FILIAL:
                        if ($question->comment_enabled) {
                            $labels[] = '';
                            $details[] = '';
                            $details[] = 'Комментарий';
                        } else {
                            $details[] = '';
                        }
                        break;
                    case FoquzQuestion::TYPE_VARIANT_STAR:
                    case FoquzQuestion::TYPE_PRIORITY:
                        if ($question->donor) {
                            $recipientQuestionDetails = $question->recipientQuestionDetails;
                            ArrayHelper::multisort($recipientQuestionDetails, 'position');
                            foreach ($recipientQuestionDetails as $key => $detail) {
                                if ($key) {
                                    $labels[] = '';
                                }
                                if ($detail->question_detail_id === null && $detail->dictionary_element_id === null) {
                                    $details[] = $question->getMainDonor()->self_variant_text ?: 'Свой вариант';
                                } elseif ($detail->dictionary_element_id) {
                                    $details[] = DictionaryElement::findOne($detail->dictionary_element_id)->title;
                                } else {
                                    $detailModel = FoquzQuestionDetail::findOne($detail->question_detail_id);
                                    $details[] = $detailModel ? $detailModel->question : "Свой вариант";
                                }
                                /** @var FoquzQuestionDetail[] $clarifyingQuestions */
                                $clarifyingQuestions = FoquzQuestionDetail::find()
                                    ->where(['foquz_question_id' => $question->id, 'extra_question' => 1])
                                    ->all();
                                if (!empty($question->detail_question) && $detail->need_extra) {
                                    if ($question->variants_element_type  === 2) {
                                        $labels[] = '';
                                        $details[] = $question->detail_question;
                                    } else {
                                        foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                            $labels[] = '';
                                            $details[] = $clarifyingQuestion->question;
                                        }
                                        if ($question->is_self_answer) {
                                            $labels[] = '';
                                            $details[] = $question->self_variant_text ?: 'Свой вариант';
                                        }
                                    }
                                }
                            }
                        } else {
                            $questionDetails = ArrayHelper::index($question->questionDetails, null, 'extra_question');
                            /** @var FoquzQuestionDetail[] $variants */
                            $variants = $questionDetails[0] ?? [];
                            /** @var FoquzQuestionDetail[] $clarifyingQuestions */
                            $clarifyingQuestions = $questionDetails[1] ?? [];
                            foreach ($variants as $key => $detail) {
                                if ($key) {
                                    $labels[] = '';
                                }
                                $details[] = $detail->question;
                                if (!empty($question->detail_question) && $detail->need_extra) {
                                    if ($question->variants_element_type  === 2) {
                                        $labels[] = '';
                                        $details[] = $question->detail_question;
                                    } else {
                                        foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                            $labels[] = '';
                                            $details[] = $clarifyingQuestion->question;
                                        }
                                        if ($question->is_self_answer) {
                                            $labels[] = '';
                                            $details[] = $question->self_variant_text ?: 'Свой вариант';
                                        }
                                    }
                                }
                            }
                        }
                        if ($question->skip && !$question->skip_variant) {
                            $labels[] = '';
                            if ($question->skip_text) {
                                $details[] = $question->skip_text;
                            } elseif ($question->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR) {
                                $details[] = 'Не готов(а) оценить';
                            } else {
                                $details[] = 'Затрудняюсь ответить';
                            }
                        }
                        break;
                    case FoquzQuestion::TYPE_FORM:
                        foreach ($question->formFields as $key => $detail) {
                            if ($key) {
                                $labels[] = '';
                            }
                            $details[] = $detail->name;
                        }
                        break;
                    case FoquzQuestion::TYPE_CHOOSE_MEDIA:
                        if ($question->variants_element_type) {
                            $files = $question->questionFiles;
                            ArrayHelper::multisort($files, 'position');
                            foreach ($files as $key => $detail) {
                                if ($key) {
                                    $labels[] = '';
                                }
                                $details[] = $detail->type === 'image' ? 'Изображение ' . ($key + 1) : 'Видео ' . ($key + 1);
                            }
                            if ($question->skip) {
                                $labels[] = '';
                                $details[] = $question->skip_text ?: 'Затрудняюсь ответить';
                            }
                        } else {
                            $details[] = '';
                        }
                        break;
                    case FoquzQuestion::TYPE_GALLERY_RATING:
                        $count = count($question->questionFiles);
                        for ($i = 1; $i <= $count; $i++) {
                            if ($i > 1) {
                                $labels[] = '';
                            }
                            $details[] = "Файл $i";
                        }
                        if ($question->skip) {
                            $labels[] = '';
                            $details[] = $question->skip_text ?: 'Не готов(а) оценить';
                        }
                        break;
                    case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                        $settings = FoquzQuestionDifferentialRow::find()
                            ->where(['question_id' => $question->id])
                            ->orderBy('position')
                            ->all();
                        foreach ($settings as $key => $setting) {
                            if ($key) {
                                $labels[] = '';
                            }
                            $value = $setting->start_label ?: '#';
                            $value .= ' - ';
                            $value .= $setting->end_label ?: '#';
                            $details[] = $value;
                        }
                        if ($question->skip) {
                            $labels[] = '';
                            $details[] = $question->skip_text ?: 'Не готов(а) оценить';
                        }
                        break;
                    case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                        $settings = json_decode($question->matrix_settings, true);
                        $questionDetails = ArrayHelper::index($question->questionDetails, null, 'extra_question');
                        /** @var FoquzQuestionDetail[] $clarifyingQuestions */
                        $clarifyingQuestions = $questionDetails[1] ?? [];
                        foreach ($settings['rows'] as $key => $label) {
                            if ($key) {
                                $labels[] = '';
                            }
                            $details[] = $label;
                            if (!empty($question->detail_question) && ($question->for_all_rates ||
                                    empty($settings['extra_question']['rows']) || in_array($label, $settings['extra_question']['rows']))) {
                                if ($question->variants_element_type  === 2) {
                                    $labels[] = '';
                                    $details[] = $question->detail_question;
                                } else {
                                    foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                        $labels[] = '';
                                        $details[] = $clarifyingQuestion->question;
                                    }
                                    if ($question->is_self_answer) {
                                        $labels[] = '';
                                        $details[] = $question->self_variant_text ?: 'Свой вариант';
                                    }
                                }
                            }
                        }
                        if ($question->skip && !$question->skip_variant) {
                            $labels[] = '';
                            $details[] = $question->skip_text ?: 'Затрудняюсь ответить';
                        }
                        break;
                    case FoquzQuestion::TYPE_3D_MATRIX:
                        $matrixElements = ArrayHelper::index($question->activeMatrixElements, null, 'type_id');
                        /** @var FoquzQuestionMatrixElement[] $columns */
                        $columns = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN]) ?
                                $matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN] : [];
                        /** @var FoquzQuestionMatrixElement[] $mRows */
                        $mRows = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW]) ?
                                $matrixElements[FoquzQuestionMatrixElement::TYPE_ROW] : [];
                        foreach ($mRows as $key => $mRow) {
                            if ($key) {
                                $labels[] = '';
                            }
                            $details[] = $mRow->name;
                            foreach ($columns as $column) {
                                $labels[] = '';
                                $details[] = $column->name;
                            }
                        }
                        if ($question->skip && !$question->skip_variant) {
                            $labels[] = '';
                            $details[] = $question->skip_text ?: 'Затрудняюсь ответить';
                        }
                        break;
                    case FoquzQuestion::TYPE_RATING:
                    case FoquzQuestion::TYPE_STAR_RATING:
                    case FoquzQuestion::TYPE_SMILE_RATING:
                        if ($question->detail_question) {
                            if ($question->variants_element_type === 1) {
                                $details[] = '';
                                $foquzQuestionDetails = FoquzQuestionDetail::find()
                                    ->where(['foquz_question_id' => $question->id])
                                    ->orderBy('position')
                                    ->all();
                                foreach ($foquzQuestionDetails as $detail) {
                                    $labels[] = '';
                                    $variantUv = $detail->question;
                                    if (empty($variantUv)) {
                                        $variantUv = 'Вариант ' . $detail->position;
                                    }
                                    $details[] = $variantUv;
                                }
                                if ($question->is_self_answer) {
                                    $labels[] = '';
                                    $details[] = $question->self_variant_text ?: 'Свой вариант';
                                }
                            } else {
                                $labels[] = '';
                                $details[] = '';
                                $details[] = $question->detail_question;
                            }
                        } else {
                            $details[] = '';
                        }
                        break;
                    case FoquzQuestion::TYPE_ASSESSMENT:
                        $foquzQuestionDetails = FoquzQuestionDetail::find()
                            ->where(['foquz_question_id' => $question->id])
                            ->orderBy('position')
                            ->all();
                        if ($question->rating_type === 1) { // тип вопроса 5 звезд
                            if ($question->detail_question) { // если есть уточняющий вопрос
                                $labels[] = '';
                                $details[] = '';
                                if ($question->variants_element_type) { // выбор нескольких вариантов
                                    foreach ($foquzQuestionDetails as $key => $detail) {
                                        if ($key) {
                                            $labels[] = '';
                                        }
                                        $details[] = $detail->question;
                                    }
                                    if ($question->is_self_answer) {
                                        $labels[] = '';
                                        $details[] = $question->self_variant_text ?: 'Свой вариант';
                                    }
                                } else {
                                    $details[] = $question->detail_question;
                                }
                            } else {
                                $details[] = '';
                            }
                        } else if ($question->variants_element_type) {
                            foreach ($foquzQuestionDetails as $key => $detail) {
                                if ($key) {
                                    $labels[] = '';
                                }
                                $details[] = $detail->question;
                            }
                            if ($question->is_self_answer) {
                                $labels[] = '';
                                $details[] = $question->self_variant_text ?: 'Свой вариант';
                            }
                        } else {
                            $details[] = '';
                        }
                        break;
                    case FoquzQuestion::TYPE_NPS_RATING:
                        if (!$question->set_variants) {
                            $details[] = '';
                            break;
                        }
                        /** @var FoquzQuestionDetail[] $clarifyingQuestions */
                        $clarifyingQuestions = ArrayHelper::index($question->questionDetails, null, 'extra_question')[1] ?? [];
                        if ($question->donor) {
                            $recipientQuestionDetails = $question->recipientQuestionDetails;
                            ArrayHelper::multisort($recipientQuestionDetails, 'position');
                            foreach ($recipientQuestionDetails as $key => $detail) {
                                if ($key) {
                                    $labels[] = '';
                                }
                                if ($detail->question_detail_id === null && $detail->dictionary_element_id === null) {
                                    $details[] = $question->getMainDonor()->self_variant_text ?: 'Свой вариант';
                                } elseif ($detail->dictionary_element_id) {
                                    $details[] = DictionaryElement::findOne($detail->dictionary_element_id)->title;
                                } else {
                                    $details[] = FoquzQuestionDetail::findOne($detail->question_detail_id)->question;
                                }
                                if (
                                    !empty($question->detail_question) &&
                                    $detail->need_extra &&
                                    $question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_COMMON_FOR_EACH
                                ) {
                                    if ($question->variants_element_type  === 2) {
                                        $labels[] = '';
                                        $details[] = $question->detail_question;
                                    } else {
                                        foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                            $labels[] = '';
                                            $details[] = $clarifyingQuestion->question;
                                        }
                                        if ($question->is_self_answer) {
                                            $labels[] = '';
                                            $details[] = $question->self_variant_text ?: 'Свой вариант';
                                        }
                                    }
                                }
                            }
                        } else {
                            $questionDetails = ArrayHelper::index($question->questionDetails, null, 'extra_question');
                            /** @var FoquzQuestionDetail[] $variants */
                            $variants = $questionDetails[0] ?? [];
                            /** @var FoquzQuestionDetail[] $clarifyingQuestions */
                            $clarifyingQuestions = $questionDetails[1] ?? [];
                            foreach ($variants as $key => $detail) {
                                if ($key) {
                                    $labels[] = '';
                                }
                                $details[] = $detail->question;
                                if (
                                    !empty($question->detail_question) &&
                                    $detail->need_extra &&
                                    $question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_COMMON_FOR_EACH
                                ) {
                                    if ($question->variants_element_type  === 2) {
                                        $labels[] = '';
                                        $details[] = $question->detail_question;
                                    } else {
                                        foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                            $labels[] = '';
                                            $details[] = $clarifyingQuestion->question;
                                        }
                                        if ($question->is_self_answer) {
                                            $labels[] = '';
                                            $details[] = $question->self_variant_text ?: 'Свой вариант';
                                        }
                                    }
                                }
                                if ($question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_DIFFERENT_EACH) {
                                    if (!empty($detail->detail_question)) {
                                        $extraQuestionService = new ExtraQuestionService();
                                        $variantsForEq = $extraQuestionService->getVariantsForExtraQuestion($detail->id);
                                        if ($variantsForEq) {
                                            foreach ($variantsForEq as $variantEq) {
                                                $labels[] = '';
                                                $details[] = $variantEq->question;
                                            }
                                        }
                                        if ($detail->is_self_answer) {
                                            $labels[] = '';
                                            $details[] = 'свой вариант';
                                        }
                                    }
                                }
                            }
                        }
                        if (
                            !empty($question->detail_question) &&
                            $question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_COMMON
                        ) {
                            if ($question->variants_element_type !== FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT) {
                                foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                    $labels[] = '';
                                    $details[] = $clarifyingQuestion->question;
                                }
                                if ($question->is_self_answer) {
                                    $labels[] = '';
                                    $details[] = $question->self_variant_text ?: 'Свой вариант';
                                }
                            } else {
                                $labels[] = '';
                                $details[] = $question->detail_question;
                            }
                        }
                        if ($question->skip) {
                            $labels[] = '';
                            if ($question->skip_text) {
                                $details[] = $question->skip_text;
                            } else {
                                $details[] = 'Не готов(а) оценить';
                            }
                        }
                        break;
                    case FoquzQuestion::TYPE_CARD_SORTING_CLOSED:
                        $labelsDetails = (new CardSortingClosed())->getAnswerSheetHeaderExport2($question, $labels, $details);
                        $labels = $labelsDetails['labels'];
                        $details = $labelsDetails['details'];
                        break;
                    case FoquzQuestion::TYPE_FIRST_CLICK:
                        $labelsDetails = (new FirstClick())->getAnswerSheetHeaderExport2($question, $labels, $details);
                        $labels = $labelsDetails['labels'];
                        $details = $labelsDetails['details'];
                        break;
                    default:
                        $details[] = '';
                }
                if (
                    (($question->main_question_type !== FoquzQuestion::TYPE_VARIANTS && $question->is_self_answer) || $question->comment_enabled ||
                    ($question->main_question_type === FoquzQuestion::TYPE_VARIANTS && $question->comment_enabled)) &&
                    (!$question->detail_question || $question->main_question_type === FoquzQuestion::TYPE_DICTIONARY) &&
                    !($question->main_question_type === FoquzQuestion::TYPE_ASSESSMENT && $question->rating_type === 2)
                ) {
                    $labels[] = '';
                    $details[] = 'Комментарий';
                }
            }
        }

        return [$labels, $details];
    }

    public function getSheetRows($params, array $columns2, $forGoogle = 0, $user_id = null, $company_id = null): array
    {
        if (($index = array_search('points', $columns2)) !== false) {
            unset($columns2[$index]);
        }
        if (($index = array_search('comments', $columns2)) !== false) {
            unset($columns2[$index]);
        }
        if (($index = array_search('mode', $columns2)) !== false) {
            unset($columns2[$index]);
        }

        if ($user_id) {
            $searchService = AnswerSearchService::getInstanceByUser($user_id);
        } else {
            $searchService = AnswerSearchService::getInstanceByCompany($company_id);
        }
        $searchService->applyParams($params);
        $data = ArrayHelper::getColumn($searchService->all(), "id");

        /** @var FoquzPollAnswer[] $answers */
        $answers = FoquzPollAnswer::find()
            ->where(["id"=>$data])
            ->with([
                'foquzPoll', 'answerChannel', 'pollLang', 'foquzAnswer',
                'answerFilial', 'order', 'contact', 'processing' , 'mailingListSend.mailingListContact', 'tags',
            ])
            ->all();
        $rows = $processingStatusSet = [];
        $rowCounter = 5;

        $answers = ArrayHelper::index($answers, 'id');
        $sortedAnswers = [];
        foreach ($data as $answer) {
            if (!isset($answers[$answer])) {
                continue;
            }
            $sortedAnswers[] = $answers[$answer];
        }

        foreach ($sortedAnswers as $foquzPollAnswer) {
            if (!$foquzPollAnswer) {

                throw new NotFoundHttpException('Poll answer not found');
            }
            $row = [];

            foreach ($columns2 as $keyColumn => $column) {
                $v=$foquzPollAnswer->getAnswerValue($column);
                $row[] = $v;
                if ($column === 'processingStatus') {
                    $color = '';
                    switch ($v) {
                        case 'Новая':
                            $color = '#16ceb9';
                            break;
                        case 'В процессе':
                            $color = '#2d99ff';
                            break;
                        case 'Обрабатывается исполнителем':
                            $color = '#caad46';
                            break;
                        case 'Отложена':
                            $color = '#f96261';
                            break;
                        case 'Обработана':
                            $color = '#8400ff';
                            break;
                    }
                    $processingStatusSet[] = [
                        'row' => $rowCounter,
                        'column' => $keyColumn,
                        'color' => $color,
                    ];
                }
            }

            //print_r($columns); exit;
            $rowCounter++;
            if (!empty($this->company_id)) {
                switch ($this->company_id) {
                    case 633:
                        $row[] = $foquzPollAnswer->contact->additionalFieldValues[0]->value ?? '';
                        break;
                    case 1462:
                        $otrs = '';
                        $cf = $foquzPollAnswer->custom_fields;
                        if ($cf) {
                            $cf = @json_decode($cf, true);
                            if (is_array($cf) && isset($cf["OTRS"])) {
                                $otrs = $cf['OTRS'];
                            }
                        }
                        $row[] = $otrs;
                        break;
                    case 1055:
                        $row[] = $foquzPollAnswer->getAnswerKey() ?: '';
                        break;
                }
                if ($this->company_id === 633) {
                    $row[] = $foquzPollAnswer->contact->additionalFieldValues[0]->value ?? '';
                }
            }

            if ($this->id == 262574) { //особый опрос касперского
                $cf = $foquzPollAnswer->custom_fields;
                $task = '';
                $reporter = '';
                $assignee = '';
                if ($cf) {
                    $cf = @json_decode($cf, true);
                    if (is_array($cf)) {
                        if (isset($cf['task'])) $task = $cf["task"];
                        if (isset($cf['reporter'])) $reporter = $cf["reporter"];
                        if (isset($cf['assignee'])) $assignee = $cf["assignee"];
                    }
                }
                $row[] = $task;
                $row[] = $reporter;
                $row[] = $assignee;
            }

            if (in_array($this->id, [380531, 380530])) { //особый опрос касперского
                $row[] = $foquzPollAnswer->getCustomField('order_id');
                //$cf = $foquzPollAnswer->custom_fields;
            }

            if ($this->id == 253110) { //особый опрос касперского
                $cf = $foquzPollAnswer->custom_fields;
                $jira = '';
                $engineer = '';
                if ($cf) {
                    $cf = @json_decode($cf, true);
                    if (is_array($cf)) {
                        if (isset($cf['JIRA'])) $jira = $cf["JIRA"];
                        if (isset($cf['engineer'])) $engineer = $cf["engineer"];
                    }
                }
                $row[] = $jira;
                $row[] = $engineer;
            }
            if ($this->id === 267388) { //особый опрос касперского
                $cf = $foquzPollAnswer->custom_fields;
                $companyID = '';
                if ($cf) {
                    $cf = @json_decode($cf, true);
                    if (is_array($cf) && isset($cf['company_id'])) {
                        $companyID = $cf['company_id'];
                    }
                }
                $row[] = $companyID;
            }

            $questions = $this->getFoquzQuestions();
            if ($this->displaySetting?->type === FoquzPollDisplaySetting::MANUAL_SPLIT) {
                $questions->joinWith(['pollDisplayPageQuestion.displayPage'], false)
                    ->orderBy(['foquz_poll_display_pages.order' => SORT_ASC, 'foquz_question.position' => SORT_ASC]);
            }

            /** @var FoquzQuestion $question */
            foreach ($questions->all() as $question) {
                if ($question->main_question_type !== FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
                    $answerItem = FoquzPollAnswerItem::findOne([
                        'foquz_poll_answer_id' => $foquzPollAnswer->id,
                        'foquz_question_id' => $question->id,
                    ]);
                    $detailItem = $answer = '';
                    if ($answerItem) {
                        $detailItem = is_string($answerItem->detail_item) ?
                            json_decode($answerItem->detail_item, true) : $answerItem->detail_item;
                        $answer = json_decode($answerItem->answer ?? '[]', true);
                        //print_r($detailItem); exit;
                    }
                    $questionDetails = $question->questionDetails;
                    switch ($question->main_question_type) {
                        case FoquzQuestion::TYPE_VARIANTS:
                            $result = [];
                            $found = false;

                            if ($question->donor) {
                                $recipientQuestionDetails = $question->recipientQuestionDetails;
                                ArrayHelper::multisort($recipientQuestionDetails, 'position');
                                $variantsResult = [];
                                foreach ($recipientQuestionDetails as $detail) {
                                    if ($detail->question_detail_id === null && is_array($detailItem) && in_array('-1', $detailItem)) {
                                        $variantsResult[] = $answerItem->getDonorAnswer()->self_variant;
                                    } elseif ($detail->question_detail_id && is_array($detailItem) && in_array($detail->question_detail_id, $detailItem)) {
                                        $variantsResult[] = FoquzQuestionDetail::findOne($detail->question_detail_id)->question;
                                    } elseif ($detail->dictionary_element_id && is_array($detailItem) && in_array($detail->dictionary_element_id, $detailItem)) {
                                        $variantsResult[] = DictionaryElement::findOne($detail->dictionary_element_id)->title;
                                    } else {
                                        $variantsResult[] = '';
                                    }
                                }
                                if ($question->variants_element_type && !empty($variantsResult)) {
                                    $result = ArrayHelper::merge($result, $variantsResult);
                                } elseif (!$question->variants_element_type && !empty($variantsResult)) {
                                    $variantsResult = array_values(array_filter($variantsResult, function ($value) {
                                        return $value !== '';
                                    }));
                                    if (isset($variantsResult[0]) && $variantsResult[0] !== '') {
                                        $result[] = $variantsResult[0];
                                    }
                                }
                            } else {
                                $detailItem = ($detailItem && is_array($detailItem)) ? array_flip($detailItem) : $detailItem;
                                ArrayHelper::multisort($questionDetails, 'position');
                                foreach ($questionDetails as $detail) {
                                    if ($question->variants_element_type) {
                                        $result[] = (isset($detailItem[$detail->id])) ? $detail->question : '';
                                    } else if (isset($detailItem[$detail->id])) {
                                        $result[] = $detail->question;
                                        $found = true;
                                        break;
                                    }
                                }
                            }
                            if ($question->is_self_answer) {
                                if ($question->variants_element_type) {
                                    $result[] = $answerItem && $answerItem->self_variant !== null ? $answerItem->self_variant : '';
                                } else if ($answerItem && !$detailItem && $answerItem->self_variant !== null && $answerItem->self_variant !== '') {
                                    $string = $question->self_variant_text ?: 'Свой вариант';
                                    $string .= ': ' . $answerItem->self_variant;
                                    $result[] = $string;
                                    $found = true;
                                }
                            }

                            if (!$question->skip && !$question->variants_element_type && !count($result)) {
                                $result[] = '';
                            }

                            if ($question->skip) {
                                if ($question->variants_element_type) {
                                    if ($answerItem && $answerItem->skipped) {
                                        $result[] = $question->skip_text ?: 'Затрудняюсь ответить';
                                    } else {
                                        $result[] = '';
                                    }
                                } else if ($answerItem && $answerItem->skipped) {
                                    $result[] = $question->skip_text ?: 'Затрудняюсь ответить';
                                } elseif (!count($result)) {
                                      $result[] = '';
                                }
                            }

                            if ($question->comment_enabled) {
                                $result[] = $answerItem && $answerItem->answer ? $answerItem->answer : '';
                            }
                            $row = ArrayHelper::merge($row, $result);
                            break;
                        case FoquzQuestion::TYPE_FILIAL:
                            if (is_array($detailItem)) {
                                $detailItem = $detailItem[0];
                            }
                            $filial = Filial::findOne($detailItem);
                            if ($filial) {
                                $row[] = $filial->name;
                            } else {
                                if ($answerItem && $answerItem->skipped) {
                                    $row[] = $question->skip_text ?: 'Затрудняюсь ответить';
                                } else {
                                    $row[] = '';
                                }
                            }
                            if ($question->comment_enabled) {
                                $row[] = !empty($answerItem->self_variant) ? $answerItem->self_variant : '';
                            }
                            break;
                        case FoquzQuestion::TYPE_PRIORITY:
                            if ($detailItem && !$question->donor) {

                                $answer = [];
                                foreach ($detailItem as $i=>$id) $answer[$id] = $i;
                                //$answer = $detailItem;
                                $questionDetails = ArrayHelper::getColumn($questionDetails, 'id');
                                foreach ($questionDetails as $key => $questionDetail) {
                                    if (isset($answer[$questionDetail])) {
                                        $row[] = $answer[$questionDetail] + 1;
                                    } else {
                                        $row[] = '';
                                    }
                                }
                            } else if ($detailItem && is_array($detailItem) && $question->donor &&
                                $question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY
                            ) {
                                $questionDetails = $question->recipientQuestionDetails;
                                ArrayHelper::multisort($questionDetails, 'position');
                                foreach ($questionDetails as $questionDetail) {
                                    if (array_search($questionDetail->question_detail_id, $detailItem) !== false) {
                                        $row[] = array_search($questionDetail->question_detail_id, $detailItem) + 1;
                                    } elseif (!$questionDetail->question_detail_id && array_search('-1', $detailItem) !== false) {
                                        $row[] = array_search('-1', $detailItem) + 1;
                                    } else {
                                        $row[] = '';
                                    }
                                }
                            } elseif ($detailItem && is_array($detailItem) && $question->donor) {
                                $questionDetails = $question->recipientQuestionDetails;
                                foreach ($questionDetails as $questionDetail) {
                                    if (array_search($questionDetail->dictionary_element_id, $detailItem) !== false) {
                                        $row[] = array_search($questionDetail->dictionary_element_id, $detailItem) + 1;
                                    } elseif (!$questionDetail->dictionary_element_id && array_search('-1', $detailItem) !== false) {
                                        $row[] = array_search('-1', $detailItem) + 1;
                                    } else {
                                        $row[] = '';
                                    }
                                }

                            } else {
                                $questionDetails = ArrayHelper::getColumn($questionDetails, 'id');
                                foreach ($questionDetails as $key => $questionDetail) {
                                    if (isset($detailItem[$key])) {
                                        $row[] = array_search($detailItem[$key], $questionDetails) + 1;
                                    } else {
                                        $row[] = '';
                                    }
                                }
                            }
                            break;
                        case FoquzQuestion::TYPE_VARIANT_STAR:
                            $skipText = $question->skip_text ?: 'Не готов(а) оценить';
                            if ($question->donor) {
                                $questionDetails = $question->recipientQuestionDetails;
                                ArrayHelper::multisort($questionDetails, 'position');
                                foreach ($questionDetails as $questionDetail) {
                                    $answerWithoutExtra = $answer;
                                    if (empty($questionDetail->question_detail_id) && empty($questionDetail->dictionary_element_id)) {
                                        $questionDetail->question_detail_id = -1;
                                    }
                                    if (is_array($answerWithoutExtra) && isset($answerWithoutExtra['extra'])) {
                                        unset($answerWithoutExtra['extra']);
                                    }
                                    if (is_array($answerWithoutExtra) && $questionDetail->question_detail_id === null && $questionDetail->dictionary_element_id === null && array_key_exists('-1', $answerWithoutExtra)) {
                                        $row[] = $answerWithoutExtra['-1'];
                                    } elseif (
                                        is_array($answerWithoutExtra) &&
                                        (
                                            array_key_exists($questionDetail->question_detail_id, $answerWithoutExtra) ||
                                            array_key_exists($questionDetail->dictionary_element_id, $answerWithoutExtra)
                                        )
                                    ) {
                                        $value = $answerWithoutExtra[$questionDetail->question_detail_id ?: $questionDetail->dictionary_element_id];
                                        if (empty($value) && !$question->skip_variant) {
                                            $value = '';
                                        } elseif ($value === 'null' || $value === '') {
                                            $value = $skipText;
                                        }
                                        $row[] = $value;
                                    } else {
                                        $row[] = '';
                                    }
                                    /** @var FoquzQuestionDetail[] $clarifyingQuestions */
                                    $clarifyingQuestions = FoquzQuestionDetail::find()
                                        ->where(['foquz_question_id' => $question->id, 'extra_question' => 1])
                                        ->all();
                                    if (!empty($question->detail_question) && $questionDetail->need_extra) {
                                        if ($question->variants_element_type  === 2) {
                                            if (empty($questionDetail->question_detail_id) && empty($questionDetail->dictionary_element_id)) {
                                                $questionDetail->question_detail_id = -1;
                                            }
                                            if (!empty($answer['extra'][$questionDetail->question_detail_id ?: $questionDetail->dictionary_element_id]['answer']))
                                            {
                                                $row[] = $answer['extra'][$questionDetail->question_detail_id ?: $questionDetail->dictionary_element_id]['answer'];
                                            } else {
                                                $row[] = '';
                                            }
                                        } else {
                                            foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                                if (!empty($answer['extra'][$questionDetail->question_detail_id]) && in_array($clarifyingQuestion->id, $answer['extra'][$questionDetail->question_detail_id])) {
                                                    $row[] = $clarifyingQuestion->question;
                                                } else {
                                                    $row[] = '';
                                                }
                                            }
                                            if ($question->is_self_answer) {
                                                if (empty($questionDetail->question_detail_id)) {
                                                    $questionDetail->question_detail_id = -1;
                                                }
                                                if (!empty($answer['extra'][$questionDetail->question_detail_id]['self_variant'])) {
                                                    $row[] = $answer['extra'][$questionDetail->question_detail_id]['self_variant'];
                                                } else {
                                                    $row[] = '';
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                $questionDetails = ArrayHelper::index($question->questionDetails, null, 'extra_question');
                                $variants = $questionDetails[0] ?? [];
                                /** @var FoquzQuestionDetail[] $variants */
                                $variants = ArrayHelper::index($variants, 'id');
                                /** @var FoquzQuestionDetail[] $clarifyingQuestions */
                                $clarifyingQuestions = $questionDetails[1] ?? [];
                                foreach ($variants as $key => $variant) {
                                    $value = $answer[$key] ?? '';
                                    if (($value === 'null' || $value === '') && !$question->skip_variant) {
                                        $value = '';
                                    } elseif ($value === 'null' || $value === '') {
                                        $value = $skipText;
                                    }
                                    if ($value === '-1') {
                                        $value = '';
                                    }
                                    $row[] = $value ?: '';
                                    if (!empty($question->detail_question) && $variant->need_extra) {
                                        if ($question->variants_element_type  === 2) {
                                            if (!empty($answer['extra'][$key]['answer']))
                                            {
                                                $row[] = $answer['extra'][$key]['answer'];
                                            } else {
                                                $row[] = '';
                                            }
                                        } else {
                                            foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                                if (!empty($answer['extra'][$key]) && in_array($clarifyingQuestion->id, $answer['extra'][$key])) {
                                                    $row[] = $clarifyingQuestion->question;
                                                } else {
                                                    $row[] = '';
                                                }
                                            }
                                            if ($question->is_self_answer) {
                                                if (!empty($answer['extra'][$key]['self_variant'])) {
                                                    $row[] = $answer['extra'][$key]['self_variant'];
                                                } else {
                                                    $row[] = '';
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            if ($question->skip && !$question->skip_variant) {
                                if ($answerItem && $answerItem->skipped) {
                                    $row[] = $skipText;
                                } else {
                                    $row[] = '';
                                }
                            }
                            break;
                        case FoquzQuestion::TYPE_FORM:
                            //$row[]='';
                            //break;
                        //die('22');
                            $questionDetails = FoquzQuestionFormField::find()
                                ->where(['question_id' => $question->id])
                                ->orderBy('position')
                                ->all();
                            foreach ($questionDetails as $questionDetail) {
                                if ($answer && isset($answer[$questionDetail->id])) {
                                    //print($questionDetail->mask_type); exit;
                                    switch ($questionDetail->mask_type) {
                                        case 5:
                                          case 0:
                                            $result = '';
                                            if ($fio = $answer[$questionDetail->id]) {
                                                if (is_array($fio)) {
                                                    $result = [];
                                                    $result[] = $fio['surname'] ?? '';
                                                    $result[] = $fio['name'] ?? '';
                                                    $result[] = $fio['patronymic'] ?? '';
                                                    $result = trim(implode(' ', $result));
                                                } else {
                                                    $result = $answer[$questionDetail->id];
                                                }
                                            }
                                            $row[] = $result;
                                            break;
                                        case 4:
                                            if ($forGoogle) {
                                                $result = $answer[$questionDetail->id] ?
                                                    GoogleSheet::hyperlink($answer[$questionDetail->id], $answer[$questionDetail->id]) : '';
                                            } else {
                                                $result = $answer[$questionDetail->id] ?: '';
                                            }
                                            $row[] = $result;
                                            break;
                                        case 1:
                                            $row[] = $answer[$questionDetail->id] ? trim($answer[$questionDetail->id], '+') : '';
                                            break;
                                        default:
                                            $row[] = $answer[$questionDetail->id] !== false ? $answer[$questionDetail->id] : '';
                                    }
                                } else {
                                    $row[] = '';
                                }
                            }
                            break;
                        case FoquzQuestion::TYPE_CHOOSE_MEDIA:
                            $questionDetails = $question->questionFiles;
                            ArrayHelper::multisort($questionDetails, 'position');
                            $type = $questionDetails[0]->type;
                            if ($question->variants_element_type) {
                                foreach ($questionDetails as $key => $questionDetail) {
                                    if ($answer && in_array($questionDetail->id, $answer)) {
                                        $row[] = $type === 'image' ? 'Изображение ' . ($key + 1) : 'Видео ' . ($key + 1);
                                    } else {
                                        $row[] = '';
                                    }
                                }
                                if ($question->skip) {
                                    if ($answerItem && $answerItem->skipped) {
                                        $row[] = $question->skip_text ?: 'Затрудняюсь ответить';
                                    } else {
                                        $row[] = '';
                                    }
                                }
                            } elseif ($answerItem && $answerItem->skipped) {
                                $row[] = $question->skip_text ?: 'Затрудняюсь ответить';
                            } else {
                                $questionDetails = ArrayHelper::getColumn($questionDetails, 'id');
                                $result = '';
                                foreach ($questionDetails as $key => $questionDetail) {
                                    if ($answer) {
                                        if (in_array($questionDetail, $answer)) {
                                            $result = $type === 'image' ? 'Изображение ' . ($key + 1) : 'Видео ' . ($key + 1);
                                            break;
                                        }
                                    }
                                }
                                $row[] = $result;
                            }
                            break;
                        case FoquzQuestion::TYPE_GALLERY_RATING:
                            $questionDetails = $question->questionFiles;
                            ArrayHelper::multisort($questionDetails, 'position');
                            foreach ($questionDetails as $questionDetail) {
                                if ($answer && array_key_exists($questionDetail->id, $answer)) {
                                    $row[] = $answer[$questionDetail->id];
                                } else {
                                    $row[] = '';
                                }
                            }
                            if ($question->skip) {
                                if ($answerItem && $answerItem->skipped) {
                                    $row[] = $question->skip_text ?: 'Не готов(а) оценить';
                                } else {
                                    $row[] = '';
                                }
                            }
                            break;
                        case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                            $settings = FoquzQuestionDifferentialRow::find()
                                ->where(['question_id' => $question->id])
                                ->orderBy('position')
                                ->all();
                            foreach ($settings as $setting) {
                                if ($answer && isset($answer[$setting->id])) {
                                    $row[] = $answer[$setting->id];
                                } else {
                                    $row[] = '';
                                }
                            }
                            if ($question->skip) {
                                if ($answerItem && $answerItem->skipped) {
                                    $row[] = $question->skip_text ?: 'Не готов(а) оценить';
                                } else {
                                    $row[] = '';
                                }
                            }
                            break;
                        case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                            $skipText = $question->skip_text ?: 'Затрудняюсь ответить';
                            $settings = json_decode($question->matrix_settings, true);
                            $labels = $settings['rows'];
                            $questionDetails = ArrayHelper::index($question->questionDetails, null, 'extra_question');
                            /** @var FoquzQuestionDetail[] $clarifyingQuestions */
                            $clarifyingQuestions = $questionDetails[1] ?? [];
                            if ($question->donor && is_array($answer)) {
                                $donorAnswer = [];
                                if ($question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                                    $details = ArrayHelper::map(FoquzQuestionDetail::findAll(['id' => array_keys($answer)]), 'id', 'question');
                                } else {
                                    $details = ArrayHelper::map(DictionaryElement::findAll(['id' => array_keys($answer)]), 'id', 'fullPath');
                                }
                                foreach ($answer as $key => $value) {
                                    if (is_array($value)) {
                                        $value = implode("\n", $value);
                                    }
                                    if (($value === 'null' || $value === '') && !$question->skip_variant) {
                                        $value = '';
                                    } elseif ($value === 'null' || $value === '') {
                                        $value = $skipText;
                                    }
                                    if ($key == '-1') {
                                        $donorAnswer[-1] = $value;
                                    } elseif (isset($details[$key])) {
                                        $donorAnswer[$details[$key]] = $value;
                                    }
                                }
                                $answer = $donorAnswer;
                            }
                            foreach ($labels as $label) {
                                $labelForAnswer = $label;
                                if ($question->donor && $label == ($question->getMainDonor()->self_variant_text ?: 'Свой вариант')) {
                                    $labelForAnswer = -1;
                                }
                                $value = !empty($answer[$labelForAnswer])  ? $answer[$labelForAnswer]  : null;
                                if (is_array($value)) {
                                    $value =  @implode("\n", $value);
                                }
                                $value = $value === 'null' || $value === '' ? $skipText : $value;
                                $row[] = $value ?? '';
                                if (!empty($question->detail_question) && ($question->for_all_rates ||
                                        empty($settings['extra_question']['rows']) || in_array($label, $settings['extra_question']['rows']))) {
                                    if ($question->variants_element_type  === 2) {
                                        if (!empty($detailItem[$labelForAnswer]['answer']))
                                        {
                                            $row[] = $detailItem[$labelForAnswer]['answer'];
                                        } else {
                                            $row[] = '';
                                        }
                                    } else {
                                        foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                            if (!empty($detailItem[$labelForAnswer]) && in_array($clarifyingQuestion->id, $detailItem[$labelForAnswer])) {
                                                $row[] = $clarifyingQuestion->question;
                                            } else {
                                                if (isset($detailItem['Свой вариант']) &&
                                                    is_array($detailItem['Свой вариант']) &&
                                                    $labelForAnswer === -1 &&
                                                    in_array($clarifyingQuestion->id, $detailItem['Свой вариант'])
                                                ) {
                                                    $row[] = $clarifyingQuestion->question;
                                                } else {
                                                    $row[] = '';
                                                }
                                            }
                                        }
                                        if ($question->is_self_answer) {
                                            if (!empty($detailItem[$labelForAnswer]['self_variant'])) {
                                                $row[] = $detailItem[$labelForAnswer]['self_variant'];
                                            } else {
                                                $row[] = '';
                                            }
                                        }
                                    }
                                }
                            }
                            if ($question->skip && !$question->skip_variant) {
                                if ($answerItem && $answerItem->skipped) {
                                    $row[] = $question->skip_text ?: 'Затрудняюсь ответить';
                                } else {
                                    $row[] = '';
                                }
                            }
                            break;
                        case FoquzQuestion::TYPE_3D_MATRIX:
                            $skipText = $question->skip_text ?: 'Затрудняюсь ответить';
                            $matrixElements = ArrayHelper::index($question->activeMatrixElements, null, 'type_id');
                            /** @var FoquzQuestionMatrixElement[] $matrixColumns */
                            $matrixColumns = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN]) ?
                                $matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN] : [];
                            /** @var FoquzQuestionMatrixElement[] $mRows */
                            $mRows = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW]) ?
                                $matrixElements[FoquzQuestionMatrixElement::TYPE_ROW] : [];
                            $matrixVariants = FoquzQuestionMatrixElementVariant::find()
                                ->where(['matrix_element_id' => ArrayHelper::getColumn($matrixColumns, 'id')])
                                ->all();
                            /** @var FoquzQuestionMatrixElementVariant[] $matrixVariants */
                            $matrixVariants = ArrayHelper::index($matrixVariants, 'id');
                            foreach ($mRows as $mRow) {
                                $row[] = '';
                                foreach ($matrixColumns as $matrixColumn) {
                                    $value = !empty($answer[$mRow->id][$matrixColumn->id]) ? $answer[$mRow->id][$matrixColumn->id] : null;
                                    if (is_array($value)) {
                                        foreach ($value as $key => $item) {
                                            if (!empty($matrixVariants[$item]->name)) {
                                                $value[$key] = $matrixVariants[$item]->name;
                                            } elseif ($item === '-1') {
                                                $value[$key] = $skipText;
                                            } else {
                                                $value[$key] = '';
                                            }
                                        }
                                        $value = implode("\n", $value);
                                    }
                                    if ($value === 'null') {
                                        $value = '';
                                    } elseif ($value === '-1') {
                                        $value = $skipText;
                                    }
                                    $row[] = $value;
                                }
                            }
                            if ($question->skip && !$question->skip_variant) {
                                if ($answerItem && $answerItem->skipped) {
                                    $row[] = $question->skip_text ?: 'Затрудняюсь ответить';
                                } else {
                                    $row[] = '';
                                }
                            }
                            break;
                        case FoquzQuestion::TYPE_SMILE_RATING:
                            if ($answer) {
                                $smiles = FoquzQuestionSmile::findAll(['foquz_question_id' => $question->id]);
                                foreach ($smiles as $key => $smile) {
                                    if ($answer === $smile->id) {
                                        $row[] = in_array($question->smile_type, [
                                            FoquzQuestion::SMILE_HEART, FoquzQuestion::SMILE_LIKE
                                        ], true) ? (string)$key : $key + 1;
                                        break;
                                    }
                                }
                            } elseif ($answerItem && $answerItem->skipped) {
                                $row[] = $question->skip_text ?: 'Не готов(а) оценить';
                            } else {
                                $row[] = '';
                            }
                            if ($question->detail_question) {
                                $details = FoquzQuestionDetail::find()
                                    ->select(['id', 'question', 'position'])
                                    ->where(['foquz_question_id' => $question->id])
                                    ->orderBy('position')
                                    ->indexBy('id')
                                    ->asArray()->all();
                                if ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_CHECKBOX) {
                                    foreach ($details as $detail) {
                                        $ansUv = '';
                                        if (is_array($answerItem?->detail_item)) {
                                            foreach ($answerItem->detail_item as $detail_item) {
                                                if (!is_array($detail_item)) {
                                                    if ((int)$detail_item === $detail['id']) {
                                                        $ansUv = (empty($detail['question'])) ? 'Вариант ' . $detail['position'] : $detail['question'];;
                                                    }
                                                }
                                            }
                                        }
                                        $row[] = (!empty($ansUv)) ? $ansUv : '';
                                    }
                                    if ($question->is_self_answer) {
                                        $ansUv = '';
                                        if (is_array($answerItem?->detail_item)) {
                                            foreach ($answerItem->detail_item as $key => $detail_item) {
                                                if ($key === 'self_variant') {
                                                    $ansUv = $detail_item;
                                                } else if ($key === 'text_answer') {
                                                    $ansUv = $detail_item;
                                                } else {
                                                    if (is_array($detail_item) && isset($detail_item['self_variant'])) {
                                                        $ansUv = $detail_item['self_variant'];
                                                    }
                                                }
                                            }
                                        }
                                        $row[] = (!empty($ansUv)) ? $ansUv : '';
                                    }

                                } elseif ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT) {
                                    if (isset($answerItem->detail_item['text_answer'])) {
                                        $row[] = $answerItem->detail_item['text_answer'];
                                    } else {
                                        $row[] = '';
                                    }
                                } elseif ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO) {
                                    if (is_array($answerItem?->detail_item)) {
                                        foreach ($answerItem->detail_item as $key => $detail_item) {
                                            if ($key === 'self_variant') {
                                                $row[] = $detail_item;
                                            } else {
                                                if (isset($details[$detail_item])) {
                                                    $row[] = (empty($details[$detail_item]['question'])) ? 'Вариант ' . $details[$detail_item]['position'] : $details[$detail_item]['question'];
                                                } else {
                                                    $row[] = '';
                                                }
                                            }
                                        }
                                    } else {
                                        $row[] = '';
                                    }
                                }
                            }
                            break;
                        case FoquzQuestion::TYPE_RATING:
                        case FoquzQuestion::TYPE_STAR_RATING:
                            if ($answerItem) {
                                if ($answerItem->skipped) {
                                    $row[] = $question->skip_text ?: 'Не готов(а) оценить';
                                } else {
                                    $row[] = $answerItem->rating ?: '';
                                }
                            } else {
                                $row[] = '';
                            }

                            if ($question->detail_question) {
                                $details = FoquzQuestionDetail::find()
                                    ->where(['foquz_question_id' => $question->id])
                                    ->orderBy('position')
                                    ->all();
                                if ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_CHECKBOX) {
                                    foreach ($details as $detail) {
                                        if (is_array($detailItem)) {
                                            $row[] = in_array($detail->id, $detailItem) ? $detail->question : '';
                                        } else {
                                            $row[] = $detail->id == $detailItem ? $detail->question : '';
                                        }
                                    }
                                    if ($answerItem && $question->is_self_answer) {
                                        $row[] = $answerItem->self_variant ?: '';
                                    }
                                } elseif ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT) {
                                    $row[] = $answerItem && $answerItem->answer ? $answerItem->answer : '';
                                } elseif ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO) {
                                    if (!empty($answerItem->detail_item) && !empty(json_decode($answerItem->detail_item)[0])) {
                                        $row[] = FoquzQuestionDetail::find()
                                            ->select('question')
                                            ->where(['id' => json_decode($answerItem->detail_item)[0]])
                                            ->scalar();
                                    } elseif (!empty($answerItem->self_variant)) {
                                        $row[] = $answerItem->self_variant;
                                    } else {
                                        $row[] = '';
                                    }
                                } elseif ($answerItem) {
                                    if ($question->is_self_answer) {
                                        $result = '';
                                        if ($answerItem->self_variant) {
                                            $result = $question->self_variant_text ?: 'Свой вариант';
                                            $result .= ': ';
                                            $result .= $answerItem->self_variant ?: '';
                                        } else {
                                            foreach ($details as $detail) {
                                                if (is_array($detailItem)) {
                                                    if (in_array($detail->id, $detailItem)) {
                                                        $result = $detail->question;
                                                        break;
                                                    }
                                                } else if ($detail->id == $detailItem) {
                                                    $result = $detail->question;
                                                    break;
                                                }
                                            }
                                        }
                                        $row[] = $result ?: '';
                                    } elseif ($answerItem->answer) {
                                        $row[] = $answerItem->answer;
                                        //die('234214');
                                    } else {
                                        $row[] = '';
                                    }
                                } else {
                                    $row[] = '';
                                }
                            }
                            break;
                        case FoquzQuestion::TYPE_ASSESSMENT:
                            if ($answerItem) {
                                $details = FoquzQuestionDetail::find()
                                    ->where(['foquz_question_id' => $question->id])
                                    ->orderBy('position')
                                    ->all();
                                if ($question->rating_type === 1) { // тип оценки 5 звезд
                                    $row[] = $answerItem->rating ?: '';
                                }
                                if ($question->variants_element_type) { // выбор нескольких вариантов
                                    foreach ($details as $detail) {
                                        if (is_array($detailItem)) {
                                            $row[] = in_array($detail->id, $detailItem) ? $detail->question : '';
                                        } else {
                                            $row[] = $detail->id == $detailItem ? $detail->question : '';
                                        }
                                    }
                                    if (
                                        ($question->is_self_answer && $question->detail_question) ||
                                        ($question->is_self_answer && $question->rating_type === 2)
                                    ) {
                                        $row[] = $answerItem->self_variant ?: '';
                                    }
                                } else {
                                    $result = false;
                                    if ($answerItem->is_self_variant || $answerItem->answer !== null) {
                                        $result = $question->self_variant_text ?: 'Свой вариант';
                                        $result .= ': ';
                                        $result .= $answerItem->self_variant !== null ? $answerItem->self_variant : $answerItem->answer;
                                    } else {
                                        foreach ($details as $detail) {
                                            if (is_array($detailItem)) {
                                                if (in_array($detail->id, $detailItem)) {
                                                    $result = $detail->question;
                                                    break;
                                                }
                                            } else {
                                                if ($detail->id == $detailItem) {
                                                    $result = $detail->question;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    $row[] = $result !== false ? $result : '';
                                }
                            } else {
                                $row[] = '';
                            }
                            break;
                        case FoquzQuestion::TYPE_NPS_RATING:
                            if (!$question->set_variants) {
                                if (isset($answerItem->rating) && (string) $answerItem->rating === '-1') {
                                    $row[] = '';
                                } else {
                                    $row[] = isset($answerItem->rating) ? (string) $answerItem->rating : '';
                                }
                                break;
                            }
                            $answer = json_decode($answerItem->answer ?? '', true);
                            if (!is_array($answer)) {
                                $answer = [];
                            }
                            $detailItem = $answerItem->detail_item ?? [];
                            if (!is_array($detailItem)) {
                                $detailItem = [];
                            }
                            $skipText = $question->skip_text ?: 'Не готов(а) оценить';
                            /** @var FoquzQuestionDetail[] $clarifyingQuestions */
                            $clarifyingQuestions = ArrayHelper::index($question->questionDetails, null, 'extra_question')[1] ?? [];
                            if ($question->donor) {
                                $questionDetails = $question->recipientQuestionDetails;
                                ArrayHelper::multisort($questionDetails, 'position');
                                foreach ($questionDetails as $questionDetail) {
                                    if (empty($questionDetail->question_detail_id) && empty($questionDetail->dictionary_element_id)) {
                                        $questionDetail->question_detail_id = -1;
                                    }
                                    if (
                                        $questionDetail->question_detail_id === null &&
                                        $questionDetail->dictionary_element_id === null &&
                                        array_key_exists('-1', $answer)
                                    ) {
                                        $value = $answer['-1'];
                                        if ($value === 'null' || $value === '-1') {
                                            $value = '';
                                        }
                                        $row[] = $value;
                                    } elseif (
                                        array_key_exists($questionDetail->question_detail_id, $answer) ||
                                        array_key_exists($questionDetail->dictionary_element_id, $answer)
                                    ) {
                                        $value = $answer[$questionDetail->question_detail_id ?: $questionDetail->dictionary_element_id];
                                        if ($value === 'null' || $value === '-1') {
                                            $value = '';
                                        }
                                        $row[] = $value;
                                    } else {
                                        $row[] = '';
                                    }
                                    if (
                                        !empty($question->detail_question) &&
                                        $questionDetail->need_extra &&
                                        $question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_COMMON_FOR_EACH
                                    ) {
                                        if ($question->variants_element_type  === 2) {
                                            if (empty($questionDetail->question_detail_id) && empty($questionDetail->dictionary_element_id)) {
                                                $questionDetail->question_detail_id = -1;
                                            }
                                            if (!empty($detailItem[$questionDetail->question_detail_id ?: $questionDetail->dictionary_element_id]['answer']))
                                            {
                                                $row[] = $detailItem[$questionDetail->question_detail_id ?: $questionDetail->dictionary_element_id]['answer'];
                                            } else {
                                                $row[] = '';
                                            }
                                        } else {
                                            foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                                if (!empty($detailItem[$questionDetail->question_detail_id]) && in_array($clarifyingQuestion->id, $detailItem[$questionDetail->question_detail_id])) {
                                                    $row[] = $clarifyingQuestion->question;
                                                } else {
                                                    $row[] = '';
                                                }
                                            }
                                            if ($question->is_self_answer) {
                                                if (empty($questionDetail->question_detail_id)) {
                                                    $questionDetail->question_detail_id = -1;
                                                }
                                                if (!empty($detailItem[$questionDetail->question_detail_id]['self_variant'])) {
                                                    $row[] = $detailItem[$questionDetail->question_detail_id]['self_variant'];
                                                } else {
                                                    $row[] = '';
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                $questionDetails = ArrayHelper::index($question->questionDetails, null, 'extra_question');
                                $variants = $questionDetails[0] ?? [];
                                /** @var FoquzQuestionDetail[] $variants */
                                $variants = ArrayHelper::index($variants, 'id');
                                foreach ($variants as $key => $variant) {
                                    $value = $answer[$key] ?? '';
                                    if ($value === 'null' || $value === '-1') {
                                        $value = '';
                                    }
                                    $row[] = $value ?: '';
                                    if (
                                        !empty($question->detail_question) &&
                                        $variant->need_extra &&
                                        $question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_COMMON_FOR_EACH
                                    ) {
                                        if ($question->variants_element_type  === 2) {
                                            if (!empty($detailItem[$key]['answer']))
                                            {
                                                $row[] = $detailItem[$key]['answer'];
                                            } else {
                                                $row[] = '';
                                            }
                                        } else {
                                            foreach ($clarifyingQuestions as $clarifyingQuestion) {
                                                if (!empty($detailItem[$key]) && in_array($clarifyingQuestion->id, $detailItem[$key])) {
                                                    $row[] = $clarifyingQuestion->question;
                                                } else {
                                                    $row[] = '';
                                                }
                                            }
                                            if ($question->is_self_answer) {
                                                if (!empty($detailItem[$key]['self_variant'])) {
                                                    $row[] = $detailItem[$key]['self_variant'];
                                                } else {
                                                    $row[] = '';
                                                }
                                            }
                                        }
                                    }
                                    if ($question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_DIFFERENT_EACH) {
                                        $extraQuestionService = new ExtraQuestionService();
                                        $variantsForEq = $extraQuestionService->getVariantsForExtraQuestion($key);
                                        if ($variant->variants_element_type !== FoquzQuestion::TYPE_TEXT_ANSWER) {
                                            if ($variantsForEq) {
                                                foreach ($variantsForEq as $variantEq) {
                                                    $row[] = isset($detailItem[$key]) && (in_array($variantEq->id, $detailItem[$key])) ? $variantEq->question : '';
                                                }
                                                if ($variant->is_self_answer) {
                                                    $row[] = $detailItem[$key]['self_variant'] ?? '';
                                                }
                                            }
                                        } else {
                                            $row[] = $detailItem[$key]['answer'] ?? '';
                                        }
                                    }

                                }
                            }
                            /** @var FoquzQuestionDetail[] $extraVariants */
                            $extraVariants = ArrayHelper::index($question->questionDetails, null, 'extra_question')[1] ?? [];
                            if ($question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_COMMON) {
                                    if ($question->variants_element_type !== FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT) {
                                        foreach ($extraVariants as $extraVariant) {
                                            if (in_array($extraVariant->id, $detailItem)) {
                                                $row[] = $extraVariant->question;
                                            } else {
                                                $row[] = '';
                                            }
                                        }
                                        if ($question->is_self_answer) {
                                            $row[] = !empty($answerItem->is_self_variant) ? $answerItem->self_variant : '';
                                        } else {
                                            $row[] = '';
                                        }
                                    } else {
                                        $row[] = !empty($answerItem->is_self_variant) ? $answerItem->self_variant : '';
                                    }
                            }
                            if ($question->skip) {
                                if ($answerItem && $answerItem->skipped) {
                                    $row[] = $skipText;
                                } else {
                                    $row[] = '';
                                }
                            }
                            break;
                        case FoquzQuestion::TYPE_CARD_SORTING_CLOSED:
                            $row = (new CardSortingClosed())->getAnswerSheetRowsExport2($question, $answerItem, $row);
                            break;
                        case FoquzQuestion::TYPE_FIRST_CLICK:
                            $row = (new FirstClick())->getAnswerSheetRowsExport2($question, $answerItem, $row);
                            break;
                        default:
                            $row[] = $answerItem ? $answerItem->getAnswerColumn(true) : '';
                    }
                    if (
                        ($question->is_self_answer || $question->comment_enabled) &&
                        $question->main_question_type !== FoquzQuestion::TYPE_VARIANTS &&
                        (!$question->detail_question || $question->main_question_type === FoquzQuestion::TYPE_DICTIONARY) &&
                        !($question->main_question_type === FoquzQuestion::TYPE_ASSESSMENT && $question->rating_type === 2)
                    ) {
                        if (in_array($question->main_question_type, [
                            FoquzQuestion::TYPE_FILE_UPLOAD,
                            FoquzQuestion::TYPE_STAR_RATING,
                            FoquzQuestion::TYPE_RATING,
                            FoquzQuestion::TYPE_ASSESSMENT,
                            FoquzQuestion::TYPE_FIRST_CLICK,
                        ], true)) {
                            $row[] = ($answerItem && $answerItem->answer !== false) ? $answerItem->answer : '';
                        } else {
                            $row[] = ($answerItem && $answerItem->self_variant !== false) ? $answerItem->self_variant : '';
                        }
                    }
                }
            }

            //foreach ($row as $k=>$v) $row[$k]=preg_replace("@^=@", "", $v);
            $rows[] = $row;
        }

        //die('45');
        return ['items' => $rows, 'processingStatusSet' => $processingStatusSet];
    }

    public function hasDictionaryLinks(): bool
    {
        return FoquzQuestion::find()
            ->leftJoin('foquz_question_detail', 'foquz_question_detail.foquz_question_id = foquz_question.id')
            ->where(['poll_id' => $this->id, 'foquz_question.is_deleted' => 0])
            ->andWhere(['OR',
                ['NOT', ['foquz_question.dictionary_element_id' => null]],
                ['NOT', ['foquz_question_detail.dictionary_element_id' => null]],
                ['>', 'json_length(foquz_question.matrix_settings->"$.rows_dictionary")', 0]
            ])
            ->exists();
    }

    public function dictionarySelectDisabled(): bool
    {
        return !Dictionary::find()
            ->andWhere(['company_id' => $this->company_id])
            ->andWhere(['deleted' => 0, 'is_active' => 1])
            ->exists();
    }

    public static function recalcStat($ID): void
    {
        $data = self::find()
            ->select(['a.status', 'c' => 'SUM(IF(a.id IS NOT NULL,1,0))', 'id2' => 'foquz_poll.id'])
            ->leftJoin('foquz_poll_answer a',  'a.foquz_poll_id = foquz_poll.id')
            ->where(['foquz_poll.id' => $ID])
            ->groupBy(['foquz_poll.id', 'a.status'])
            ->asArray()
            ->all();

        $dataProcessing = self::find()
            ->select(['p.status', 'c' => 'SUM(IF(p.id IS NOT NULL,1,0))', 'id2' => 'foquz_poll.id'])
            ->leftJoin('foquz_poll_answer a',  'a.foquz_poll_id = foquz_poll.id')
            ->leftJoin('foquz_poll_answer_processings p',  'p.poll_answer_id = a.id')
            ->where(['foquz_poll.id' => $ID])
            ->andWhere(['a.status' => [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE]])
            ->groupBy(['foquz_poll.id', 'p.status'])
            ->asArray()
            ->all();

        $data = ArrayHelper::index($data, null,'id2');
        $dataProcessing = ArrayHelper::index($dataProcessing, null,'id2');

        foreach ($data as $id => $item) {
            $item = ArrayHelper::map($item, 'status', 'c');
            $itemProcessing = ArrayHelper::map($dataProcessing[$id] ?? [], 'status', 'c');

            self::updateAll([
                'sent_answers_count' => ($item[FoquzPollAnswer::STATUS_NEW] ?? 0) + ($item[FoquzPollAnswer::STATUS_EMAIL_OPEN] ?? 0),
                'opened_answers_count' => $item[FoquzPollAnswer::STATUS_OPEN] ?? 0,
                'in_progress_answers_count' => $item[FoquzPollAnswer::STATUS_IN_PROGRESS] ?? 0,
                'filled_answers_count' => $item[FoquzPollAnswer::STATUS_DONE] ?? 0,
                'processing_new_answers_count' => $itemProcessing[FoquzPollAnswerProcessing::STATUS_NEW] ?? 0,
                'processing_inprocess_answers_count' => $itemProcessing[FoquzPollAnswerProcessing::STATUS_IN_PROCESS] ?? 0,
                'processing_work_answers_count' => $itemProcessing[FoquzPollAnswerProcessing::STATUS_WORK] ?? 0,
                'processing_delayed_answers_count' => $itemProcessing[FoquzPollAnswerProcessing::STATUS_DELAYED] ?? 0,
                'processing_done_answers_count' => $itemProcessing[FoquzPollAnswerProcessing::STATUS_DONE] ?? 0,
            ], ['id' => $id]);
        }

        $polls = self::findAll($ID);
        foreach ($polls as $poll) {
            if  ($poll->isAnswerLimit !== (bool)$poll->is_answer_limited) {
                $poll->is_answer_limited = $poll->isAnswerLimit;
                $poll->save();
            }
        }
    }

    public function clearStatCache(): void
    {
        foreach ($this->foquzQuestions as $question) {
            Yii::$app->cache->delete(['question_stat', 'id' => $question->id]);
        }
    }

    /**
     * Проверяет, ограничено ли получение новых ответов для опроса
     * @return bool
     */
    public function getIsAnswerLimit(): bool
    {
        $answerCount = $this->in_progress_answers_count + $this->filled_answers_count;
        return
            (!$this->is_published && $answerCount >= self::ACCESS_TEST_ANSWERS) ||
            ($this->is_published && $this->limit_count && $answerCount >= $this->limit_count);
    }
}
