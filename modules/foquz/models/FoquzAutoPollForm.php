<?php

namespace app\modules\foquz\models;


use app\helpers\DateTimeHelper;
use app\modules\foquz\controllers\api\PollController;
use yii\base\Model;
use yii\db\Exception;
use yii\helpers\ArrayHelper;
use yii\helpers\HtmlPurifier;

/**
 * Class FoquzAutoPollForm
 * @package app\modules\foquz\models
 */
class FoquzAutoPollForm extends Model
{
    const SCENARIO_AUTO_POLL = 'auto_poll';

    public $trigger;
    public $triggerTime;
    public $triggerDays;
    public $triggerOrders;
    public $dateRange;
    public $goalCount;
    public $limit_count;
    public $goal;
    public $endOffPoll;
    public $notificationScript;
    public $kioskMode;
    public $send_time;
    public $title;
    public $point_system;
    public $dont_send_if_passed;
    public $dont_send_if_passed_link;
    public $dont_send_if_promocode_used;
    public $triggerSetting;
    public $personal_data;
    public $time_to_pass;
    public $time_to_restart;
    public $datetime_start;
    public $datetime_end;
    public $show_foquz_link;
    public $processing_time_in_minutes;
    public $processing_time_by_link_in_minutes;
    public $processing_time_for_client_in_minutes;
    public $expiration_in_minutes;
    public $utm_source;
    public $utm_medium;
    public $utm_campaign;
    public $time_to_restart_screen_type_end;
    public $lang;
    public $defaultLang;
    public $stopSendingCondition;
    public $stopSendingLinkCondition;
    public $stopSendingPeriod;
    public $widget_display_ignore_limit;
    public $widget_display_limit_type;
    public $widget_display_limit_days;
    public $widget_display_limit_time;
    public $widget_create_new_answer_limit;
    public $stopSendingLinkPeriod;
    public $stopSendingLinkPeriodTime;
    public $default_executor_id;
    public $default_moderator_id;
    public $dictionary_id;
    public $editing_duration;
    public $need_auth;
    // Тип своих css-стилей
    public $css_self_type;
    // Url своего css-файла
    public $css_self_url;
    // Текст своего css
    public $css_self_text;

    /** @var FoquzPoll */
    private $poll;

    /**
     * @return FoquzPoll
     */
    public function getPoll(): FoquzPoll
    {
        return $this->poll;
    }

    /**
     * @param FoquzPoll $poll
     */
    public function setPoll(FoquzPoll $poll): void
    {
        $this->poll = $poll;
    }

    public function init()
    {
        $poll = $this->poll;
        if (!empty($poll->date_start)) {
            $this->dateRange .= date('d.m.Y', strtotime($poll->date_start));
        } else {
            if(!$poll->is_auto)
                $this->dateRange .= date('d.m.Y');
        }

        if (!empty($poll->date_end)) {
            $this->dateRange .= ' - ' . date('d.m.Y', strtotime($poll->date_end));
        } else {
            if(!$poll->is_auto)
                $this->dateRange .= ' - ' . date('d.m.Y');
        }

        $this->endOffPoll = $poll->end_of_question;
        $this->goalCount = $poll->goals_count;
        $this->limit_count = $poll->limit_count;
        $this->goal = $poll->goal_text;
        $this->trigger = $poll->trigger;
        $this->notificationScript = $poll->notification_script_id;
        $this->kioskMode = $poll->kiosk_mode;
        $this->send_time = $poll->send_time;
        $this->title = $poll->title;
        $this->point_system = $poll->point_system;
        $this->dont_send_if_passed = $poll->dont_send_if_passed;
        $this->dont_send_if_passed_link = $poll->dont_send_if_passed_link;
        $this->dont_send_if_promocode_used = $poll->dont_send_if_promocode_used;
        $this->triggerSetting = $poll->noOrderAsUsualTriggerSetting;
        $this->personal_data = $poll->personal_data;
        $this->time_to_pass = $poll->time_to_pass;
        $this->datetime_start = $poll->datetime_start;
        $this->datetime_end = $poll->datetime_end;
        $this->show_foquz_link = $poll->show_foquz_link;
        $this->time_to_restart = $poll->time_to_restart;
        $this->processing_time_in_minutes = $poll->processing_time_in_minutes;
        $this->processing_time_by_link_in_minutes = $poll->processing_time_by_link_in_minutes;
        $this->processing_time_for_client_in_minutes = $poll->processing_time_for_client_in_minutes;
        $this->expiration_in_minutes = $poll->expiration_in_minutes;
        $this->utm_source = $poll->utm_source;
        $this->utm_medium = $poll->utm_medium;
        $this->utm_campaign = $poll->utm_campaign;
        $this->time_to_restart_screen_type_end = $poll->time_to_restart_screen_type_end;
        $this->default_moderator_id = $poll->default_moderator_id;
        $this->default_executor_id = $poll->default_executor_id;
        $this->dictionary_id = $poll->dictionary_id;
        $this->editing_duration = $poll->editing_duration;
        $this->need_auth = $poll->need_auth;
        $this->widget_display_ignore_limit = $poll->widget_display_ignore_limit;
        $this->widget_display_limit_type = $poll->widget_display_limit_type;
        $this->widget_display_limit_days = $poll->widget_display_limit_days;
        $this->widget_display_limit_time = $poll->widget_display_limit_time;
        $this->widget_create_new_answer_limit = $poll->widget_create_new_answer_limit;
        $this->css_self_type = $poll->css_self_type;
        $this->css_self_url = $poll->css_self_url;
        $this->css_self_text = $poll->css_self_text;
        if ($poll->stop_sending) {
            if ($poll->stop_sending === 'double') {
                $this->stopSendingCondition = 'double';
            } else {
                $this->stopSendingCondition = 'period';
            }
        } else {
            $this->stopSendingCondition = null;
        }
        $this->stopSendingPeriod = $poll->stop_sending && $poll->stop_sending !== 'double' ? $poll->stop_sending : null;
        if ($poll->stop_sending_link) {
            if ($poll->stop_sending_link === 'double') {
                $this->stopSendingLinkCondition = 'double';
            } else {
                $this->stopSendingLinkCondition = 'period';
            }
        } else {
            $this->stopSendingLinkCondition = null;
        }
        $this->stopSendingLinkPeriod = $poll->stop_sending_link && $poll->stop_sending_link !== 'double' ? $poll->stop_sending_link : null;
        $this->stopSendingLinkPeriodTime = $poll->stop_sending_link_time;

        if ($poll->trigger_time) {
            $this->triggerTime = substr($poll->trigger_time, 0, -3);
        }

        if ($poll->trigger_orders) {
            $this->triggerOrders = $poll->trigger_orders;
        }

        if ($poll->trigger_days) {
            $this->triggerDays = $poll->trigger_days;
        }
    }

    public function rules()
    {
        return [
            //[['endOffPoll','goalCount','trigger',], 'required', 'on' => self::SCENARIO_AUTO_POLL],
            //[['dateRange',], 'required'],
            [
                [
                    'time_to_restart',
                    'dateRange',
                    'endOffPoll',
                    'trigger',
                    'triggerTime',
                    'triggerDays',
                    'triggerOrders',
                    'goal',
                    'notificationScript',
                    'kioskMode',
                    'send_time',
                    'title',
                    'point_system',
                    'dont_send_if_passed',
                    'dont_send_if_passed_link',
                    'dont_send_if_promocode_used',
                    'triggerSetting',
                    'goalCount',
                    'limit_count',
                    'time_to_pass',
                    'personal_data',
                    'datetime_start',
                    'datetime_end',
                    'show_foquz_link',
                    'processing_time_in_minutes',
                    'processing_time_by_link_in_minutes',
                    'processing_time_for_client_in_minutes',
                    'expiration_in_minutes',
                    'utm_source',
                    'utm_medium',
                    'utm_campaign',
                    'time_to_restart_screen_type_end',
                    'lang',
                    'defaultLang',
                    'stopSendingCondition',
                    'stopSendingPeriod',
                    'stopSendingLinkCondition',
                    'stopSendingLinkPeriod',
                    'stopSendingLinkPeriodTime',
                    'default_executor_id',
                    'default_moderator_id',
                    'dictionary_id',
                    'editing_duration',
                    'need_auth',
                    'widget_display_ignore_limit',
                    'widget_display_limit_type',
                    'widget_display_limit_days',
                    'widget_display_limit_time',
                    'widget_create_new_answer_limit',
                ], 'safe'],
            [[ 'goalCount'], 'safe', 'except' => self::SCENARIO_AUTO_POLL],
        ];
    }

    public function attributeLabels()
    {
        return [
            'trigger' => 'Триггер',
            'goalCount' => 'Цель (кол-во ответов)',
            'endOffPoll' => 'Заканчивать опрос',
            'dateRange' => 'Продолжительность',
            'goal' => 'Цель',
        ];
    }

    public function validateData($post): bool
    {
        if (!empty($post['stopSendingLinkPeriodTime'])) {
            if (!preg_match('/^(([0-1][0-9])|(2[0-3])):[0-5][0-9]$/', $post['stopSendingLinkPeriodTime'])) {
                $this->addError('stopSendingLinkPeriodMinutes', 'Некорректное значение');
                return false;
            }
            list($h, $m) = explode(':', $post['stopSendingLinkPeriodTime']);

            if ($h < 0 || $h > 23) {
                $this->addError('stopSendingLinkPeriodMinutes', 'Некорректное значение');
                return false;
            }
            if ($m < 0 || $m > 59) {
                $this->addError('stopSendingLinkPeriodMinutes', 'Некорректное значение');
                return false;
            }
        }

        if (!empty($post['stopSendingLinkCondition']) && $post['stopSendingLinkCondition'] !== 'double') {

            if (empty($post['stopSendingLinkPeriodTime'])) {

                if (!is_numeric($post['stopSendingLinkPeriod'])) {
                    $this->addError('stopSendingLinkPeriod', 'Некорректное значение');
                    return false;
                }

                if ($post['stopSendingLinkPeriod'] < 1 || $post['stopSendingLinkPeriod'] > 999) {
                    $this->addError('stopSendingLinkPeriod', 'Некорректное значение');
                    return false;
                }

            } else {
                if (!empty($post['stopSendingLinkPeriod'])) {

                    if (!is_numeric($post['stopSendingLinkPeriod'])) {
                        $this->addError('stopSendingLinkPeriod', 'Некорректное значение');
                        return false;
                    }

                    if ($post['stopSendingLinkPeriod'] < 0 || $post['stopSendingLinkPeriod'] > 999) {
                        $this->addError('stopSendingLinkPeriod', 'Некорректное значение');
                        return false;
                    }
                }
            }
        }

        if (isset($post['widget_display_limit_type'])) {

            if ($post['widget_display_limit_type'] !== '0' && $post['widget_display_limit_type'] !== '1' && $post['widget_display_limit_type'] !== '2') {
                $this->addError('widget_display_limit_type', 'Некорректное значение');
                return false;
            }

            if ($post['widget_display_limit_type'] === '2') {
                if (!empty($post['widget_display_limit_time'])) {
                    if (!preg_match('/^(([0-1][0-9])|(2[0-3])):[0-5][0-9]$/', $post['widget_display_limit_time'])) {
                        $this->addError('widget_display_limit_time', 'Некорректное значение');
                        return false;
                    }
                    list($h, $m) = explode(':', $post['widget_display_limit_time']);
                    if ($h < 0 || $h > 23) {
                        $this->addError('widget_display_limit_time', 'Некорректное значение');
                        return false;
                    }
                    if ($m < 0 || $m > 59) {
                        $this->addError('widget_display_limit_time', 'Некорректное значение');
                        return false;
                    }
                }

                if (empty($post['widget_display_limit_time'])) {
                    if (!is_numeric($post['widget_display_limit_days'])) {
                        $this->addError('widget_display_limit_days', 'Некорректное значение');
                        return false;
                    }
                    if ($post['widget_display_limit_days'] < 1 || $post['widget_display_limit_days'] > 999) {
                        $this->addError('widget_display_limit_days', 'Некорректное значение');
                        return false;
                    }
                } else {

                    if (!empty($post['widget_display_limit_days'])) {
                        if (!is_numeric($post['widget_display_limit_days'])) {
                            $this->addError('widget_display_limit_days', 'Некорректное значение');
                            return false;
                        }
                        if ($post['widget_display_limit_days'] < 0 || $post['widget_display_limit_days'] > 999) {
                            $this->addError('widget_display_limit_days', 'Некорректное значение');
                            return false;
                        }
                    }
                }
            }
        }
        if (isset($post['widget_create_new_answer_limit']) && $post['widget_create_new_answer_limit'] !== '') {
            if (!is_numeric($post['widget_create_new_answer_limit'])) {
                $this->addError('widget_create_new_answer_limit', 'Некорректное значение');
                return false;
            }
            if ($post['widget_create_new_answer_limit'] < 1 || $post['widget_create_new_answer_limit'] > 999) {
                $this->addError('widget_create_new_answer_limit', 'Некорректное значение');
                return false;
            }
        }

        $this->css_self_type = $post['css_self_type'] ?? FoquzPoll::CSS_SELF_TYPE_URL;
        if (!in_array($this->css_self_type, [FoquzPoll::CSS_SELF_TYPE_URL, FoquzPoll::CSS_SELF_TYPE_TEXT])) {
            $this->addError('css_self_type', 'Некорректное значение');
            return false;
        }

        $this->css_self_url = $post['css_self_url'] ?? '';
        if (!empty($this->css_self_url)) {
            if (strlen($this->css_self_url) > 255) {
                $this->addError('css_self_url', 'Неверный формат');
                return false;
            }

            if (!str_ends_with($this->css_self_url, '.css')) {
                $this->addError('css_self_url', 'Неверный формат');
                return false;
            }
            if (!filter_var($this->css_self_url, FILTER_VALIDATE_URL) !== false) {
                $this->addError('css_self_url', 'Неверный формат');
                return false;
            }
            $this->css_self_url = strip_tags($this->css_self_url);
        }

        $css_self_text = $post['css_self_text'] ?? '';
        $css_self_text = strip_tags($css_self_text);
        $css_self_text = HtmlPurifier::process($css_self_text);
        if (mb_strlen($css_self_text) > 65000) {
            $this->addError('css_self_text', 'Слишком длинный текст');
            return false;
        }
        $this->css_self_text = $css_self_text;

        return true;
    }

    /**
     * @throws Exception
     */
    public function handle()
    {
        $poll = $this->getPoll();
        if($this->dateRange) {
            $dateParse = array_map(function ($value) {
                return date('Y-m-d', strtotime(trim($value)));
            }, explode('-', $this->dateRange));

            $poll->date_start = ArrayHelper::getValue($dateParse, 0);
            $poll->date_end = ArrayHelper::getValue($dateParse, 1) ?? ArrayHelper::getValue($dateParse, 0);
        } else {
            $poll->date_start = null;
            $poll->date_end = null;
        }

        $poll->goal_text = $this->goal;
        $poll->goals_count = $this->goalCount;
        $poll->limit_count = $this->limit_count;
        $poll->end_of_question = $this->endOffPoll;
        $poll->trigger = $this->trigger;
        $poll->notification_script_id = $this->notificationScript;
        $poll->kiosk_mode = $this->kioskMode;
        $poll->send_time = $this->send_time;
        $poll->title = $this->title;
        $poll->point_system = $this->point_system;
        $poll->dont_send_if_passed = $this->dont_send_if_passed;
        $poll->dont_send_if_passed_link = $this->dont_send_if_passed_link;
        $poll->dont_send_if_promocode_used = $this->dont_send_if_promocode_used;
        $poll->personal_data = $this->personal_data;
        $poll->time_to_pass = $this->time_to_pass;
        $poll->datetime_start = DateTimeHelper::timeZoneDateToGMT3($this->datetime_start);
        $poll->datetime_end = DateTimeHelper::timeZoneDateToGMT3($this->datetime_end);
        $poll->show_foquz_link = $this->show_foquz_link;
        $poll->time_to_restart = $this->time_to_restart;
        $poll->time_to_restart_screen_type_end = $this->time_to_restart_screen_type_end;
        $poll->processing_time_in_minutes = $this->processing_time_in_minutes;
        $poll->processing_time_by_link_in_minutes = $this->processing_time_by_link_in_minutes;
        $poll->processing_time_for_client_in_minutes = $this->processing_time_for_client_in_minutes;
        $poll->expiration_in_minutes = $this->expiration_in_minutes;
        $poll->utm_source = $this->utm_source;
        $poll->utm_medium = $this->utm_medium;
        $poll->utm_campaign = $this->utm_campaign;
        $poll->default_executor_id = $this->default_executor_id;
        $poll->default_moderator_id = $this->default_moderator_id;
        $poll->dictionary_id = $this->dictionary_id;
        $poll->editing_duration = $this->editing_duration;
        $poll->need_auth = $this->need_auth;
        $poll->widget_display_ignore_limit = $this->widget_display_ignore_limit;
        $poll->widget_display_limit_type = $this->widget_display_limit_type;
        $poll->widget_create_new_answer_limit = $this->widget_create_new_answer_limit;
        $poll->css_self_type = $this->css_self_type;
        $poll->css_self_url = $this->css_self_url;
        $poll->css_self_text = $this->css_self_text;

        if ($this->widget_display_limit_type < 2) {
            $poll->widget_display_limit_days = null;
            $poll->widget_display_limit_time = null;
        } else {
            $poll->widget_display_limit_days = $this->widget_display_limit_days;
            $poll->widget_display_limit_time = $this->widget_display_limit_time;
        }


        if ($this->stopSendingPeriod) {
            $poll->stop_sending = $this->stopSendingPeriod;
        }
        if ($this->stopSendingCondition) {
            if ($this->stopSendingCondition === 'double') {
                $poll->stop_sending = 'double';
            }
        } else {
            $poll->stop_sending = null;
        }

        // значение "00" должно быть null
        $this->stopSendingLinkPeriod = (int)$this->stopSendingLinkPeriod;
        $poll->stop_sending_link = !empty($this->stopSendingLinkPeriod) ? (string)$this->stopSendingLinkPeriod : null;

        $poll->stop_sending_link_time = $this->stopSendingLinkPeriodTime;

        if ($this->stopSendingLinkCondition) {
            if ($this->stopSendingLinkCondition === 'double') {
                $poll->stop_sending_link = 'double';
                $poll->stop_sending_link_time = null;
            }
        } elseif ($this->stopSendingLinkCondition === '') {
            $poll->stop_sending_link = null;
            $poll->stop_sending_link_time = null;
        }
        if($poll->trigger == FoquzPoll::TRIGGER_NO_ORDER_AS_USUAL) {
            $triggerSetting = $poll->noOrderAsUsualTriggerSetting ?? new FoquzPollNoOrderAsUsualTriggerSettings(['foquz_poll_id' => $poll->id]);
            $triggerSetting->load($this->triggerSetting, '');
            $triggerSetting->save();
        }
        if (!empty($this->triggerTime)) {
            $poll->trigger_time = str_replace(' ','',$this->triggerTime);
        }

        if (!empty($this->triggerOrders)) {
            $poll->trigger_orders = $this->triggerOrders;
        }
        
        if (!empty($this->triggerDays)) {
            $poll->trigger_days = $this->triggerDays;
        }
        if ($this->lang || $this->defaultLang) {
            $oldLangs = FoquzPollLang::findAll(['foquz_poll_id' => $poll->id]);
            $langs = [];
            if ($this->lang) {
                foreach ($this->lang as $lang) {
                    $pollLang = FoquzPollLang::findOne(['foquz_poll_id' => $poll->id, 'poll_lang_id' => $lang]);
                    if (!$pollLang) {
                        $pollLang = new FoquzPollLang();
                    }
                    $pollLang->foquz_poll_id = $poll->id;
                    $pollLang->poll_lang_id = $lang;
                    $pollLang->checked = 1;
                    if ($pollLang->save()) {
                        $langs[] = $pollLang->id;
                    } else {
                        throw new Exception('Не удалось сохранить настройки языка', $pollLang->errors);
                    }
                }
            }
            if ($this->defaultLang) {
                $pollLang = FoquzPollLang::findOne(['foquz_poll_id' => $poll->id, 'poll_lang_id' => $this->defaultLang]);
                if ($pollLang && !in_array($pollLang->id, $langs)) {
                    $pollLang->checked = 0;
                }
                if (!$pollLang) {
                    $pollLang = new FoquzPollLang([
                        'foquz_poll_id' => $poll->id,
                        'poll_lang_id' => $this->defaultLang,
                        'checked' => 0,
                    ]);
                }
                $pollLang->default = 1;
                if ($pollLang->save()) {
                    $defaultLang = $langs[] = $pollLang->id;
                } else {
                    throw new Exception('Не удалось сохранить настройки языка', $pollLang->errors);
                }
            }
            if ($oldLangs) {
                foreach ($oldLangs as $oldLang) {
                    if (!in_array($oldLang->id, $langs)) {
                        $oldLang->delete();
                    } elseif ($oldLang->default && $oldLang->id != $defaultLang) {
                        $oldLang->default = 0;
                        $oldLang->save();
                    }
                }
            }
        } else {
            FoquzPollLang::deleteAll(['foquz_poll_id' => $poll->id]);
        }

        $poll->is_tmp = false;
        $poll->is_template = false;

        //$poll->trigger_days = 10;
        if ($poll->save()) {
            return true;
        }

        dx($poll->getErrors());

        return false;
    }
}
