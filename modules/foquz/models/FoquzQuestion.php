<?php

namespace app\modules\foquz\models;

use app\components\helpers\DictionariesHelper;
use app\models\DictionaryElement;
use app\models\Filial;
use app\models\FilialCategory;
use app\models\User;
use app\modules\foquz\models\quotes\FoquzPollAnswerQuotesDetail;
use app\modules\foquz\services\api\ExtraQuestionService;
use app\modules\foquz\services\FileService;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\caching\TagDependency;
use yii\console\Application;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\HtmlPurifier;
use yii\helpers\StringHelper;
use yii\web\BadRequestHttpException;
use yii\web\NotFoundHttpException;
use yii\web\ServerErrorHttpException;
use yii\web\UploadedFile;

/**
 * This is the model class for table "{{%foquz_question}}".
 *
 * @property int $id
 * @property int $created_at
 * @property int $updated_at
 * @property int $created_by
 * @property int $updated_by
 * @property int $poll_id
 * @property string $service_name Служебное название
 * @property string $name Название вопроса
 * @property string $description Текст вопроса
 * @property string $description_html Текст вопроса с HTML
 * @property string $sub_description Дополнительное описание
 * @property string $text
 * @property string $type
 * @property int $rating_type
 * @property string $detail_question
 * @property int $is_system
 * @property int $position
 * @property bool $is_self_answer Опция свой вариант
 * @property bool $is_tmp
 * @property bool $is_deleted
 * @property bool $is_condition
 * @property bool $is_source
 * @property bool $show_category
 * @property bool $show_name
 * @property bool $show_portion
 * @property bool $value_all
 * @property bool $is_required
 * @property bool $is_updated
 * @property int $point_id
 * @property int $min_sum
 * @property int $max_points_calc_method Метод расчета максимального количества баллов (Метод расчета максимального количества баллов (0 - не учитывать в максимальном количестве баллов, если вопрос скрыт, 1 - учитывать в максимальном количестве баллов даже если вопрос скрыт)
 * @property bool $max_points_calc_recipient Считать максимум баллов за вопрос, исходя из фактически унаследованных вариантов
 * @property int $variants_element_type Тип уточняющего вопроса
 * @property int $main_question_type
 * @property boolean $comment_enabled Комментарий
 * @property boolean $comment_required Комментарий обязателен
 * @property int $comment_minlength Кол-во символов в комментарии min
 * @property int $comment_maxlength Кол-во символов в комментарии max
 * @property int|null $self_variant_minlength Минимальная длина своего ответа
 * @property int|null $self_variant_maxlength Максимальная длина своего ответа
 * @property boolean $self_variant_comment_required Обязателен комментарий к своему варианту ответу
 * @property boolean $self_variant_nothing Свой вариант с опцией "Ничего из перечисленного"
 * @property string $self_variant_description Текст для тултипа в Своем варианте
 * @property boolean $show_tooltips Показывать подсказки для элементов и категорий справочника
 * @property string|null $comment_label Метка поля Комментарий
 * @property int $company_id
 * @property boolean $for_all_rates
 * @property bool $link_with_client_field
 * @property string $linked_client_field
 * @property bool $rewrite_linked_field
 * @property string $placeholder_text Подсказка внутри поля (Комментарий)
 * @property string|null $select_placeholder_text Подсказка внутри поля (селект)
 * @property string|null $self_variant_placeholder_text Текст подсказки для своего ответа
 * @property string $choose_type
 * @property int $mask
 * @property string $mask_config
 * @property bool $dont_show_if_answered Не отображать вопрос, если ответ уже был получен
 * @property string $answers_from
 * @property string $smile_type
 * @property string $deleted_detail_question Удаленные вариант ответов для типов вопросов где ответ хранится в detail_question
 * @property int $smiles_count
 * @property string $matrix_settings
 * @property int|null $min_choose_variants Min кол-во выбранных ответов
 * @property int $max_choose_variants
 * @property string $self_variant_text
 * @property boolean $dropdown_variants Варианты ответов выпадающим списком
 * @property boolean $only_date_month
 * @property boolean $random_variants_order Случайный порядок вариантов
 * @property boolean $random_categories_order Случайный порядок категорий (для типа опроса Закрытая карточная сортировка)
 * @property string|null $card_column_text Наименование колонки с карточками (для типа опроса Закрытая карточная сортировка)
 * @property string|null $category_column_text Наименование колонки с категориями (для типа опроса Закрытая карточная сортировка)
 * @property boolean $skip Пропуск ответа
 * @property string|null $skip_text Текст кнопки пропуска ответа
 * @property boolean $skip_variant Пропуск для каждой оценки
 * @property boolean $skip_row Общий пропуск для строки
 * @property boolean $skip_column Общий пропуск для столбца
 * @property int $donor ID донора
 * @property int|null $donor_columns ID вопроса-донора для столбцов
 * @property int|null $donor_rows ID вопроса-донора для строк
 * @property bool $donor_chosen Переключатель Варианты ответов: Выбранные варианты / Невыбранные варианты
 * @property bool $donor_cols_chosen Переключатель Варианты ответов: Выбранные варианты / Невыбранные варианты (столбцы)
 * @property bool $show_labels
 * @property bool $show_numbers
 * @property bool $from_one
 * @property boolean $set_variants Опция включения вариантов
 * @property int $extra_question_type Тип уточняющего вопроса (0 - Выключен, 1 - Общий для всех вариантов, 2 - Одинаковый вопрос для каждого варианта, 3 - Для каждого варианта свой вопрос)
 * @property int|null $extra_question_rate_from Минимальная оценка для отображения уточняющего вопроса
 * @property int|null $extra_question_rate_to Максимальная оценка для отображения уточняющего вопроса
 * @property bool $extra_required Опция обязательный для Уточняющего вопроса
 * @property bool $disable_select_category Запретить выбор категории/подкатегории до разворачивания вложенных уровней
 * @property bool $variants_with_files Варианты ответов с изображениями/видео (плиткой)
 * @property int $min_choose_extra_variants Минимальное количество выбранных вариантов для уточняющего вопроса
 * @property int $max_choose_extra_variants Максимальное количество выбранных вариантов для уточняющего вопроса
 *
 * @property int $questionAnswerItemsCount
 *
 * @property string $mainTypeString
 * @property string $commentField Название поля с комментарием
 * @property bool $haveClarifyingQuestion Содержит ли вопрос уточняющий вопрос
 *
 * @property FoquzPoll $poll
 * @property FoquzQuestionDetail[] $questionDetails
 * @property FoquzQuestionDetail[] $questionDetailsActive
 * @property-read FoquzQuestionDetailLang[] $questionDetailLangs
 * @property RecipientQuestionDetail[] $recipientQuestionDetails
 * @property FoquzQuestionDetail[] $recipientsQuestionDetails
 * @property FoquzQuestionImage[] $questionImages
 * @property FoquzQuestionFile[] $questionFiles
 * @property FoquzPollAnswer[] $doneAnswers
 * @property FoquzPollAnswer[] $questionDoneAnswers
 * @property FoquzPollAnswerItem[] $questionAnswerItems
 * @property FoquzQuestionSmile[] $questionSmiles
 * @property FoquzPointItem $point
 * @property User $user
 * @property FoquzQuestionNpsRatingSetting|null $npsRatingSetting
 * @property FoquzQuestionScaleRatingSetting|null $scaleRatingSetting
 * @property FoquzQuestionDifferentialRow[] $differentialRows
 * @property FoquzPollQuestionsLogic[] $questionLogic
 * @property FoquzPollQuestionViewLogic[] $foquzPollQuestionViewLogics
 * @property FoquzQuestionStarRatingOptions|null $starRatingOptions
 * @property FoquzQuestionRightAnswer $rightAnswer
 * @property FoquzQuestionIntermediateBlockSetting $intermediateBlock
 * @property FoquzQuestionSemDifSetting $semDifSetting
 * @property FoquzPollDisplayPageQuestion $pollDisplayPageQuestion
 * @property FoquzQuestionAddressCodes $addressCodes
 * @property FoquzQuestionPrioritySettings $foquzQuestionPrioritySettings
 * @property FoquzQuestionFormField[] $formFields
 * @property FoquzQuestionEndScreenLogo[] $foquzQuestionEndScreenLogos
 * @property FoquzQuestionLang[] $foquzQuestionLangs
 * @property FoquzQuestionMatrixElement[] $matrixElements
 * @property FoquzQuestionMatrixElement[] $activeMatrixElements
 * @property DictionaryElement $dictionaryElement
 * @property FoquzQuestion $rowsDonor
 * @property FoquzQuestion $columnsDonor
 * @property FoquzQuestion|null $mainDonor
 * @property FoquzQuestion|null $mainDonorRows
 * @property FoquzQuestion|null $mainDonorColumns
 * @property FoquzQuestionDetailLang[] $dictionaryLangs
 * @property int $date_type [int(11)]
 * @property array|null $file_types Типы файлов для загрузки (audio, video, image)
 * @property int $files_length [int(11)]
 * @property string $dictionary_sort [enum('default', 'alphabetter', 'random')]
 * @property string $dictionary_list_type [enum('list', 'tree')]
 * @property int $dictionary_id
 * @property int|null $dictionary_element_id ID элемента справочника для вопроса
 * @property-read string $typeName
 * @property-read int|null $maxPoints
 * @property-read int|null $minPoints
 * @property-read float|null $avgPoint
 * @property-read FoquzFile[] $detailFiles Файлы вариантов ответов
 * @property-read array|null $selfVariantFile Изображение для своего варианта
 * @property-read FoquzQuestionCardSortingCategory[] $cardSortingCategories Категории для типа опроса Закрытая карточная сортировка
 * @property-read FoquzQuestionFirstClick $firstClick Настройки для типа опроса Тест первого клика
 * @property-read FoquzQuestionFirstClickArea[] $firstClickArea Области для изображения для типа опроса Тест первого клика
 *
 * @property-read bool $isHaveExtra
 */
class FoquzQuestion extends BaseModel
{
    /** @var self */
    public $mainDonor;
    public $mainDonorRows;
    public $mainDonorColumns;
    public $tooltip = [];

    const RATING_TYPE_START = 1;
    const RATING_OPTIONS = 2;
    const RATING_DISHES = 3;

    const VARIANT_ELEMENT_TYPE_RADIO = 0;
    const VARIANT_ELEMENT_TYPE_CHECKBOX = 1;
    const VARIANT_ELEMENT_TYPE_TEXT = 2;

    const CHOOSE_IMAGE = 'image';
    const CHOOSE_VIDEO = 'video';

    public const TYPE_ASSESSMENT = 0; //Оценка
    public const TYPE_VARIANTS = 1; //Варианты ответов
    public const TYPE_TEXT_ANSWER = 2; //Текстовый ответ
    public const TYPE_DATE = 3; // Дата/время has json in foquz_poll_answer_item.answer
    public const TYPE_ADDRESS = 4; //Адрес
    public const TYPE_FILE_UPLOAD = 5; //Загрузка файла
    public const TYPE_FORM = 6; //Анкета
    public const TYPE_VARIANT_STAR = 7; //Звездный рейтинг для вариантов
    public const TYPE_PRIORITY = 8; //Приоритет
    public const TYPE_CHOOSE_MEDIA = 9; // Выбор изображения/видео has array in foquz_poll_answer_item.answer
    public const TYPE_GALLERY_RATING = 10; // Рейтинг фото/видео галереи
    public const TYPE_SMILE_RATING = 11; // Смайл-рейтинг has int in foquz_poll_answer_item.answer
    public const TYPE_NPS_RATING = 12; //Рейтинг NPS
    public const TYPE_SIMPLE_MATRIX = 13; // Простая матрица has json in foquz_poll_answer_item.answer
    public const TYPE_SEM_DIFFERENTIAL = 14; // Семантический дифференциал has json in foquz_poll_answer_item.answer
    public const TYPE_STAR_RATING = 15; //Звездный рейтинг
    public const TYPE_INTERMEDIATE_BLOCK = 16; //Промежуточный блок
    public const TYPE_FILIAL = 17; //Выбор филиала
    public const TYPE_RATING = 18; //Рейтинг
    public const TYPE_DICTIONARY = 19; //Классификатор
    public const TYPE_SCALE = 20; //Шкала
    public const TYPE_3D_MATRIX = 21; //3D матрица
    public const TYPE_CARD_SORTING_CLOSED = 22; //Закрытая карточная сортировка
    public const TYPE_DISTRIBUTION_SCALE = 23; // Распределительная шкала
    public const TYPE_FIRST_CLICK = 24; // Тест первого клика

    const SCENARIO_UPDATE = 'update';
    const SCENARIO_UPDATE_IMAGE = 'update_image';
    const SCENARIO_UPDATE_VIDEO = 'update_video';

    const TYPE_TEXT = 'text';
    const TYPE_IMAGE = 'image';
    const TYPE_VIDEO = 'video';

    const SMILE_HEART = 'heart';
    const SMILE_LIKE = 'like';
    const SMILE_FACE = 'face';
    const SMILE_YELLOW = 'yellow';
    const SMILE_COLOR_FACE = 'color_face'; // Фронт использует такое обозначение для Лица (жёлтый фон)
    const SMILE_ROBOT = 'robot';
    const SMILE_EMOJI = 'emoji';
    const SMILE_DIFFERENT = 'different'; // Фронт использует такое обозначение для Эмодзи

    const SMILE_WEATHER = 'weather';
    const SMILE_CUSTOM = 'custom';

    public const UPLOAD_TYPE_AUDIO = 'audio';
    public const UPLOAD_TYPE_VIDEO = 'video';
    public const UPLOAD_TYPE_IMAGE = 'image';

    const NO_MASK = 0;
    const MASK_PHONE = 1;
    const MASK_EMAIL = 2;
    const MASK_NUMBER = 3;
    const MASK_SITE = 4;
    const MASK_NAME = 5;
    const MASK_DATE = 6;
    const MASK_PERIOD = 7;
    const MASK_DATE_MONTH = 8;

    /** Методы расчета максимального балла */
    const MAX_POINTS_CALC_METHOD_WITHOUT_HIDDEN = 0;
    const MAX_POINTS_CALC_METHOD_WITH_HIDDEN = 1;

    /** типы кторые могут содержать комментарий */
    const TYPES_COMMENTED = [
        FoquzQuestion::TYPE_TEXT_ANSWER,
        FoquzQuestion::TYPE_DATE,
        FoquzQuestion::TYPE_ADDRESS,
        FoquzQuestion::TYPE_FORM,
        FoquzQuestion::TYPE_PRIORITY,
        FoquzQuestion::TYPE_GALLERY_RATING,
        FoquzQuestion::TYPE_CHOOSE_MEDIA,
        FoquzQuestion::TYPE_SMILE_RATING,
        FoquzQuestion::TYPE_NPS_RATING,
        FoquzQuestion::TYPE_SIMPLE_MATRIX,
        FoquzQuestion::TYPE_3D_MATRIX,
        FoquzQuestion::TYPE_SEM_DIFFERENTIAL,
        FoquzQuestion::TYPE_VARIANT_STAR,
        FoquzQuestion::TYPE_SCALE,
        FoquzQuestion::TYPE_VARIANTS,
        FoquzQuestion::TYPE_CARD_SORTING_CLOSED,
        FoquzQuestion::TYPE_DISTRIBUTION_SCALE,
    ];

    const TYPES_DONORS = [
        FoquzQuestion::TYPE_VARIANTS,
        FoquzQuestion::TYPE_DICTIONARY,
    ];

    const TYPES_RECIPIENTS = [
        FoquzQuestion::TYPE_VARIANTS,
        FoquzQuestion::TYPE_PRIORITY,
        FoquzQuestion::TYPE_SIMPLE_MATRIX,
        FoquzQuestion::TYPE_VARIANT_STAR,
        FoquzQuestion::TYPE_NPS_RATING,
        FoquzQuestion::TYPE_SCALE
    ];

    public const TYPES_CLARIFYING_QUESTION = [
        FoquzQuestion::TYPE_SIMPLE_MATRIX,
        FoquzQuestion::TYPE_VARIANT_STAR,
        FoquzQuestion::TYPE_STAR_RATING,
        FoquzQuestion::TYPE_RATING,
        FoquzQuestion::TYPE_NPS_RATING,
    ];

    public const TYPES_POINTS = [
        self::TYPE_VARIANTS,
        self::TYPE_DATE,
        self::TYPE_PRIORITY,
        self::TYPE_CHOOSE_MEDIA,
        self::TYPE_SIMPLE_MATRIX,
    ];

    public const UPLOAD_TYPES = [
        self::UPLOAD_TYPE_AUDIO,
        self::UPLOAD_TYPE_VIDEO,
        self::UPLOAD_TYPE_IMAGE,
    ];

    public const TYPES_NAME = [
        self::TYPE_ASSESSMENT         => 'Оценка',
        self::TYPE_VARIANTS           => 'Варианты ответов',
        self::TYPE_TEXT_ANSWER        => 'Текстовый ответ',
        self::TYPE_DATE               => 'Дата/время',
        self::TYPE_ADDRESS            => 'Адрес',
        self::TYPE_FILE_UPLOAD        => 'Загрузка файла',
        self::TYPE_FORM               => 'Анкета',
        self::TYPE_VARIANT_STAR       => 'Звездный рейтинг для вариантов',
        self::TYPE_PRIORITY           => 'Приоритет',
        self::TYPE_CHOOSE_MEDIA       => 'Выбор изображения/видео',
        self::TYPE_GALLERY_RATING     => 'Рейтинг фото/видео галереи',
        self::TYPE_SMILE_RATING       => 'Смайл-рейтинг',
        self::TYPE_NPS_RATING         => 'Рейтинг NPS',
        self::TYPE_SIMPLE_MATRIX      => 'Простая матрица',
        self::TYPE_SEM_DIFFERENTIAL   => 'Семантический дифференциал',
        self::TYPE_STAR_RATING        => 'Звездный рейтинг',
        self::TYPE_INTERMEDIATE_BLOCK => 'Промежуточный блок',
        self::TYPE_FILIAL             => 'Выбор филиала',
        self::TYPE_RATING             => 'Рейтинг',
        self::TYPE_DICTIONARY         => 'Классификатор',
        self::TYPE_SCALE              => 'Шкала',
        self::TYPE_3D_MATRIX          => '3D матрица',
        self::TYPE_DISTRIBUTION_SCALE => 'Распределительная шкала',
        self::TYPE_CARD_SORTING_CLOSED => 'Закрытая карточная сортировка',
        self::TYPE_FIRST_CLICK        => 'Тест первого клика',
    ];

    public const EXTRA_QUESTION_OFF = 0;
    public const EXTRA_QUESTION_COMMON = 1;
    public const EXTRA_QUESTION_COMMON_FOR_EACH = 2;
    public const EXTRA_QUESTION_DIFFERENT_EACH = 3;

    public const EXTRA_QUESTION_TYPES = [
        self::EXTRA_QUESTION_OFF             => 'Выключен',
        self::EXTRA_QUESTION_COMMON          => 'Общий для всех вариантов',
        self::EXTRA_QUESTION_COMMON_FOR_EACH => 'Одинаковый вопрос для каждого варианта',
        self::EXTRA_QUESTION_DIFFERENT_EACH  => 'Для каждого варианта свой вопрос',
    ];

    public function getRateBounds()
    {
        switch ($this->main_question_type) {
            case FoquzQuestion::TYPE_ASSESSMENT:
            case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
            case FoquzQuestion::TYPE_GALLERY_RATING:
                return [1, 5];
            case FoquzQuestion::TYPE_VARIANT_STAR:
            case FoquzQuestion::TYPE_STAR_RATING:
                return [1, FoquzQuestionStarRatingOptions::findOne(['foquz_question_id' => $this->id])->count ?? null];
            case FoquzQuestion::TYPE_SMILE_RATING:
                return [1, $this->smiles_count];
            case FoquzQuestion::TYPE_NPS_RATING:
                return [0, 10];

            default:
                return [null, null];
        }
    }

    /**
     * Возвращает название поля с комментарием
     * @return string|null
     */
    public function getCommentField(): ?string
    {
        switch ($this->main_question_type) {
            case self::TYPE_VARIANT_STAR:
            case self::TYPE_NPS_RATING:
            case self::TYPE_SMILE_RATING:
            case self::TYPE_PRIORITY:
            case self::TYPE_FILIAL:
            case self::TYPE_GALLERY_RATING:
            case self::TYPE_SEM_DIFFERENTIAL:
            case self::TYPE_CHOOSE_MEDIA:
            case self::TYPE_SCALE:
            case self::TYPE_DICTIONARY:
            case self::TYPE_SIMPLE_MATRIX:
            case self::TYPE_3D_MATRIX:
            case self::TYPE_DISTRIBUTION_SCALE:
                return 'self_variant';
            case self::TYPE_STAR_RATING:
            case self::TYPE_FILE_UPLOAD:
            case self::TYPE_RATING:
            case self::TYPE_ASSESSMENT:
            case self::TYPE_VARIANTS:
                return 'answer';
            default:
                return null;
        }
    }

    /**
     * Содержит ли вопрос уточняющий вопрос
     * @return bool
     */
    public function getHaveClarifyingQuestion()
    {
        return in_array($this->main_question_type, self::TYPES_CLARIFYING_QUESTION) && $this->detail_question;
    }

    /**
     * Возвращает название типа вопроса
     * @return string
     */
    public function getTypeName(): string
    {
        return self::TYPES_NAME[$this->main_question_type] ?? 'Неизвестный тип';
    }

    public function fields()
    {
        $additionalFields = [
            'poll_is_auto'   => function ($model) {
                return $model->poll->is_auto;
            },
            'position'       => function () {
                return (int)$this->position;
            },
            'detail_answers' => function () {
                return $this->collectVariants();
            },
            'gallery'        => function () {
                return $this->grabMediaContent();
            },
            'enableGallery'  => function ($model) {
                return count($model->grabMediaContent()) > 0;
            },
            'images'         => function ($model) {
                return array_filter($model->questionFiles, static function ($file) {
                    return $file->type === FoquzQuestion::TYPE_IMAGE;
                });
            },
            'is_system'      => function ($model) {
                return $model->is_system;
            },
            'smiles'         => function ($model) {
                if ($model->main_question_type !== self::TYPE_SMILE_RATING) {
                    return [];
                }
                return $model->questionSmiles;
            },
            'chooseMedia'    => function ($model) {
                if ($model->choose_type) {
                    if ($model->choose_type === self::CHOOSE_IMAGE) {
                        return array_filter($model->questionFiles, static function ($file) {
                            return $file->type === FoquzQuestion::TYPE_IMAGE;
                        });
                    }

                    $videos = [];
                    foreach ($model->questionFiles as $video) {
                        if ($video->type !== FoquzQuestion::TYPE_VIDEO) {
                            continue;
                        }
                        $videos[] = [
                            'id'          => $video->id,
                            'url'         => isset($video->attachment_content) && $video->attachment_content ? $video->attachment_content : '/' . $video->file_path,
                            'previewUrl'  => $video->getImage(),
                            'position'    => $video->position,
                            'description' => $video->description,
                            'points'      => $video->points
                        ];
                    }
                    return $videos;
                }
                return [];
            },
            'videos'         => function ($model) {
                $videos = [];
                foreach ($model->questionFiles as $video) {
                    if ($video->type !== FoquzQuestion::TYPE_VIDEO) {
                        continue;
                    }
                    $videos[] = [
                        'id'         => $video->id,
                        'url'        => isset($video->attachment_content) && $video->attachment_content ? $video->attachment_content : '/' . $video->file_path,
                        'previewUrl' => $video->getImage(),
                        'label'      => $video->file_text
                    ];
                }
                return $videos;
            },
            'quizzes'        => function ($model) {
                if ($model->main_question_type !== self::TYPE_FORM) {
                    return [];
                }
                $quizzes = [];
                foreach ($model->formFields as $formField) {
                    $quiz = [
                        'id'                  => $formField->id,
                        'label'               => $formField->name,
                        'value'               => '',
                        'isRequired'          => $formField->is_required,
                        'maskType'            => $formField->mask_type,
                        'isTextarea'          => $formField->variants_type === 1,
                        'textFieldParam'      => [
                            'min' => $formField->comment_minlength,
                            'max' => $formField->comment_maxlength
                        ],
                        'linkWithClientField' => $formField->link_with_client_field,
                        'linkedClientField'   => $formField->linked_client_field,
                        'rewriteLinkedField'  => $formField->rewrite_linked_field,
                        'placeholderText'     => $formField->placeholder_text,
                        'maskConfig'          => json_decode($formField->mask_config),
                    ];
                    if ($langs = FoquzQuestionFormFieldLang::findAll(['form_field_id' => $formField->id])) {
                        $quiz['formLangs'] = [];
                        foreach ($langs as $lang) {
                            $lang = ArrayHelper::toArray($lang);
                            $quiz['formLangs'][] = $lang;
                        }
                    }
                    $quizzes[] = $quiz;
                }
                return $quizzes;
            },


            'addressCodes' => function ($model) {
                if ($model->main_question_type !== self::TYPE_ADDRESS) {
                    return [];
                }
                return [
                    'regions'   => $model->addressCodes ? json_decode($model->addressCodes->regions) : [],
                    'cities'    => $model->addressCodes ? json_decode($model->addressCodes->cities) : [],
                    'districts' => $model->addressCodes ? json_decode($model->addressCodes->districts) : [],
                    'streets'   => $model->addressCodes ? json_decode($model->addressCodes->streets) : []
                ];
            },


            'countAnswers' => function ($model) {
                return $model->questionAnswerItemsCount;
            },


            'placeholderText'            => function ($model) {
                return $model->placeholder_text;
            },
            'selectPlaceholderText'      => function ($model) {
                return $model->select_placeholder_text;
            },
            'selfVariantPlaceholderText' => function ($model) {
                return $model->self_variant_placeholder_text;
            },
            'maskConfig'                 => function ($model) {
                return json_decode($model->mask_config);
            },
            'questionLogic'              => function ($model) {
                return $model->questionLogic;
            },
            'questionViewLogic'          => function ($model) {
                return $model->foquzPollQuestionViewLogics;
            },
            'pointName' => function ($model) {
                // убрано в рамках задачи #4484
                //return $model->point ? $model->point->name : null;
                return null;
            },
            'foquzQuestionEndScreenLogos',
            'comment_label'              => function () {
                return $this->comment_label ?: 'Ваш комментарий';
            },
            'set_variants'               => function () {
                return (bool)$this->set_variants;
            },
            'isHaveExtra'                => function () {
                return !empty($this->detail_question);
            },
        ];
        if ($this->foquzQuestionLangs) {
            $additionalFields['langs'] = function () {
                return $this->foquzQuestionLangs;
            };
        }
        if ($this->main_question_type === self::TYPE_NPS_RATING) {
            $additionalFields['npsRatingSetting'] = function ($model) {
                return $model->npsRatingSetting;
            };
        }
        if ($this->main_question_type === self::TYPE_SCALE || $this->main_question_type === self::TYPE_DISTRIBUTION_SCALE) {
            $additionalFields['scaleRatingSetting'] = function () {
                return $this->scaleRatingSetting;
            };
        }
        if ($this->main_question_type === self::TYPE_PRIORITY) {
            $additionalFields['foquzQuestionPrioritySettings'] = function ($model) {
                return $model->foquzQuestionPrioritySettings;
            };
        }
        if ($this->main_question_type === self::TYPE_SIMPLE_MATRIX || $this->main_question_type === self::TYPE_3D_MATRIX) {
            $additionalFields['matrixSettings'] = function ($model) {
                return json_decode($model->matrix_settings);
            };
        }
        if ($this->main_question_type === self::TYPE_SEM_DIFFERENTIAL) {
            $additionalFields['differentialRows'] = function ($model) {
                return $model->differentialRows;
            };
            $additionalFields['semDifSetting'] = function ($model) {
                return $model->semDifSetting;
            };
        }
        if (in_array($this->main_question_type, [self::TYPE_STAR_RATING, self::TYPE_VARIANT_STAR, self::TYPE_RATING])) {
            $additionalFields['starRatingOptions'] = function ($model) {
                return $model->starRatingOptions;
            };
        }
        if ($this->main_question_type === self::TYPE_PRIORITY || $this->main_question_type === self::TYPE_DATE) {
            $additionalFields['rightAnswer'] = function ($model) {
                return $model->rightAnswer;
            };
        }
        if ($this->main_question_type === self::TYPE_INTERMEDIATE_BLOCK) {
            $additionalFields['intermediateBlock'] = function ($model) {
                return $model->intermediateBlock;
            };
        }
        if ($this->main_question_type === self::TYPE_CARD_SORTING_CLOSED) {
            $additionalFields['cardSortingCategories'] = function ($model) {
                return $model->cardSortingCategories;
            };
        }
        if ($this->main_question_type === self::TYPE_FIRST_CLICK) {
            $additionalFields['firstClick'] = function ($model) {
                return $model->firstClick;
            };
            $additionalFields['firstClickArea'] = function ($model) {
                return $model->firstClickArea;
            };
        }
        if ($this->dictionary_element_id) {
            $additionalFields['dictionary_element_name'] = function () {
                return DictionaryElement::findOne($this->dictionary_element_id)->title;
            };
        }
        if ($this->main_question_type === self::TYPE_3D_MATRIX || $this->main_question_type === self::TYPE_SIMPLE_MATRIX) {
            $additionalFields['matrixElements'] = static function ($model) {
                $matrixElements = ArrayHelper::index($model->activeMatrixElements, null, 'type_id');
                return [
                    'columns' => !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN]) ?
                        $matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN] : [],
                    'rows'    => !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW]) ?
                        $matrixElements[FoquzQuestionMatrixElement::TYPE_ROW] : [],
                ];
            };
        }
        if (!$this->donor && (int)$this->main_question_type === FoquzQuestion::TYPE_VARIANTS
            && $recipients = FoquzQuestion::findAll([
                ['OR', ['donor' => $this->id, 'donor_rows' => $this->id, 'donor_columns' => $this->id]],
                'is_deleted' => 0
            ])) {
            $additionalFields['recipients'] = function () use ($recipients) {
                $array = [];
                foreach ($recipients as $recipient) {
                    $array[] = [
                        'id'           => $recipient->id,
                        'name'         => $recipient->name,
                        'service_name' => $recipient->service_name,
                        'position'     => $recipient->position,
                    ];
                }
                return $array;
            };
        }
        $additionalFields['selfVariantFile'] = function () {
            return $this->selfVariantFile;
        };
        $additionalFields['tooltip'] = function () {
            return $this->tooltip;
        };
        $fields = parent::fields();

        if ((int)$this->main_question_type === FoquzQuestion::TYPE_INTERMEDIATE_BLOCK && $this->intermediateBlock?->screen_type === FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_TEXT) {
            $this->description = StringHelper::truncate(strip_tags($this->intermediateBlock->text), 150);
        }
        // в рамках задачи #4484
        unset($fields['point_id']);
        return $fields + $additionalFields;
    }


    /**
     * перевод для вопроса
     * @param FoquzPollLang|null $lang
     * @return FoquzQuestionLang|null
     */
    public function getTranslate(FoquzPollLang $lang = null)
    {
        if ($lang) {
            return FoquzQuestionLang::find()->where([
                "foquz_question_id"  => $this->id,
                "foquz_poll_lang_id" => $lang->id
            ])->one();
        }
        return null;
    }

    public function getFoquzQuestionLangs()
    {
        return $this->hasMany(FoquzQuestionLang::class, ['foquz_question_id' => 'id']);
    }

    public function getFoquzQuestionPrioritySettings()
    {
        return $this->hasOne(FoquzQuestionPrioritySettings::className(), ['foquz_question_id' => 'id']);
    }

    public function getQuizzes($answerItem = null)
    {
        $quizzes = [];
        if ($this->main_question_type != self::TYPE_FORM) {
            return $quizzes;
        }
        foreach ($this->formFields as $formField) {
            $id = $formField->id;
            $mask = [];
            if ($formField->mask_type == 5 && $formField->mask_config) {
                foreach (json_decode($formField->mask_config, true) as $name => $maskField) {
                    $mask[$name] = $maskField + [
                            'value' => $answerItem ? (json_decode($answerItem->answer)->$id->$name ?? '') : ''
                        ];
                }
            }

            $quizzes[] = [
                'id'                  => $formField->id,
                'label'               => $formField->name,
                'value'               => $answerItem ? (json_decode($answerItem->answer)->$id ?? '') : '',
                'isRequired'          => $formField->is_required,
                'maskType'            => $formField->mask_type,
                'isTextarea'          => (int)($formField->mask_type != null ? false : $formField->variants_type === 1),
                'textFieldParam'      => [
                    'min' => $formField->comment_minlength,
                    'max' => $formField->comment_maxlength
                ],
                'linkWithClientField' => $formField->link_with_client_field,
                'linkedClientField'   => $formField->linked_client_field,
                'rewriteLinkedField'  => $formField->rewrite_linked_field,
                'placeholderText'     => $formField->placeholder_text,
                'maskConfig'          => (object)$mask,
                'langs'               => $formField->langs,
            ];
            /*if ($langs = FoquzQuestionFormFieldLang::findAll(['form_field_id' => $formField->id])) {
                $quizzes['formLangs'] = [];
                foreach ($langs as $lang) {
                    $lang = ArrayHelper::toArray($lang);
                    $quizzes['formLangs'][] = $lang;
                }
            }*/
        }

        return $quizzes;
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();

        $scenarios[self::SCENARIO_UPDATE] = [
            'service_name',
            'name',
            'description',
            'sub_description',
            'text',
            'rating_type',
            'detail_question',
            'is_self_answer',
            'main_question_type',
            'type',
            'position',
            'is_updated',
            'is_required',
            'comment_minlength',
            'comment_maxlength',
            'self_variant_minlength',
            'self_variant_maxlength',
            'variants_element_type',
            'mask',
            'date_type',
            'file_types',
            'files_length',
            'comment_enabled',
            'comment_required',
            'for_all_rates',
            'link_with_client_field',
            'linked_client_field',
            'rewrite_linked_field',
            'placeholder_text',
            'select_placeholder_text',
            'self_variant_placeholder_text',
            'choose_type',
            'mask_config',
            'dont_show_if_answered',
            'answers_from',
            'smile_type',
            'smiles_count',
            'matrix_settings',
            'min_choose_variants',
            'max_choose_variants',
            'self_variant_text',
            'dropdown_variants',
            'only_date_month',
            'random_variants_order',
            'random_categories_order',
            'point_id',
            'skip',
            'skip_text',
            'skip_variant',
            'skip_row',
            'skip_column',
            'donor',
            'donor_chosen',
            'donor_cols_chosen',
            'donor_columns',
            'donor_rows',
            'dictionary_element_id',
            'max_points_calc_method',
            'show_tooltips',
            'disable_select_category',
            'variants_with_files',
            'show_category',
            'show_name',
            'show_portion',
            'value_all',
            'min_sum',
            'max_points_calc_recipient',
            'self_variant_comment_required',
            'self_variant_nothing',
            'self_variant_description',
            'extra_question_type',
            'extra_question_rate_from',
            'extra_question_rate_to',
            'min_choose_extra_variants',
            'max_choose_extra_variants',
        ];

        $scenarios[self::SCENARIO_UPDATE_IMAGE] = [
            'service_name',
            'name',
            'description',
            'rating_type',
            'detail_question',
            'is_self_answer',
            'main_question_type',
            'type',
            'position',
            'show_category',
            'show_name',
            'show_portion',
            'value_all',
            'min_sum',
            'is_required',
            'comment_minlength',
            'comment_maxlength',
            'self_variant_minlength',
            'self_variant_maxlength',
            'variants_element_type',
            'comment_enabled',
            'comment_required',
            'for_all_rates',
            'link_with_client_field',
            'linked_client_field',
            'rewrite_linked_field',
            'placeholder_text',
            'select_placeholder_text',
            'self_variant_placeholder_text',
            'choose_type',
            'mask_config',
            'dont_show_if_answered',
            'answers_from',
            'smile_type',
            'smiles_count',
            'matrix_settings',
            'max_choose_variants',
            'self_variant_text',
            'dropdown_variants',
            'only_date_month',
            'random_variants_order',
            'point_id',
            'skip',
            'skip_text',
            'skip_variant',
            'skip_row',
            'skip_column',
            'donor',
            'donor_chosen',
            'donor_cols_chosen',
            'donor_columns',
            'donor_rows',
            'dictionary_element_id',
            'max_points_calc_method',
            'show_tooltips',
            'disable_select_category',
            'variants_with_files',
        ];

        $scenarios[self::SCENARIO_UPDATE_VIDEO] = [
            'service_name',
            'name',
            'description',
            'rating_type',
            'detail_question',
            'is_self_answer',
            'main_question_type',
            'type',
            'position',
            'is_required',
            'comment_minlength',
            'comment_maxlength',
            'self_variant_minlength',
            'self_variant_maxlength',
            'variants_element_type',
            'comment_enabled',
            'comment_required',
            'for_all_rates',
            'link_with_client_field',
            'linked_client_field',
            'rewrite_linked_field',
            'placeholder_text',
            'select_placeholder_text',
            'self_variant_placeholder_text',
            'choose_type',
            'mask_config',
            'dont_show_if_answered',
            'answers_from',
            'smile_type',
            'smiles_count',
            'matrix_settings',
            'max_choose_variants',
            'self_variant_text',
            'dropdown_variants',
            'only_date_month',
            'random_variants_order',
            'point_id',
            'skip',
            'skip_text',
            'skip_variant',
            'skip_row',
            'skip_column',
            'donor',
            'donor_chosen',
            'donor_cols_chosen',
            'donor_columns',
            'donor_rows',
            'dictionary_element_id',
            'max_points_calc_method',
            'show_tooltips',
            'disable_select_category',
            'variants_with_files',
        ];

        return $scenarios;
    }

    public function transactions()
    {
        return [
            self::SCENARIO_DEFAULT => self::OP_ALL,
        ];
    }

    public static function getRatingTypes()
    {
        return [
            self::RATING_TYPE_START => 'Звёзды 5',
            self::RATING_OPTIONS    => 'Варианты на выбор'
        ];
    }

    /** @inheritdoc */
    public function behaviors()
    {
        return [
            TimestampBehavior::class,
            //BlameableBehavior::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%foquz_question}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                'service_name',
                'required',
                'on'      => [self::SCENARIO_UPDATE, self::SCENARIO_UPDATE_IMAGE, self::SCENARIO_UPDATE_VIDEO],
                'message' => 'Необходимо заполнить ' . ($this->poll ? Yii::t('app', '«Точка контакта».') : ''),
                'when'    => function ($model) {
                    return $this->poll->is_auto;
                }
            ],
            [['comment_label'], 'string', 'max' => 120],
            [
                [
                    'created_at',
                    'updated_at',
                    'created_by',
                    'is_system',
                    'is_condition',
                    'is_source',
                    'updated_by',
                    'value_all',
                    'poll_id',
                    'rating_type',
                    'min_sum',
                    'position',
                    'show_category',
                    'show_name',
                    'show_portion',
                    'is_updated',
                    'main_question_type',
                    'comment_minlength',
                    'comment_maxlength',
                    'variants_element_type',
                    'mask',
                    'date_type',
                    'files_length',
                    'link_with_client_field',
                    'rewrite_linked_field',
                    'smiles_count',
                    'max_choose_variants',
                    'dictionary_id',
                    'dictionary_element_id',
                    'donor_columns',
                    'donor_rows',
                    'self_variant_minlength',
                    'self_variant_maxlength',
                    'max_points_calc_method',
                    'min_choose_variants',
                    'extra_question_type',
                    'extra_question_rate_from',
                    'extra_question_rate_to',
                    'min_choose_extra_variants',
                    'max_choose_extra_variants'
                ],
                'integer'
            ],
            [
                [
                    'description',
                    'text',
                    'detail_question',
                    'type',
                    'placeholder_text',
                    'select_placeholder_text',
                    'choose_type',
                    'mask_config',
                    'answers_from',
                    'smile_type',
                    'self_variant_text'
                ],
                'string'
            ],
            [
                ['name', 'service_name', 'skip_text', 'deleted_detail_question', 'self_variant_placeholder_text'],
                'string',
                'max' => 255
            ],
            [['card_column_text', 'category_column_text'], 'string', 'max' => 125],

            [['sub_description', 'self_variant_description'], 'string', 'max' => 65535],
            [
                [
                    'is_tmp',
                    'is_self_answer',
                    'is_deleted',
                    'is_required',
                    'show_category',
                    'show_name',
                    'show_portion',
                    'comment_enabled',
                    'for_all_rates',
                    'linked_client_field',
                    'dont_show_if_answered',
                    'matrix_settings',
                    'dropdown_variants',
                    'only_date_month',
                    'random_variants_order',
                    'random_categories_order',
                    'description_html',
                    'file_types',
                ],
                'safe'
            ],
            [
                ['poll_id'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => FoquzPoll::class,
                'targetAttribute' => ['poll_id' => 'id']
            ],
            [
                ['point_id'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => FoquzPointItem::class,
                'targetAttribute' => ['point_id' => 'id']
            ],
            [
                [
                    'skip',
                    'skip_variant',
                    'donor_chosen',
                    'donor_cols_chosen',
                    'show_labels',
                    'show_numbers',
                    'from_one',
                    'set_variants',
                    'extra_required',
                    'comment_required',
                    'skip_row',
                    'skip_column',
                    'show_tooltips',
                    'disable_select_category',
                    'variants_with_files',
                    'max_points_calc_recipient',
                    'self_variant_comment_required',
                    'self_variant_nothing',
                ],
                'boolean'
            ],
            [['donor'], 'exist', 'targetClass' => self::class, 'targetAttribute' => ['donor' => 'id']],
            [['donor', 'point_id'], 'validateDonorPoint'],
            ['dictionary_sort', 'in', 'range' => ['default', 'alphabetter', 'random']],
            ['dictionary_list_type', 'in', 'range' => ['list', 'tree']],
            [['description_html', 'sub_description'], 'filter', 'filter' => [$this, 'filterHTML']],
            ['description', 'filter', 'filter' => [$this, 'clearHTML']],
            [
                ['dictionary_element_id'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => DictionaryElement::class,
                'targetAttribute' => ['dictionary_element_id' => 'id']
            ],
            [
                ['donor_rows'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => self::class,
                'targetAttribute' => ['donor_rows' => 'id']
            ],
            [
                ['donor_columns'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => self::class,
                'targetAttribute' => ['donor_columns' => 'id']
            ],
            [
                'max_points_calc_method',
                'in',
                'range' => [
                    self::MAX_POINTS_CALC_METHOD_WITH_HIDDEN,
                    self::MAX_POINTS_CALC_METHOD_WITHOUT_HIDDEN
                ]
            ],
            [['extra_question_type'], 'in', 'range' => array_keys(self::EXTRA_QUESTION_TYPES)],
            [['file_types'], 'validateFileTypes', 'skipOnEmpty' => false],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'                            => 'ID',
            'created_at'                    => 'Created At',
            'updated_at'                    => 'Updated At',
            'created_by'                    => 'Created By',
            'updated_by'                    => 'Updated By',
            'poll_id'                       => 'Poll ID',
            'service_name'                  => 'Точка контакта',
            'name'                          => 'Название и текст вопроса',
            'description'                   => 'Описание вопроса',
            'description_html'              => 'Описание вопроса с HTML',
            'sub_description'               => 'Дополнительное описание',
            'text'                          => 'Текст',
            'is_self_answer'                => 'Свой вариант (произвольное поле)',
            'rating_type'                   => 'Тип оценки / выбора',
            'detail_question'               => 'Уточняющие вопросы',
            'is_system'                     => 'Is System',
            'position'                      => 'Позиция',
            'min_sum'                       => 'Мин. сумма блюда для оценки',
            'max_points_calc_method'        => 'Метод расчета максимального балла',
            'value_all'                     => 'Оценить все сразу',
            'date_type'                     => 'Date Type',
            'skip'                          => 'Пропуск оценки',
            'skip_text'                     => 'Текст пропуска оценки',
            'skip_row'                      => 'Общий пропуск для строки',
            'skip_column'                   => 'Общий пропуск для столбца',
            'show_tooltips'                 => 'Показывать подсказки для элементов и категорий справочника',
            'donor'                         => 'Использовать варианты ответов респондента из другого вопроса',
            'donor_chosen'                  => 'Варианты ответов: Выбранные варианты / Невыбранные варианты',
            'donor_cols_chosen'             => 'Варианты ответов: Выбранные варианты / Невыбранные варианты (столбцы)',
            'donor_column'                  => 'Вопрос-донор для столбцов',
            'donor_rows'                    => 'Вопрос-донор для строк',
            'show_labels'                   => 'Всегда отображать метки',
            'show_numbers'                  => 'Показывать числа над звёздами',
            'from_one'                      => 'Начинать шкалу с единицы',
            'dictionary_element_id'         => 'Элемент справочника для вопроса',
            'comment_required'              => 'Комментарий обязателен',
            'disable_select_category'       => 'Запретить выбор категории/подкатегории до разворачивания вложенных уровней',
            'variants_with_files'           => 'Варианты ответов с изображениями/видео (плиткой)',
            'max_points_calc_recipient'     => 'Считать максимум баллов за вопрос, исходя из фактически унаследованных вариантов',
            'self_variant_comment_required' => 'Комментарий к своему варианту обязателен',
            'self_variant_nothing'          => 'Свой вариант с опцией "Ничего из перечисленного"',
            'self_variant_description'      => 'Текст для тултипа в Своем варианте',
            'min_choose_variants'           => 'Минимальное количество выбранных вариантов',
            'extra_question_type'           => 'Тип дополнительного вопроса',
            'extra_question_rate_from'      => 'Минимальная оценка дополнительного вопроса',
            'extra_question_rate_to'        => 'Максимальная оценка дополнительного вопроса',
            'min_choose_extra_variants'     => 'Минимальное количество выбранных вариантов дополнительного вопроса',
            'max_choose_extra_variants'     => 'Максимальное количество выбранных вариантов дополнительного вопроса',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        $this->setTooltip();
    }

    public function afterSave($insert, $changedAttributes)
    {
        if (!$insert && (int)$this->main_question_type === self::TYPE_VARIANTS && (
                array_key_exists('is_self_answer', $changedAttributes) || array_key_exists('self_variant_text',
                    $changedAttributes)
            )) {
            $this->syncRecipients();
        }
        $this->setTooltip();
        parent::afterSave($insert, $changedAttributes);
    }

    private function setTooltip()
    {
        if ($this->main_question_type === FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
            if ($this->intermediateBlock) {
                if ($this->intermediateBlock->screen_type === FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_TEST5S) {
                    $this->tooltip['text'] = StringHelper::truncate(strip_tags($this->description), 250);
                } else {
                    $this->tooltip['text'] = StringHelper::truncate(strip_tags($this->intermediateBlock->text), 250);
                }
                $this->tooltip['type'] = 'Промежуточный блок / ' . $this->intermediateBlock->typeName;
            }
        } else {
            $this->tooltip['text'] = StringHelper::truncate(strip_tags($this->description), 250);
            $this->tooltip['type'] = $this->typeName;
        }
    }

    /**
     * @return ActiveQuery
     */
    public function getPoll()
    {
        return $this->hasOne(FoquzPoll::class, ['id' => 'poll_id']);
    }

    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    public function getPoint()
    {
        return $this->hasOne(FoquzPointItem::class, ['id' => 'point_id']);
    }

    /**
     * @return ActiveQuery | FoquzQuestionDetail[]
     */
    public function getQuestionDetails()
    {
        return $this->hasMany(FoquzQuestionDetail::class, ['foquz_question_id' => 'id'])
            ->orderBy('position');
    }

    /**
     * @return ActiveQuery | RecipientQuestionDetail[]
     */
    public function getQuestionDetailsActive()
    {
        return $this->getQuestionDetails()->where(['is_deleted' => 0]);
    }

    /**
     * @return ActiveQuery | RecipientQuestionDetail[]
     */
    public function getRecipientQuestionDetails(): ActiveQuery
    {
        return $this->hasMany(RecipientQuestionDetail::class, ['recipient_id' => 'id'])->orderBy('position');
    }

    /**
     * @return ActiveQuery | FoquzQuestionDetail[]
     */
    public function getRecipientsQuestionDetails(): ActiveQuery
    {
        return $this->hasMany(FoquzQuestionDetail::class, ['id' => 'question_detail_id'])
            ->via('recipientQuestionDetails')
            ->orderBy('position');
    }

    /**
     * @return ActiveQuery | FoquzQuestionDetail[]
     */
    public function getRecipientsQuestionDetailsActive(): ActiveQuery
    {
        return $this->getRecipientQuestionDetails()
            ->joinWith('questionDetail')
            ->where(['is_deleted' => 0]);
    }


    public function getQuestionSmiles()
    {
        return $this->hasMany(FoquzQuestionSmile::className(), ['foquz_question_id' => 'id']);
    }

    public function getQuestionLogic()
    {
        return $this->hasMany(FoquzPollQuestionsLogic::className(), ['question_id' => 'id'])->orderBy('position');
    }

    public function getFoquzPollQuestionViewLogics()
    {
        return $this->hasMany(FoquzPollQuestionViewLogic::class, ['question_id' => 'id'])->orderBy('sort');
    }

    public function getStarRatingOptions()
    {
        return $this->hasOne(FoquzQuestionStarRatingOptions::className(), ['foquz_question_id' => 'id']);
    }

    public function getIntermediateBlock()
    {
        return $this->hasOne(FoquzQuestionIntermediateBlockSetting::className(), ['question_id' => 'id']);
    }

    public function getAddressCodes()
    {
        return $this->hasOne(FoquzQuestionAddressCodes::class, ['question_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getQuestionImages()
    {
        return $this->hasMany(FoquzQuestionFile::class, ['question_id' => 'id'])->andWhere(['type' => 'image']);
    }

    /**
     * @return ActiveQuery
     */
    public function getQuestionVideos()
    {
        return $this->hasMany(FoquzQuestionFile::class, ['question_id' => 'id'])->andWhere(['type' => 'video']);
    }

    public function getFormFields()
    {
        return $this->hasMany(FoquzQuestionFormField::class, ['question_id' => 'id'])->orderBy('position');
    }

    /**
     * @return ActiveQuery
     */
    public function getQuestionFiles()
    {
        return $this->hasMany(FoquzQuestionFile::class, ['question_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getRightAnswer()
    {
        return $this->hasOne(FoquzQuestionRightAnswer::class, ['foquz_question_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getDishes()
    {
        return $this->hasMany(FoquzPollDishScore::class, ['question_id' => 'id']);
    }

    /**
     * @return array
     */
    public function getDetailFiles(): array
    {
        return FoquzFile::find()->where([
            'entity_type' => FoquzFile::TYPE_DETAIL,
            'entity_id'   => FoquzQuestionDetail::find()->select('id')->where([
                'foquz_question_id' => $this->id,
                'is_deleted'        => false
            ])
        ])->all();
    }

    /**
     * @return ActiveQuery
     */
    public function getCardSortingCategories(): ActiveQuery
    {
        return $this->hasMany(FoquzQuestionCardSortingCategory::class, ['foquz_question_id' => 'id']);
    }

    public function getFirstClick(): ActiveQuery
    {
        return $this->hasOne(FoquzQuestionFirstClick::class, ['foquz_question_id' => 'id']);
    }

    public function getFirstClickArea(): ActiveQuery
    {
        return $this->hasMany(FoquzQuestionFirstClickArea::class, ['question_id' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getDoneAnswers()
    {
        return $this
            ->hasMany(FoquzPollAnswer::class, ['foquz_poll_id' => 'poll_id'])
            ->where([
                'foquz_poll_answer.status' => [
                    FoquzPollAnswer::STATUS_DONE,
                    FoquzPollAnswer::STATUS_IN_PROGRESS
                ]
            ]);
    }

    /**
     * @return ActiveQuery | FoquzPollAnswerItem[]
     */
    public function getQuestionAnswerItems()
    {
        return $this->hasMany(FoquzPollAnswerItem::class, ['foquz_question_id' => 'id']);
    }

    /**
     * @return int
     */
    public function getQuestionAnswerItemsCount()
    {
        return (int)$this->getQuestionAnswerItems()->exists();
    }

    /**
     * @return ActiveQuery
     */
    public function getQuestionDoneAnswers()
    {
        return $this
            ->hasMany(FoquzPollAnswer::class, ['id' => 'foquz_poll_answer_id'])
            ->via('questionAnswerItems')
            ->where([
                FoquzPollAnswer::tableName() . '.status' => [
                    FoquzPollAnswer::STATUS_DONE,
                    FoquzPollAnswer::STATUS_IN_PROGRESS
                ]
            ]);
    }

    public function getNumberMediaType()
    {
        $array = [
            self::TYPE_TEXT  => 0,
            self::TYPE_IMAGE => 1,
            self::TYPE_VIDEO => 2,
        ];
        return $array[$this->type];
    }

    public function getAnswerType()
    {
        $type = 2;
        if (count($this->questionDetails) > 0) {
            $type = 0;
        } elseif ($this->comment_enabled || $this->is_self_answer) {
            $type = 1;
        }
        return $type;
    }

    public function getDifferentialRows()
    {
        return $this->hasMany(FoquzQuestionDifferentialRow::className(), ['question_id' => 'id'])->orderBy('position');
    }

    public function getQuestionDetailLangs()
    {
        return $this->hasMany(FoquzQuestionDetailLang::class, ['foquz_question_id' => 'id']);
    }

    /**
     * Gets query for [[FoquzQuestionMatrixElements]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMatrixElements()
    {
        return $this->hasMany(FoquzQuestionMatrixElement::class, ['foquz_question_id' => 'id']);
    }

    /**
     * Gets query for [[FoquzQuestionMatrixElements]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getActiveMatrixElements()
    {
        return $this->hasMany(FoquzQuestionMatrixElement::class,
            ['foquz_question_id' => 'id'])->onCondition(['is_deleted' => 0]);
    }


    /**
     * Gets query for [[DictionaryElement]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDictionaryElement()
    {
        return $this->hasOne(DictionaryElement::class, ['id' => 'dictionary_element_id']);
    }

    public function getDictionaryLangs()
    {
        return $this->hasMany(FoquzQuestionDetailLang::class, ['foquz_question_id' => 'id'])
            ->onCondition(['AND', ['foquz_question_detail_id' => null], ['NOT', ['detail_id' => null]]]);
    }

    /**
     * @return int
     */
    public function getCountQuestionDoneAnswers()
    {
        return count($this->questionDoneAnswers);
    }

    public function beforeSave($insert)
    {
        if ($insert && $this->position == 0) {
            $this->position = (int)FoquzQuestion::find()->where(['poll_id' => $this->poll_id])->andWhere([
                    '!=',
                    'position',
                    -1
                ])->count() + 1;
        }
        if (!is_a(Yii::$app, Application::className()) && Yii::$app->user->identity !== null) {
            $this->created_by = Yii::$app->user->id;
            $this->updated_by = Yii::$app->user->id;
        }
        return parent::beforeSave($insert);
    }

    public function validateDonorPoint($attribute)
    {
        if ($this->donor && $this->point_id) {
            $this->addError(
                $attribute,
                'Нельзя сохранить вопрос как точку контакта, так как в настройках вопроса используются ответы из другого вопроса'
            );
        }
    }

    public function validateFileTypes($attribute)
    {
        if (!$this->file_types || empty($this->file_types[0])) {
            $this->file_types = [];
            return;
        }
        if (!is_array($this->file_types)) {
            $this->addError($attribute, 'Типы файлов должны быть массивом');
            return;
        }
        $this->file_types = array_values(array_unique($this->file_types));
        if (array_diff($this->file_types, self::UPLOAD_TYPES)) {
            $this->addError($attribute,
                'Типы файлов должны быть из списка разрешенных: ' . implode(', ', self::UPLOAD_TYPES));
        }
    }

    public function getRatingTypeText()
    {
        $values = self::getRatingTypes();
        return $values[$this->rating_type];
    }

    public static function questionsListPoll($questions_list, $answer)
    {
        foreach ($questions_list as $key => $question_array) {
            $question_list[$key]['variants'] = $question_array['clarifyingQuestionVariants'];
            $question = self::findOne(['id' => $question_array['id']]);
            $answer_item = FoquzPollAnswerItem::findOne([
                'foquz_question_id'    => $question->id,
                'foquz_poll_answer_id' => $answer->id
            ]);
            $result = false;
            if ($answer_item) {
                if ($answer_item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_DATE) {
                    $decoded_date = json_decode($answer_item->answer);
                    if ($decoded_date !== null) {
                        $questions_list[$key]['answer'] = ($decoded_date->date ? $decoded_date->date : '') . ' ' . (str_replace(' ',
                                '', $decoded_date->time) ? str_replace(' ', '', $decoded_date->time) : '');
                    } else {
                        $questions_list[$key]['answer'] = '';
                    }
                } elseif ($answer_item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                    $selected = is_array($answer_item->detail_item) ? $answer_item->detail_item : json_decode($answer_item->detail_item);
                    $result['selectedVariantIndex'] = $selected;
                    $result['clarifyingQuestionCustomAnswer'] = $answer_item->self_variant != "" ? $answer_item->self_variant : null;
                    $result['comment'] = $answer_item->self_variant != "" ? $answer_item->self_variant : null;
                    $questions_list[$key]['answer'] = (object)$result;
                } elseif (in_array($answer_item->foquzQuestion->main_question_type,
                    [FoquzQuestion::TYPE_TEXT_ANSWER, FoquzQuestion::TYPE_ADDRESS])) {
                    $questions_list[$key]['answer'] = $answer_item->answer != "" ? $answer_item->answer : null;
                } elseif ($answer_item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_FILE_UPLOAD) {
                    $result = [
                        'files'    => ArrayHelper::getColumn($answer_item->getAnswerItemFiles()->select('file_path')->all(),
                            "file_path"),
                        'passedAt' => date('d.m.Y', strtotime($answer_item->created_at)),
                        'comment'  => $answer_item->answer != "" ? $answer_item->answer : null,
                    ];
                    $questions_list[$key]['answer'] = (object)$result;
                } elseif ($answer_item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_FORM) {
                    $values = [];
                    $dbAnswer = json_decode($answer_item->answer);
                    $properties = $answer_item->foquzQuestion->getFormFields()->select('id, name, link_with_client_field, linked_client_field')->all();
                    foreach ($properties as $property) {
                        $id = $property->id;
                        if (isset($dbAnswer->$id) && $dbAnswer->$id !== '') {
                            $values[] = [
                                'label'               => $property->name,
                                'value'               => $dbAnswer->$id,
                                'linkWithClientField' => $property->link_with_client_field,
                                'linkedClientField'   => ContactAdditionalField::getText($property->linked_client_field)
                            ];
                        }
                    }
                    $result['values'] = $values;
                    $questions_list[$key]['answer'] = (object)$result;
                } elseif ($answer_item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_PRIORITY) {
                    $question_list[$key]['variants'] = ArrayHelper::getColumn($question_list[$key]['variants'],
                        'question');
                    $questions_list[$key]['answer'] = [
                        'variants' => json_decode($answer_item->answer)
                    ];
                } else {
                    $clarifyingQuestionCustomAnswer = $answer_item->self_variant != '' ? $answer_item->self_variant : null;
                    if ($answer_item->is_self_variant) {
                        $result = [
                            "rating"                                 => $answer_item->rating,
                            "clarifyingQuestionSelectedVariantIndex" => null,
                            "clarifyingQuestionCustomAnswer"         => $clarifyingQuestionCustomAnswer,
                            'comment'                                => $answer_item->answer != "" ? $answer_item->answer : null
                        ];
                    } else {
                        if (isset($question_array['clarifyingQuestionVariants'])) {
                            $result = [
                                "rating"                         => $answer_item->rating,
                                'selectedVariantIndex'           => is_array($answer_item->detail_item) ? $answer_item->detail_item : (is_array(json_decode($answer_item->detail_item)) ? json_decode($answer_item->detail_item) : []),
                                "clarifyingQuestionCustomAnswer" => $clarifyingQuestionCustomAnswer,
                                'comment'                        => $answer_item->answer != "" ? $answer_item->answer : null
                            ];
                        } else {
                            $result = [
                                "rating"                                 => $answer_item->rating,
                                "clarifyingQuestionSelectedVariantIndex" => 0,
                                "clarifyingQuestionCustomAnswer"         => $clarifyingQuestionCustomAnswer,
                                'comment'                                => $answer_item->answer != "" ? $answer_item->answer : null
                            ];
                        }
                    }
                    if (!$result['rating']) {
                        $result['rating'] = $answer_item->rating;
                    }
                    if ($answer_item->detail_item != "") {
                        $selected = is_array($answer_item->detail_item) ? $answer_item->detail_item : json_decode($answer_item->detail_item);
                        $result['selectedVariantIndex'] = $selected;
                        $result['clarifyingQuestionCustomAnswer'] = $clarifyingQuestionCustomAnswer;
                        $result['comment'] = $answer_item->answer != "" ? $answer_item->answer : null;
                    }
                    if ($question_array['dishes'] === true) {
                        foreach ($answer->getFoquzPollDishes()->where([
                            '>',
                            'score',
                            0
                        ])->orderBy('score desc')->all() as $dishScore) {
                            if ($answer->order) {
                                $dishOrderData = $answer->order->getDishes()->where(['dish_id' => $dishScore->dish_id])->one();
                                $result['dishRatings'][] = [
                                    'dish'     => ['name' => $dishScore->dish->name],
                                    'quantity' => $dishOrderData->quantity ?? 0,
                                    'value'    => $dishScore->score
                                ];
                            }
                        }
                        $result['comment'] = $answer_item->answer != "" ? $answer_item->answer : null;
                    }
                    if ($answer_item->foquzQuestion->is_system) {
                        if ($answer_item->foquzQuestion->service_name === 'Оператор') {
                            $result['employee']['name'] = $answer->order && $answer->order->operator ? $answer->order->operator->name : '';
                        } elseif ($answer_item->foquzQuestion->service_name === 'Курьер') {
                            $result['employee']['name'] = $answer->order && $answer->order->driver ? $answer->order->driver->name : '';
                        }
                    }
                    $questions_list[$key]['answer'] = (object)$result;
                }
            } else {
                $questions_list[$key]['answer'] = null;
            }

            if ($question->type == 'image') {
                $questions_list[$key]['imageUrls'] = ArrayHelper::getColumn($question->questionFiles,
                    function ($element) {
                        return [
                            'file_path' => '/' . $element->file_path,
                            'file_text' => $element->file_text
                        ];
                    });
            }
            if ($question->type == 'video') {
                $questions_list[$key]['videoUrls'] = ArrayHelper::getColumn($question->getQuestionFiles()->where(['type' => 'video'])->all(),
                    function ($element) {
                        if ($element->attachment_type == 'file') {
                            return [
                                'file_path' => '/' . $element->file_path,
                                'file_text' => $element->file_text
                            ];
                        } else {
                            return [
                                'file_text' => $element->file_text,
                                'link'      => 'https://www.youtube.com/embed/' . $element->attachment_content,
                                'imageLink' => $element->getImage()
                            ];
                        }
                    });
            }
            //unset($questions_list[$key][$question->rating_type==2 ? 'clarifyingQuestionVariants' : 'variants']);
        }
        return $questions_list;
    }

    public function collectClarifyingQuestionVariants()
    {
        return isset($this->questionDetails[0]) ? ArrayHelper::getColumn($this->questionDetails, function ($element) {
            return ['id' => $element->id, 'variant' => $element->question];
        }) : null;
    }

    public function collectVariants($multiLang = true): array
    {
        $array = [];
        if ($this->main_question_type === self::TYPE_FILIAL) {
            return $this->filialActualList();
        }

        if ($this->main_question_type === self::TYPE_DICTIONARY) {
            $variants = [];
            $elements = [];
            $langs = [];
            $detailIDs = json_decode($this->detail_question) ?? [];
            if (!empty($detailIDs) && is_array($detailIDs)) {
                $elements = DictionaryElement::findAll($detailIDs);
                $elements = ArrayHelper::index($elements, 'id');
            }
            foreach (json_decode($this->detail_question) as $key => $value) {
                if (isset($elements[$value])) {
                    $element = $elements[$value];
                    if (Yii::$app->requestedRoute !== 'foquz/contact-points') { //TODO: refactor
                        $path = $element->fullPath;
                    } else {
                        $path = '';
                    }
                    $variants[] = [
                        'id'          => $value,
                        'value'       => $element->title,
                        'description' => $element->description,
                        'path'        => $path,
                    ];
                }
            }
            return $variants;
        }
        if ($this->donor) {
            if ($details = $this->recipientQuestionDetails) {

                foreach ($details as $detail) {
                    if (!$detail->question_detail_id && !$detail->dictionary_element_id) {
                        $variant = $this->getMainDonor()->self_variant_text;
                        $variant = (!empty($variant)) ? $variant : 'Свой вариант';
                        $isDeleted = $detail->questionDetail->is_deleted ?? false;
                    } elseif ($detail->question_detail_id) {
                        $variant = $detail->questionDetail->question;
                        $isDeleted = $detail->questionDetail->is_deleted ?? false;
                        if ($detail->is_self_answer) {
                            $file = FoquzFile::findOne([
                                'entity_type' => FoquzFile::TYPE_SELF_VARIANT,
                                'entity_id' => $detail->id
                            ]);
                        } else {
                            $file = $detail->questionDetail->file;
                        }
                    } elseif ($detail->dictionary_element_id && Yii::$app->requestedRoute !== 'foquz/contact-points') { //TODO: refactor

                        $dictionary_list_type = $this->getMainDonor()->dictionary_list_type;
                        if ($dictionary_list_type === 'tree') {
                            $variant = $detail->dictionaryElement->fullPath;
                        } else {
                            $variant = $detail->dictionaryElement->title;
                        }
                        $isDeleted = !in_array($detail->dictionary_element_id,
                            json_decode($this->getMainDonor()->detail_question) ?? []);

                        if ($detail->is_self_answer) {
                            $file = FoquzFile::findOne([
                                'entity_type' => FoquzFile::TYPE_SELF_VARIANT,
                                'entity_id' => $detail->id
                            ]);
                        } else {
                            $file = $detail?->questionDetail?->file;
                        }
                    } else {
                        $variant = '';
                    }

                    $is_self_answer = $detail->is_self_answer;
                    if (!$detail->question_detail_id && !$detail->dictionary_element_id) {
                        $is_self_answer = true;
                    }
                    $array[] = ArrayHelper::merge(
                        ArrayHelper::toArray($detail),
                        [
                            'question'         => $variant,
                            'variant'          => $variant,
                            'extra_question'   => 0,
                            'is_self_answer'   => $is_self_answer,
                            'description'      => $detail->description,
                            'is_deleted'       => $isDeleted ?? false,
                            'need_extra'       => $detail->need_extra,
                            'comment_required' => $detail->comment_required,
                            'file_id'          => $file->id ?? null,
                            'file_url'         => $file->fileUrl ?? null,
                            'preview_url'      => $file->previewUrl ?? null,
                            'random_exclusion' => $detail->random_exclusion,
                        ]
                    );

                }
                if ($this->detail_question) {
                    $details = $this->questionDetails;
                    foreach ($details as $detail) {
                        if (!$detail->extra_question) {
                            continue;
                        }
                        if ($detail->extra_question && $detail->question === 'Вариант ' . $detail->position) {
                            $detail->question = '';
                        }
                        $array[] = ArrayHelper::merge(
                            ArrayHelper::toArray($detail),
                            [
                                'variant'        => empty($detail->is_empty) ? $detail->question : '',
                                'position'       => $detail->position,
                                'is_deleted'     => $detail->is_deleted ?? false,
                                'extra_question' => $detail->extra_question,
                                'file_id'        => $detail->file->id ?? null,
                                'file_url'       => $detail->file->fileUrl ?? null,
                                'preview_url'    => $detail->file->previewUrl ?? null,
                            ]
                        );
                    }
                }

                if ($this->extra_question_type === self::EXTRA_QUESTION_DIFFERENT_EACH) {
                    $details = $this->questionDetails;

                    foreach ($details as $element) {
                        file_put_contents(\Yii::$app->getRuntimePath() . '/fff.txt', print_r($element, true), FILE_APPEND);
                        $file_id =  $element?->file?->id;
                        $file_url = $element?->file?->fileUrl;
                        $preview_url = $element?->file?->previewUrl;

                        $question_detail_id = $element->question_detail_id;
                        if (!$question_detail_id) {
                            $question_detail_id = $element->recipient_question_detail_id;
                        }

                        $array[] = [
                            'id'                            => $element->id,
                            'type'                          => $element->type,
                            'variant'                       => empty($element->is_empty) ? $element->question : '',
                            'description'                   => $element->description,
                            'position'                      => $element->position,
                            'points'                        => $this->poll->point_system ? $element->points : 0,
                            'without_points'                => $element->without_points,
                            'is_deleted'                    => $element->is_deleted,
                            'extra_question'                => $element->extra_question,
                            'need_extra'                    => $element->need_extra,
                            'comment_required'              => $element->comment_required,
                            'dictionary_element_id'         => $element->dictionary_element_id,
                            'file_id'                       => $file_id,
                            'file_url'                      => $file_url,
                            'preview_url'                   => $preview_url,
                            'detail_question'               => $element->detail_question,
                            'question_detail_id'            => $question_detail_id,
                            'extra_question_rate_from'      => $element->extra_question_rate_from,
                            'extra_question_rate_to'        => $element->extra_question_rate_to,
                            'extra_required'                => $element->extra_required,
                            'min_choose_extra_variants'     => $element->min_choose_extra_variants,
                            'max_choose_extra_variants'     => $element->max_choose_extra_variants,
                            'variants_with_files'           => $element->variants_with_files,
                            'self_variant_text'             => $element->self_variant_text,
                            'self_variant_placeholder_text' => $element->self_variant_placeholder_text,
                            'variants_element_type'         => $element->variants_element_type,
                            'for_all_rates'                 => $element->for_all_rates,
                            'placeholder_text'              => $element->placeholder_text,
                            'self_variant_minlength'        => $element->self_variant_minlength,
                            'self_variant_maxlength'        => $element->self_variant_maxlength,
                            'text_variant_minlength'        => $element->text_variant_minlength,
                            'text_variant_maxlength'        => $element->text_variant_maxlength,
                            'is_self_answer'                => $element->is_self_answer,
                            'random_exclusion'              => $element->random_exclusion,
                        ];
                    }
                }
            } else {
                $array = ArrayHelper::toArray(FoquzQuestionDetail::findAll([
                    'foquz_question_id' => [
                        $this->getMainDonor()->id,
                        $this->id
                    ]
                ]));
                if ($this->getMainDonor()->is_self_answer) {
                    $question = $this->getMainDonor()->self_variant_text;
                    $array[] = [
                        'id' => '-1',
                        'question' => (!empty($question)) ? $question : 'Свой вариант',
                        'variant' => (!empty($question)) ? $question : 'Свой вариант',
                    ];
                }
            }
        } else {
            /** @var array $questionDetails */
            $cacheKey = "foquz-question-details-{$this->id}";
            $questionDetails = Yii::$app->cache->getOrSet($cacheKey, function () {
                return FoquzQuestionDetail::find()
                    ->where(['foquz_question_id' => $this->id])
                    ->andWhere(['is_deleted' => false])
                    ->orderBy('position')
                    ->asArray()
                    ->all();
            }, 60*60, new TagDependency(['tags' => $cacheKey]));

            $questionDetailFiles = [];
            if ($this->variants_with_files) {
                $questionDetailFiles = FoquzFile::find()
                    ->where([
                        'entity_type' => FoquzFile::TYPE_DETAIL,
                        'entity_id' => ArrayHelper::getColumn($questionDetails, 'id')
                    ])
                    ->indexBy('entity_id')
                    ->all();
            }

            foreach ($questionDetails as $key => $questionDetail) {
                if (!isset($questionDetailFiles[$questionDetail['id']])) {
                    continue;
                }
                $questionDetails[$key]['file'] = $questionDetailFiles[$questionDetail['id']];
            }
            $variantWithFiles = $this->variants_with_files;
            $array = isset($questionDetails[0]) ? ArrayHelper::getColumn($questionDetails, function (array $element) use ($variantWithFiles) {
                $file_id = null;
                $file_url = null;
                $preview_url = null;
                if ($this->variants_with_files && !empty($element['file'])) {
                    $file_id = $element['file']['id'];
                    $file_url = $element['file']['fileUrl'];
                    $preview_url = $element['file']['previewUrl'];
                }

                if ($this->extra_question_type !== self::EXTRA_QUESTION_DIFFERENT_EACH) {
                    $file_id = $variantWithFiles ? $file_id : null;
                    $file_url = $variantWithFiles ? $file_url : null;
                    $preview_url = $variantWithFiles ? $preview_url : null;
                }

                $ret = [
                    'id'                            => $element['id'],
                    'type'                          => $element['type'],
                    'variant'                       => empty($element['is_empty']) ? $element['question'] : '',
                    'description'                   => $element['description'],
                    'position'                      => $element['position'],
                    'points'                        => $this->poll->point_system ? $element['points'] : 0,
                    'without_points'                => $element['without_points'],
                    'is_deleted'                    => $element['is_deleted'],
                    'extra_question'                => $element['extra_question'],
                    'need_extra'                    => $element['need_extra'],
                    'comment_required'              => $element['comment_required'],
                    'dictionary_element_id'         => $element['dictionary_element_id'],
                    'file_id'                       => $file_id,
                    'file_url'                      => $file_url,
                    'preview_url'                   => $preview_url,
                    'detail_question'               => $element['detail_question'],
                    'question_detail_id'            => $element['question_detail_id'],
                    'extra_question_rate_from'      => $element['extra_question_rate_from'],
                    'extra_question_rate_to'        => $element['extra_question_rate_to'],
                    'extra_required'                => $element['extra_required'],
                    'min_choose_extra_variants'     => $element['min_choose_extra_variants'],
                    'max_choose_extra_variants'     => $element['max_choose_extra_variants'],
                    'variants_with_files'           => $element['variants_with_files'],
                    'self_variant_text'             => $element['self_variant_text'],
                    'self_variant_placeholder_text' => $element['self_variant_placeholder_text'],
                    'variants_element_type'         => $element['variants_element_type'],
                    'for_all_rates'                 => $element['for_all_rates'],
                    'placeholder_text'              => $element['placeholder_text'],
                    'self_variant_minlength'        => $element['self_variant_minlength'],
                    'self_variant_maxlength'        => $element['self_variant_maxlength'],
                    'text_variant_minlength'        => $element['text_variant_minlength'],
                    'text_variant_maxlength'        => $element['text_variant_maxlength'],
                    'is_self_answer'                => $element['is_self_answer'],
                    'random_exclusion'              => $element['random_exclusion'],
                ];

                if ($element['is_self_answer'] && $element['variants_with_files']) {
                    $tempElement = new FoquzQuestionDetail([
                        'id'                    => $element['id'],
                        'is_self_answer'        => $element['is_self_answer'],
                        'variants_with_files'   => $element['variants_with_files'],
                    ]);
                    $ret['selfVariantFile'] = $tempElement->getSelfVariantFile();
                }

                if ($this->extra_question_type === self::EXTRA_QUESTION_DIFFERENT_EACH) {
                    $ret['detail_question'] = $element['detail_question'];
                    $ret['question_detail_id'] = $element['question_detail_id'];
                    $ret['extra_question_rate_from'] = $element['extra_question_rate_from'];
                    $ret['extra_question_rate_to'] = $element['extra_question_rate_to'];
                    $ret['extra_required'] = $element['extra_required'];
                    $ret['min_choose_extra_variants'] = $element['min_choose_extra_variants'];
                    $ret['max_choose_extra_variants'] = $element['max_choose_extra_variants'];
                    $ret['variants_with_files'] = $element['variants_with_files'];
                    $ret['variants_element_type'] = $element['variants_element_type'];
                    $ret['for_all_rates'] = $element['for_all_rates'];
                    $ret['is_self_answer'] = $element['is_self_answer'];

                    if ($element['is_self_answer']) {
                        $ret['self_variant_text'] = $element['self_variant_text'];
                        $ret['self_variant_placeholder_text'] = $element['self_variant_placeholder_text'];
                        $ret['self_variant_minlength'] = $element['self_variant_minlength'];
                        $ret['self_variant_maxlength'] = $element['self_variant_maxlength'];

                        if ($element['variants_with_files']) {
                            $tempElement = new FoquzQuestionDetail([
                                'id'                    => $element['id'],
                                'is_self_answer'        => $element['is_self_answer'],
                                'variants_with_files'   => $element['variants_with_files'],
                            ]);
                            $ret['selfVariantFile'] = $tempElement->getSelfVariantFile();
                        }
                    } else {
                        $tempElement = new FoquzQuestionDetail([
                            'id' => $element['id'],
                        ]);
                        $f = $tempElement->file;
                        if ($f) {
                            $ret['file_id'] = $f->id;
                            $ret['file_url'] = $f->getFileUrl();
                            $ret['preview_url'] = $f->getPreviewUrl();
                        }
                    }
                    if ($element['variants_element_type'] === self::VARIANT_ELEMENT_TYPE_TEXT) {
                        $ret['placeholder_text'] = $element['placeholder_text'];
                        $ret['text_variant_minlength'] = $element['text_variant_minlength'];
                        $ret['text_variant_maxlength'] = $element['text_variant_maxlength'];
                    }
                    if (!$element['need_extra']) {
                        $ret['detail_question'] = null;
                        $ret['extra_question_rate_from'] = null;
                        $ret['extra_question_rate_to'] = null;
                        $ret['min_choose_extra_variants'] = null;
                        $ret['variants_with_files'] = 0;
                        $ret['for_all_rates'] = 0;
                        $ret['is_self_answer'] = 0;
                    }
                }
                return $ret;
            }) : [];
        }

        if ($multiLang) {
            foreach ($array as &$item) {
                if (isset($item['id'])) {
                    $detailLangs = FoquzQuestionDetailLang::findAll([
                        'foquz_question_id'        => $this->id,
                        'foquz_question_detail_id' => array_key_exists('question_detail_id',
                            $item) ? $item['question_detail_id'] : $item['id']
                    ]);
                    if ($detailLangs) {
                        $item['detailLangs'] = $detailLangs;
                    }
                }
            }
        }
        return $array;
    }

    //старый метод, почти как есть, чтобы не сломать
    public static function createQuestionFromPoint($pollId, $point, $item, $isCondition, $position): FoquzQuestion
    {
        $extraQuestionService = new ExtraQuestionService();
        $isSystem = $point->is_system;
        $question = null;
        $source_details = null;
        $source_matrix_elements = null;
        $source_files = null;
        $source_fields = null;
        $source_addresses = null;
        $source_smiles = null;
        $image_file = null;
        $source_priority_settings = null;

        /** @var FoquzQuestion $source_question */
        $source_question = FoquzQuestion::find()->where([
            'point_id'  => $point->id,
            'is_source' => 1,
        ])->andWhere([
            'in',
            'company_id',
            [null, Yii::$app->user->identity->company->id]
        ])->orderBy('updated_at DESC')->one();

        if (!$source_question && $point->is_system) {
            $source_question = $point->createNewSystemSourceQuestion(Yii::$app->user->identity->company->id);
        }

        $question = new FoquzQuestion();
        if ($source_question) {
            $question->attributes = $source_question->attributes;
            $question->is_source = 0;
            $source_details = $source_question->questionDetails;
            $source_matrix_elements = $source_question->matrixElements;
            $source_files = $source_question->questionFiles;
            $source_end_screen_logos = $source_question->foquzQuestionEndScreenLogos;
            $source_fields = $source_question->formFields;
            $source_addresses = $source_question->addressCodes;
            $source_smiles = $source_question->questionSmiles;
            $source_priority_settings = $source_question->foquzQuestionPrioritySettings;
        } else {
            $question->is_source = 1;
            $question->service_name = $point->name;
        }
        $question->poll_id = $pollId;
        $question->is_condition = $isCondition ? 1 : 0;
        $question->point_id = $point->id;
        $question->company_id = Yii::$app->user->identity->company->id;
        $question->save();

        if ($question->rating_type !== FoquzQuestion::RATING_DISHES) {
            $question->value_all = 0;
        }


        $question->position = $position; //isset($item['point_index']) ? $item['point_index'] + $max_position : $max_position;
        $question->save();

        if ($image_file) {
            $basePath = "img/" . $image_file;
            $fullPath = Yii::getAlias("@app/web/{$basePath}");
            $file = new FoquzQuestionFile([
                'question_id'    => $question->id,
                'file_path'      => $basePath,
                'file_full_path' => $fullPath,
                'type'           => 'image',
                'created_at'     => time()
            ]);
            $file->save();
        }
        if ($source_details) {
            $sourceDetailsTemp = [];
            foreach ($source_details as $detail) {
                $question_detail = new FoquzQuestionDetail([
                    'foquz_question_id'             => $question->id,
                    'type'                          => $detail->type,
                    'question'                      => $detail->question,
                    'description'                   => $detail->description,
                    'is_empty'                      => $detail->is_empty,
                    'position'                      => $detail->position,
                    'extra_question'                => $detail->extra_question,
                    'need_extra'                    => $detail->need_extra,
                    'comment_required'              => $detail->comment_required,
                    'detail_question'               => $detail->detail_question,
                    'extra_question_rate_from'      => $detail->extra_question_rate_from,
                    'extra_question_rate_to'        => $detail->extra_question_rate_to,
                    'extra_required'                => $detail->extra_required,
                    'min_choose_extra_variants'     => $detail->min_choose_extra_variants,
                    'max_choose_extra_variants'     => $detail->max_choose_extra_variants,
                    'variants_with_files'           => $detail->variants_with_files,
                    'self_variant_text'             => $detail->self_variant_text,
                    'self_variant_placeholder_text' => $detail->self_variant_placeholder_text,
                    'variants_element_type'         => $detail->variants_element_type,
                    'question_detail_id'            => $detail->question_detail_id,
                ]);
                $question_detail->save();

                $sourceDetailsTemp[$detail->id] = $question_detail;

                if ($detail->file) {
                    (new FileService())->copyFile($detail->file, $question_detail->id);
                }
            }

            $extraQuestionService->fixQuestionDetailId($sourceDetailsTemp);
            unset($sourceDetailsTemp);
        }
        if ($source_fields) {
            foreach ($source_fields as $form_field) {
                $form_field = new FoquzQuestionFormField([
                    'question_id'            => $question->id,
                    'name'                   => $form_field->name,
                    'is_required'            => $form_field->is_required,
                    'mask_type'              => $form_field->mask_type,
                    'variants_type'          => $form_field->variants_type,
                    'comment_minlength'      => $form_field->comment_minlength,
                    'comment_maxlength'      => $form_field->comment_maxlength,
                    'link_with_client_field' => $form_field->link_with_client_field,
                    'linked_client_field'    => $form_field->linked_client_field,
                    'rewrite_linked_field'   => $form_field->rewrite_linked_field,
                    'position'               => $form_field->position,
                    'placeholder_text'       => $form_field->placeholder_text,
                    'mask_config'            => $form_field->mask_config,
                ]);
                $form_field->save();
            }
        }
        if ($source_addresses) {
            $addresses = new FoquzQuestionAddressCodes([
                'question_id' => $question->id,
                'regions'     => $source_addresses->regions,
                'districts'   => $source_addresses->districts,
                'cities'      => $source_addresses->cities,
                'streets'     => $source_addresses->streets
            ]);
            $addresses->save();
        }

        if ($source_smiles) {
            foreach ($source_smiles as $sSmile) {
                $smile = new FoquzQuestionSmile();
                $smile->attributes = $sSmile->attributes;
                $smile->foquz_question_id = $question->id;
                $smile->save();
            }
        }

        if ($source_priority_settings) {
            $prioritySettings = new FoquzQuestionPrioritySettings([
                'foquz_question_id' => $question->id,
                'reorder_required'  => $source_priority_settings->reorder_required,
            ]);
            $prioritySettings->save();
        }

        if ($source_question && $source_question->npsRatingSetting) {
            $nps_rating_setting = new FoquzQuestionNpsRatingSetting([
                'foquz_question_id' => $question->id,
                'design'            => $source_question->npsRatingSetting->design,
                'start_point_color' => $source_question->npsRatingSetting->start_point_color,
                'end_point_color'   => $source_question->npsRatingSetting->end_point_color,
                'start_label'       => $source_question->npsRatingSetting->start_label,
                'end_label'         => $source_question->npsRatingSetting->end_label
            ]);
            $nps_rating_setting->save();
        }

        if ($source_question && $source_question->starRatingOptions) {
            $starOptions = new FoquzQuestionStarRatingOptions([
                'foquz_question_id'        => $question->id,
                'color'                    => $source_question->starRatingOptions->color,
                'count'                    => $source_question->starRatingOptions->count,
                'size'                     => $source_question->starRatingOptions->size,
                'labels'                   => $source_question->starRatingOptions->labels,
                'extra_question_rate_from' => $source_question->starRatingOptions->extra_question_rate_from,
                'extra_question_rate_to'   => $source_question->starRatingOptions->extra_question_rate_to

            ]);
            $starOptions->save();
        }

        if ($source_question && $source_question->intermediateBlock) {
            $pollQuestionIntermediateBlock = new FoquzQuestionIntermediateBlockSetting();
            $pollQuestionIntermediateBlock->attributes = $source_question->intermediateBlock->attributes;
            $pollQuestionIntermediateBlock->question_id = $question->id;
            $pollQuestionIntermediateBlock->save();
            if ($source_question->intermediateBlock->socNetworks) {
                $pollQuestionIntermediateBlockNetworks = new FoquzQuestionIntermediateBlockSettingSocNetworks();
                $pollQuestionIntermediateBlockNetworks->attributes = $source_question->intermediateBlock->socNetworks->attributes;
                $pollQuestionIntermediateBlockNetworks->intermediate_block_id = $pollQuestionIntermediateBlock->id;
                $pollQuestionIntermediateBlockNetworks->save();
            }
        }

        if ($source_question && count($source_question->differentialRows) > 0) {
            foreach ($source_question->differentialRows as $row) {
                $newRow = new FoquzQuestionDifferentialRow([
                    'question_id' => $question->id,
                    'start_label' => $row->start_label,
                    'end_label'   => $row->end_label,
                    'position'    => $row->position
                ]);
                $newRow->save();
            }
        }

        if ($source_question && $source_question->semDifSetting) {
            $newSemDifSetting = new FoquzQuestionSemDifSetting([
                'foquz_question_id' => $question->id,
                'form'              => $source_question->semDifSetting->form,
                'start_point_color' => $source_question->semDifSetting->start_point_color,
                'end_point_color'   => $source_question->semDifSetting->end_point_color
            ]);
            $newSemDifSetting->save();
        }

        if ($source_matrix_elements) {
            foreach ($source_matrix_elements as $element) {
                $matrix_element = new FoquzQuestionMatrixElement();
                $matrix_element->attributes = $element->attributes;
                $matrix_element->foquz_question_id = $question->id;
                $matrix_element->save();

                foreach ($element->variants as $variant) {
                    $matrix_element_variant = new FoquzQuestionMatrixElementVariant();
                    $matrix_element_variant->attributes = $variant->attributes;
                    $matrix_element_variant->matrix_element_id = $matrix_element->id;
                    $matrix_element_variant->save();
                }
            }
        }

        if ($source_files) {
            $i = 0;
            foreach ($source_files as $file) {
                $new_file = new FoquzQuestionFile();
                $new_file->attributes = $file->attributes;
                $new_file->question_id = $question->id;
                if (strlen($new_file->file_path) > 18) {
                    $path_array = explode('/', $file->file_path);
                    $fileName = array_pop($path_array);

                    $oldFileFullPathParts = explode('/', $file->file_full_path);
                    $oldFileFullPathLastPart = array_pop($oldFileFullPathParts);

                    if ($fileName != $oldFileFullPathLastPart) {
                        $file->file_full_path = $file->file_full_path . '/' . $fileName;
                    }

                    $new_file->file_path = str_replace('/' . $file->question_id . '/', '/' . $question->id . '/',
                        $file->file_path);
                    $new_file->file_path = str_replace('/contact-points/', '/' . $question->id . '/', $file->file_path);
                    $new_file->file_full_path = str_replace('/' . $file->question_id, '/' . $question->id,
                        $file->file_full_path);
                    $new_file->file_full_path = str_replace('/contact-points', '/' . $question->id,
                        $file->file_full_path);

                    if (stristr($new_file->file_full_path, '/var/www/opros') !== false && dirname(__DIR__,
                            3) !== '/var/www/opros') {
                        $new_file->file_full_path = str_replace('/var/www/opros', dirname(__DIR__, 3),
                            $new_file->file_full_path);
                    }

                    $newFileDir = str_replace($fileName, '', $new_file->file_full_path);

                    if (!is_dir($newFileDir) && !file_exists($newFileDir)) {
                        //echo str_replace('opros.doxsw.com','foquz.doxsw.com',$new_file->file_full_path);
                        mkdir($newFileDir, 0777, true);
                    }

                    // $newFileName = time() . $i . '.' . explode('.', $fileName)[1];
                    // $new_file->file_path = str_replace($fileName, $newFileName, $new_file->file_path);
                    // var_dump($file->file_full_path);
                    // var_dump($new_file->file_full_path);
                    // die();
                    try {
                        copy($file->file_full_path, $new_file->file_full_path);
                    } catch (\Exception $e) {
                        var_dump($file->file_full_path);
                        var_dump($new_file->file_full_path);
                        die();
                    }

                    if (file_exists($file->file_full_path . '.jpg')) {
                        copy($file->file_full_path . '.jpg', $new_file->file_full_path . '.jpg');
                    }
                }
                $new_file->save();
                $i++;
            }
        }

        if ($source_end_screen_logos) {
            $i = 0;
            foreach ($source_end_screen_logos as $file) {
                $new_file = new FoquzQuestionEndScreenLogo();
                $new_file->attributes = $file->attributes;
                $new_file->foquz_question_id = $question->id;
                if (strlen($new_file->logo) > 5) {
                    $path_array = explode('/', $file->logo);
                    $fileName = array_pop($path_array);

                    $new_file->logo = str_replace('/' . $file->foquz_question_id . '/', '/' . $question->id . '/',
                        $file->logo);
                    $new_file->logo = str_replace('/contact-points/', '/' . $question->id . '/', $file->logo);

                    $newFileDir = str_replace($fileName, '', $new_file->logo);

                    if (!is_dir(Yii::getAlias('@app/web') . '/' . $newFileDir) && !file_exists(Yii::getAlias('@app/web') . '/' . $newFileDir)) {
                        mkdir(Yii::getAlias('@app/web') . '/' . $newFileDir, 0777, true);
                    }

                    try {
                        copy(Yii::getAlias('@app/web') . '/' . $file->logo,
                            Yii::getAlias('@app/web') . '/' . $new_file->logo);
                    } catch (\Exception $e) {
                        var_dump($file->logo);
                        var_dump($new_file->logo);
                        die();
                    }

//                    if (file_exists($file->logo.'.jpg')) {
//                        copy(Yii::getAlias('@app/web').'/'.$file->logo.'.jpg', Yii::getAlias('@app/web').'/'.$new_file->logo.'.jpg');
//                    }
                }
                $new_file->save();
                $i++;
            }
        }
//        if ($isSystem) {
//            $answers = ['Скорость ответа', 'Вежливость', 'Знание меню'];
//            foreach ($answers as $answer) {
//                $question_detail = new FoquzQuestionDetail(['foquz_question_id' => $question->id, 'question' => $answer]);
//                $question_detail->save();
//            }
//        }
        return $question;
    }

    /**
     * @return ActiveQuery
     * точки контакта с условиями в опросы
     */
    public function getPointSelected()
    {
        return $this->hasMany(FoquzPointSelected::class,
            ['foquz_point_item_id' => 'point_id', 'foquz_poll_id' => 'poll_id']);
    }

    public function grabChooseContent($random = false)
    {
        $choose = [];

        foreach ($this->getQuestionFiles()->orderBy('position')->all() as $file) {
            $choose[] = [
                'id'          => $file->id,
                'url'         => $file->link,
                'src'         => $file->getImage() ? $file->getImage() : '',
                'poster'      => $file->getImage() ? $file->getImage() : '',
                'description' => $file->description,
                'points'      => $file->points
            ];
        }

        if ($random) {
            shuffle($choose);
        }

        return $choose;
    }

    public function grabMatrixSettings($random = false)
    {
        $settings = json_decode($this->matrix_settings);
        if (!isset($settings->minRowsReq) && !empty($settings) && !empty($settings->rows)) {
            $settings->minRowsReq = count($settings->rows);
        }
        if ($random && $settings) {
            shuffle($settings->rows);
        }
        return $settings;
    }

    public function grabMediaContent()
    {
        $media = [];

        foreach ($this->questionFiles as $file) {
            $array = [
                'id'          => $file->id,
                'url'         => $file->link,
                'src'         => $file->getImage() ?: '',
                'poster'      => $file->getImage() ?: '',
                'label'       => $file->file_text,
                'description' => $file->description,
                'position'    => $file->position,
            ];
            if ($file->langs) {
                $array['langFiles'] = $file->langs;
            }
            $media[] = $array;
        }

        return $media;
    }

    public function collectVariantsArray(
        ?FoquzPollAnswer $answer,
        FoquzPollAnswerItem $answerItem = null,
        $random = false
    ) {
        $variants = [];
        if ($this->rating_type === self::RATING_DISHES) {
            $answerDishesRatings = [];
            foreach ($answer->foquzPollDishes as $fpd) {
                $answerDishesRatings[$fpd->dish_id] = $fpd->score;
            }
            foreach ($answer->order->dishes as $orderDish) {
                if (isset($answerDishesRatings[$orderDish->dish->id])) {
                    $variants[] = [
                        'dishId'   => $orderDish->dish->id,
                        'value'    => $orderDish->dish->name,
                        'stars'    => $answerDishesRatings[$orderDish->dish->id] ? $answerDishesRatings[$orderDish->dish->id] : 0,
                        'price'    => $orderDish->sum,
                        'category' => isset($orderDish->dish->category) && $orderDish->dish->category && $orderDish->dish->category->name ? $orderDish->dish->category->name : '',
                        'serving'  => [
                            'count'  => $orderDish->quantity,
                            'weight' => rand(250, 1200)
                        ]
                    ];
                }
            }
        } elseif ($this->main_question_type === self::TYPE_FILIAL) {
            $questionFilials = json_decode($this->detail_question) ?? [];
            $filials = Filial::find()->where(['id' => $questionFilials, 'is_active' => 1])->all();
            foreach ($filials as $key => $filial) {
                $detailItem = 0;
                if ($answerItem) {
                    $detailItem = is_array(
                        $answerItem->detail_item
                    ) ? $answerItem->detail_item[0] : $answerItem->detail_item;
                }
                $variants[] = [
                    'id'        => (string)$filial->id,
                    'value'     => $filial->name,
                    'position'  => $key + 1,
                    'isChecked' => $detailItem == $filial->id,
                ];
            }
        } elseif ($this->main_question_type === self::TYPE_DICTIONARY) {
            foreach (json_decode($this->detail_question) as $key => $value) {
                if ($element = DictionaryElement::findOne($value)) {
                    $variants[] = [
                        'id'          => $value,
                        'value'       => $element->title,
                        'description' => $element->description,
                        'position'    => $key + 1,
                        'isChecked'   => $answerItem && $answerItem->detail_item && in_array($value,
                                is_array($answerItem->detail_item) ? $answerItem->detail_item : json_decode($answerItem->detail_item)),
                    ];
                }
            }
        } else {
            if ($answerItem) {
                $details = is_array($answerItem->detail_item) ? $answerItem->detail_item : json_decode($answerItem->detail_item);
            } else {
                $details = [];
            }
            $donor = $this->getMainDonor();
            $detailItems = [];
            if ($donor) {
                if ((int)$donor->main_question_type === self::TYPE_VARIANTS) {
                    $detailItems = FoquzQuestionDetail::findAll(['foquz_question_id' => $donor->id]);
                } elseif ((int)$donor->main_question_type === self::TYPE_DICTIONARY) {
                    $questionElementsID = json_decode($donor->detail_question) ?? [];
                    $questionElementsDeletedID = json_decode($donor->deleted_detail_question) ?? [];
                    /** @var DictionaryElement[] $questionElements */
                    $questionElements = DictionaryElement::find()
                        ->where(['id' => $questionElementsID])
                        ->orWhere(['id' => $questionElementsDeletedID])
                        ->all();
                    $recipientDetails = RecipientQuestionDetail::findAll(['recipient_id' => $this->id]);
                    /** @var RecipientQuestionDetail[] $recipientDetails */
                    $recipientDetails = ArrayHelper::index($recipientDetails, 'dictionary_element_id');

                    foreach ($questionElements as $questionElement) {
                        $detailItems[] = [
                            'id'             => $questionElement->id,
                            'question'       => $questionElement->fullPath,
                            'description'    => $recipientDetails[$questionElement->id]->description ?? null,
                            'position'       => $questionElement->position,
                            'is_deleted'     => !in_array($questionElement->id, $questionElementsID),
                            'extra_question' => 0,
                            'need_extra'     => $recipientDetails[$questionElement->id]->need_extra ?? 1,
                        ];
                    }
                }
            } else {
                $detailItems = $this->questionDetails;
            }


            foreach ($detailItems as $detailItem) {
                $recipientDetail = null;
                if (!empty($this->donor)) {
                    $recipientDetail = RecipientQuestionDetail::findOne([
                        'recipient_id'       => $this->id,
                        'question_detail_id' => $detailItem['id']
                    ]);
                }
                $item = [
                    'id'          => $detailItem['id'],
                    'type'        => $detailItem['type'] ?? FoquzQuestionDetail::TYPE_GENERAL,
                    'value'       => empty($detailItem['is_empty']) ? $detailItem['question'] : '',
                    'description' => $detailItem['description'] ?? null,
                    'position'    => $recipientDetail->position ?? $detailItem['position'],
                    'is_deleted'  => $detailItem['is_deleted'],
                    'file_id'     => null,
                    'file_url'    => null,
                    'preview_url' => null,
                    'need_extra'  => $detailItem['need_extra']
                ];
                if ($this->variants_with_files) {
                    $item['file_id'] =  $detailItem->file->id ?? null;
                    $item['file_url'] = $detailItem->file->fileUrl ?? null;
                    $item['preview_url'] = $detailItem->file->previewUrl ?? null;
                }

                if (!is_array($detailItem)) {
                    $item = array_merge($item, [
                        'dictionary_element_id'         => $detailItem['dictionary_element_id'],
                        'detail_question'               => $detailItem->detail_question,
                        'question_detail_id'            => $detailItem['question_detail_id'],
                        'extra_question_rate_from'      => $detailItem['extra_question_rate_from'],
                        'extra_question_rate_to'        => $detailItem['extra_question_rate_to'],
                        'extra_required'                => $detailItem['extra_required'],
                        'min_choose_extra_variants'     => $detailItem['min_choose_extra_variants'],
                        'max_choose_extra_variants'     => $detailItem['max_choose_extra_variants'],
                        'variants_with_files'           => $detailItem['variants_with_files'],
                        'self_variant_text'             => $detailItem['self_variant_text'],
                        'self_variant_placeholder_text' => $detailItem['self_variant_placeholder_text'],
                        'variants_element_type'         => $detailItem['variants_element_type'],
                        'for_all_rates'                 => $detailItem['for_all_rates'],
                        'placeholder_text'              => $detailItem['placeholder_text'],
                        'self_variant_minlength'        => $detailItem['self_variant_minlength'],
                        'self_variant_maxlength'        => $detailItem['self_variant_maxlength'],
                        'text_variant_minlength'        => $detailItem['text_variant_minlength'],
                        'text_variant_maxlength'        => $detailItem['text_variant_maxlength'],
                        'is_self_answer'                => $detailItem['is_self_answer'],
                        'variants_with_files'           => $detailItem['variants_with_files']
                    ]);
                    if ($detailItem->is_self_answer && $detailItem->variants_with_files) {
                        $item['selfVariantFile'] = $detailItem->getSelfVariantFile();
                    }

                }
                if ($this->main_question_type === self::TYPE_VARIANT_STAR) {
                    if ($answerItem && $answerItem->answer !== null) {
                        $answerDetails = json_decode($answerItem->answer, true);
                    }
                    $item['stars'] = $answerDetails[$detailItem['id']] ?? null;
                    $item['extra_question'] = $detailItem['extra_question'];
                    $item['need_extra'] = $detailItem['need_extra'];
                } else {
                    $item['isChecked'] = $answerItem && $answerItem->detail_item !== null && in_array($detailItem['id'],
                            $details);
                }
                $variants[] = $item;
            }
            if ($donor && $donor->is_self_answer) {
                $donorAnswerItem = null;
                if ($answer) {
                    $donorAnswerItem = FoquzPollAnswerItem::findOne([
                        'foquz_poll_answer_id' => $answer->id,
                        'foquz_question_id'    => $donor->id
                    ]);
                }
                $recipientDetail = RecipientQuestionDetail::findOne([
                    'recipient_id'       => $this->id,
                    'question_detail_id' => null
                ]);

                $selfVariantDescription = null;
                if ($recipientDetail && $recipientDetail->description !== null && $recipientDetail->description !== '') {
                    $selfVariantDescription = $recipientDetail->description;
                } elseif ($donor->self_variant_description !== '') {
                    $selfVariantDescription = $donor->self_variant_description;
                }
                $variants[] = [
                    'id'             => -1,
                    'value'          => $donorAnswerItem && $donorAnswerItem->self_variant ? $donorAnswerItem->self_variant : '',
                    'description'    => $selfVariantDescription,
                    'position'       => $recipientDetail->position ?? null,
                    'need_extra'     => $recipientDetail->need_extra ?? null,
                    'extra_question' => 0,
                    'is_deleted'     => 0,
                    'isChecked'      => $answerItem && $answerItem->detail_item !== null && in_array(-1, $details),
                ];
            }
        }
        if ($random && count($variants) > 0) {
            shuffle($variants);
        } else {
            ArrayHelper::multisort($variants, 'position');
        }


        return $variants;
    }

    public function collectDummyVariants($random = false)
    {
        $variants = [];
        if ($this->rating_type === self::RATING_DISHES) {
            $dishes = [
                'Сливочный с копчёным лососем (запечённый)',
                'Жареный тофу с курицей и овощами в терияки',
                'Калифорния с креветкой',
                'Эби Чиз'
            ];
            $count = rand(1, 5);
            for ($i = 0; $i < $count; $i++) {
                $variants[] = [
                    'dishId'   => rand(1, 100),
                    'value'    => $dishes[array_rand($dishes)],
                    'stars'    => rand(0, 5),
                    'price'    => rand(300, 1500),
                    'category' => 'Горячее',
                    'serving'  => [
                        'count'  => rand(1, 5),
                        'weight' => rand(250, 1200)
                    ]
                ];
            }
        } elseif ($this->main_question_type === self::TYPE_FILIAL) {
            foreach (json_decode($this->detail_question) as $key => $value) {
                if ($filial = Filial::findOne($value)) {
                    $variants[] = [
                        'id'        => $value,
                        'value'     => $filial->name,
                        'position'  => $key + 1,
                        'isChecked' => false,
                    ];
                }
            }
        } elseif ($this->main_question_type === self::TYPE_DICTIONARY) {
            foreach (json_decode($this->detail_question) as $key => $value) {
                if ($element = DictionaryElement::findOne($value)) {
                    $variants[] = [
                        'id'          => $value,
                        'value'       => $element->title,
                        'description' => $element->description,
                        'position'    => $key + 1,
                        'isChecked'   => false,
                    ];
                }
            }
        } else {
            return $this->collectVariantsArray(null, null, $random);

            $detailItems = $this->getQuestionDetails()->select('id, question, is_deleted')->all();
            foreach ($detailItems as $detailItem) {
                $variants[] = [
                    'id'          => $detailItem['id'],
                    'value'       => $detailItem['question'],
                    'isChecked'   => false,
                    'is_deleted'  => $detailItem->is_deleted,
                    'file_id'     => $detailItem->file->id ?? null,
                    'file_url'    => $detailItem->file->fileUrl ?? null,
                    'preview_url' => $detailItem->file->previewUrl ?? null,
                ];
            }
        }
        if ($random) {
            shuffle($variants);
        }
        return $variants;
    }

    public function fillForType(int $type, array $data)
    {
        switch ($type) {
            case self::TYPE_ASSESSMENT:
                if (!$this->id) {
                    $this->save();
                }
                $mediaTypes = [
                    self::TYPE_TEXT,
                    self::TYPE_IMAGE,
                    self::TYPE_VIDEO,
                ];
                $this->text = $data['text'];
                $this->type = $mediaTypes[$data['mediaType']];
                $this->detail_question = $data['clarifyingQuestionEnabled'] === 'true' ? $data['clarifyingQuestion'] : null;
                $this->is_self_answer = ($data['clarifyingQuestionEnabled'] === 'true' && $data['customClarifyingQuestionAnswerAvailable'] === 'true') || ($data['assessmentType'] == 1 && $data['customVariantAvailable'] === 'true');
                $this->variants_element_type = $data['assessmentType'] == 0 ? $data['clarifyingQuestionVariantsType'] : $data['variantsType'];
                $this->show_category = $data['isCategoryDishInformationEnabled'] === 'true' ? 1 : 0;
                $this->show_name = $data['isNameDishInformationEnabled'] === 'true' ? 1 : 0;
                $this->value_all = $data['rateAll'] === 'true' ? 1 : 0;
                $this->min_sum = $data['minSum'];
                $this->comment_enabled = $data['customVariantAvailable'] === 'true' || $data['commentEnabled'] === 'true' ? 1 : 0;
                $this->comment_required = $data['comment_required'] === 'true' ? 1 : 0;
                $variants = $data['assessmentType'] == 0 ? ($data['clarifyingQuestionAnswers'] ?? []) : ($data['variants'] ?? []);
                $ids = [];
                $variantPosition = 1;
                foreach ($variants as $variant) {
                    if (!isset($variant['type']) || (int)$variant['type'] !== FoquzQuestionDetail::TYPE_NOTHING) {
                        if (!isset($variant['id']) || $variant['id'] === '0') {
                            $formFieldModel = new FoquzQuestionDetail([
                                'foquz_question_id' => $this->id,
                                'question'          => $variant['variant']
                            ]);
                        } else {
                            $formFieldModel = FoquzQuestionDetail::findOne($variant['id']);
                            $formFieldModel->question = $variant['variant'];
                        }
                        $formFieldModel->position = $variantPosition;
                        $formFieldModel->save();
                        $variantPosition++;
                        $ids[] = $formFieldModel->id;
                    } else {
                        $nothingModel = FoquzQuestionDetail::findOne(([
                            'foquz_question_id' => $this->id,
                            'type'              => FoquzQuestionDetail::TYPE_NOTHING
                        ]));
                        if (!$nothingModel) {
                            $nothingModel = new FoquzQuestionDetail();
                        }
                        $nothingModel->foquz_question_id = $this->id;
                        $nothingModel->type = FoquzQuestionDetail::TYPE_NOTHING;
                        $nothingModel->question = $variant['variant'] ?: 'Ничего из перечисленного';
                        $nothingModel->points = $variant['points'] ?? null;
                        $nothingModel->is_deleted = false;
                        $nothingModel->save();
                        $ids[] = $nothingModel->id;
                    }
                }
                if (count($ids) > 0) {
                    FoquzQuestionDetail::deleteAll([
                        'AND',
                        ['foquz_question_id' => $this->id],
                        ['NOT', ['id' => $ids]]
                    ]);
                }
                $this->comment_minlength = $data['assessmentType'] == 0 ? $data['customClarifyingQuestionAnswerLengthRange'][0] : $data['customVariantLengthRange'][0];
                $this->comment_maxlength = $data['assessmentType'] == 0 ? $data['customClarifyingQuestionAnswerLengthRange'][1] : $data['customVariantLengthRange'][1];
                break;
            case self::TYPE_FILIAL:
            case self::TYPE_VARIANTS:
                if (!$this->id) {
                    $this->save();
                }
                $this->variants_element_type = $data['variantsType'] ?? null;
                $this->dropdown_variants = $data['dropdown_variants'] ?? null;
                $this->random_variants_order = $data['random_variants_order'] ?? null;
                $this->is_self_answer = isset($data['customVariantAvailable']) ? $data['customVariantAvailable'] === 'true' : null;
                $this->max_choose_variants = $data['max_choose_variants'] ?? null;
                $this->comment_enabled = isset($data['commentEnabled']) && $data['commentEnabled'] == 'true' ? 1 : 0;
                $this->self_variant_minlength = $data['self_variant_minlength'] ?? null;
                $this->self_variant_maxlength = $data['self_variant_maxlength'] ?? null;
                $this->comment_required = isset($data['commentRequired']) && $data['commentRequired'] === 'true' ? 1 : 0;
                $this->self_variant_nothing = !empty($data['self_variant_nothing']);
                $this->self_variant_description = $data['self_variant_description'] ?? null;
                if (isset($data['customVariantLengthRange'])) {
                    $this->comment_minlength = $data['customVariantLengthRange'][0] ?? null;
                    $this->comment_maxlength = $data['customVariantLengthRange'][1] ?? null;
                } else {
                    $this->comment_minlength = $data['comment_minlength'] ?? null;
                    $this->comment_maxlength = $data['comment_maxlength'] ?? null;
                }
                if (isset($data['customVariantLengthRange'])) {
                    $this->comment_minlength = $data['customVariantLengthRange'][0] ?? null;
                    $this->comment_maxlength = $data['customVariantLengthRange'][1] ?? null;
                }
                if (isset($data['gallery']) && count($data['gallery']) > 0) {
                    $position = 1;
                    $mediaIds = [];
                    foreach ($data['gallery'] as $galleryMedia) {
                        $galleryMediaModel = FoquzQuestionFile::findOne($galleryMedia['id']);
                        $galleryMediaModel->description = $galleryMedia['description'];
                        $galleryMediaModel->position = $position;
                        $galleryMediaModel->save();
                        $mediaIds[] = $galleryMediaModel->id;
                        $position++;
                    }
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $mediaIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }

                if (
                    !empty($data['self_variant_comment_required']) && $type === self::TYPE_VARIANTS
                ) {
                    $this->self_variant_comment_required = 1;
                } else {
                    $this->self_variant_comment_required = 0;
                }
                break;
            case self::TYPE_VARIANT_STAR:
                if (!$this->id) {
                    $this->save();
                }
                $this->variants_element_type = $data['variantsType'] ?? 0;
                $this->dropdown_variants = $data['dropdown_variants'] ?? null;
                $this->random_variants_order = $data['random_variants_order'] ?? 0;
                $this->detail_question = isset($data['clarifyingQuestionEnabled']) && $data['clarifyingQuestionEnabled'] === 'true' ? $data['clarifyingQuestion'] : null;
                $this->is_self_answer = (isset($data['clarifyingQuestionEnabled'], $data['customClarifyingQuestionAnswerAvailable']) && $data['clarifyingQuestionEnabled'] === 'true' && $data['customClarifyingQuestionAnswerAvailable'] === 'true');
                $this->comment_enabled = $data['commentEnabled'] ?? 0;
                $this->comment_minlength = $data['commentLengthRange'][0] ?? 0;
                $this->comment_maxlength = $data['commentLengthRange'][1] ?? 255;
                if (isset($data['gallery']) && count($data['gallery']) > 0) {
                    $position = 1;
                    $mediaIds = [];
                    foreach ($data['gallery'] as $galleryMedia) {
                        $galleryMediaModel = FoquzQuestionFile::findOne($galleryMedia['id']);
                        $galleryMediaModel->description = $galleryMedia['description'];
                        $galleryMediaModel->position = $position;
                        $galleryMediaModel->save();
                        $mediaIds[] = $galleryMediaModel->id;
                        $position++;
                    }
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $mediaIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }
                $starOptions = $this->starRatingOptions ?? new FoquzQuestionStarRatingOptions(['foquz_question_id' => $this->id]);
                $starOptions->color = $data['starOptions']['color'];
                $starOptions->count = $data['starOptions']['count'];
                $starOptions->size = $data['starOptions']['size'];
                $starOptions->labels = isset($data['starOptions']['labels']) ? json_encode($data['starOptions']['labels'],
                    JSON_UNESCAPED_UNICODE) : null;
                $starOptions->extra_question_rate_from = $data['starOptions']['extra_question_rate_from'] ?? null;
                $starOptions->extra_question_rate_to = $data['starOptions']['extra_question_rate_to'] ?? null;
                $starOptions->save();
                break;
            case self::TYPE_TEXT_ANSWER:
                $this->variants_element_type = $data['fieldType'] ?? null;
                $this->mask = $data['maskType'];
                if ($data['maskType'] == 5) {
                    $decodedArray = [];
                    foreach ($data['maskConfig'] as $k => $v) {
                        $decodedArray[$k] = [
                            'visible'         => (int)($v['visible'] === 'true' || $v['visible'] == 1),
                            'required'        => (int)($v['required'] === 'true' || $v['required'] == 1),
                            'placeholderText' => $v['placeholderText'],
                            'minLength'       => (int)$v['minLength'],
                            'maxLength'       => (int)$v['maxLength'],
                        ];
                    }
                    $this->mask_config = json_encode($decodedArray);
                } else {
                    $this->mask_config = null;
                }
                if (isset($data['customVariantLengthRange'][0], $data['customVariantLengthRange'][1])) {
                    $this->comment_minlength = $data['customVariantLengthRange'][0];
                    $this->comment_maxlength = $data['customVariantLengthRange'][1];
                } elseif (isset($data['customClarifyingQuestionAnswerLengthRange'][0], $data['customClarifyingQuestionAnswerLengthRange'][1])) {
                    $this->comment_minlength = $data['customClarifyingQuestionAnswerLengthRange'][0];
                    $this->comment_maxlength = $data['customClarifyingQuestionAnswerLengthRange'][1];
                }
                break;
            case self::TYPE_DATE:
                $this->date_type = $data['dateType'];
                $this->only_date_month = $data['only_date_month'];
                break;
            case self::TYPE_ADDRESS:
                if (!$this->id) {
                    $this->save();
                }
                $addressCodes = new FoquzQuestionAddressCodes();
                $addressCodes->question_id = $this->id;
                $addressCodes->regions = json_encode($data['regionsValues'] ?? []);
                $addressCodes->districts = json_encode($data['districtValues'] ?? []);
                $addressCodes->cities = json_encode($data['cityValues'] ?? []);
                $addressCodes->streets = json_encode($data['streetValues'] ?? []);
                $addressCodes->save();
                break;
            case self::TYPE_FILE_UPLOAD:
                $this->is_self_answer = $data['fileCommentEnabled'] === 'true';
                $this->comment_minlength = $data['commentLengthRange'][0];
                $this->comment_maxlength = $data['commentLengthRange'][1];
                $this->file_types = $data['fileTypes'] ?? [];
                $this->files_length = $data['filesLength'];
                break;
            case self::TYPE_FORM:
                if (!$this->id) {
                    $this->save();
                }
                $ids = [];
                $formFieldPosition = 1;
                foreach ($data['quizzes'] as $formField) {
                    if ($formField['id'] === '0') {
                        $formFieldModel = new FoquzQuestionFormField();
                    } else {
                        $formFieldModel = FoquzQuestionFormField::findOne($formField['id']);
                    }
                    $formFieldModel->question_id = $this->id;
                    $formFieldModel->name = $formField['name'];
                    $formFieldModel->is_required = $formField['required'] === 'true';
                    $formFieldModel->mask_type = $formField['maskType'];
                    if ($formField['maskType'] == 5) {
                        $formFieldModel->name = 'ФИО';
                        $decodedArray = [];
                        foreach ($formField['maskConfig'] as $k => $v) {
                            $decodedArray[$k] = [
                                'visible'         => (int)($v['visible'] === 'true' || $v['visible'] == 1),
                                'required'        => (int)($v['required'] === 'true' || $v['required'] == 1),
                                'placeholderText' => $v['placeholderText'],
                                'minLength'       => (int)$v['minLength'],
                                'maxLength'       => (int)$v['maxLength'],
                            ];
                        }
                        $formFieldModel->mask_config = json_encode($decodedArray);
                    } else {
                        $formFieldModel->mask_config = null;
                    }
                    $formFieldModel->variants_type = $formField['variantsType'];
                    $formFieldModel->comment_minlength = $formField['variantsType'] === '0' ? $formField['customVariantLengthRangeShort'][0] : $formField['customVariantLengthRange'][0];
                    $formFieldModel->comment_maxlength = $formField['variantsType'] === '0' ? $formField['customVariantLengthRangeShort'][1] : $formField['customVariantLengthRange'][1];
                    $formFieldModel->link_with_client_field = $formField['linkWithClientField'];
                    $formFieldModel->linked_client_field = $formField['linkedClientField'];
                    $formFieldModel->rewrite_linked_field = $formField['rewriteLinkedField'];
                    $formFieldModel->placeholder_text = $formField['placeholderText'];
                    $formFieldModel->position = $formFieldPosition;
                    $formFieldModel->save();
                    $formFieldPosition++;
                    $ids[] = $formFieldModel->id;
                }
                FoquzQuestionFormField::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $ids]]]);
                break;
            case self::TYPE_PRIORITY:
                $this->comment_enabled = $data['commentEnabled'] ?? 0;
                $this->comment_minlength = $data['comment_minlength'] ?? null;
                $this->comment_maxlength = $data['comment_maxlength'] ?? null;
                if (isset($data['commentLengthRange'][0], $data['commentLengthRange'][1])) {
                    $this->comment_minlength = $data['commentLengthRange'][0];
                    $this->comment_maxlength = $data['commentLengthRange'][1];
                }
                if (!$this->id) {
                    $this->save();
                }
                $ids = [];
                $variantPosition = 1;
                foreach ($data['variants'] as $variant) {
                    if ($variant['id'] === '0') {
                        $formFieldModel = new FoquzQuestionDetail([
                            'foquz_question_id' => $this->id,
                            'question'          => $variant['variant']
                        ]);
                    } else {
                        $formFieldModel = FoquzQuestionDetail::findOne($variant['id']);
                        $formFieldModel->question = $variant['variant'];
                    }
                    $formFieldModel->position = $variantPosition;
                    $formFieldModel->save();
                    $variantPosition++;
                    $ids[] = $formFieldModel->id;
                }
                FoquzQuestionDetail::deleteAll(['AND', ['foquz_question_id' => $this->id], ['NOT', ['id' => $ids]]]);
                $settings = $this->foquzQuestionPrioritySettings;
                if (!$settings) {
                    $settings = new FoquzQuestionPrioritySettings();
                }
                $settings->reorder_required = (isset($data['reorder_required']) && $data['reorder_required']) ? 1 : 0;
                $settings->foquz_question_id = $this->id;
                $settings->save();
                break;
            case self::TYPE_CHOOSE_MEDIA:
                if (!$this->id) {
                    $this->save();
                }
                $this->choose_type = $data['chooseType'];
                $this->is_self_answer = $data['commentEnabled'] === 'true';
                $this->comment_minlength = $data['commentLengthRange'][0];
                $this->comment_maxlength = $data['commentLengthRange'][1];
                $this->variants_element_type = $data['variantsType'];
                $this->max_choose_variants = $data['max_choose_variants'] ?? null;
                $this->random_variants_order = $data['random_variants_order'];
                if (isset($data['variants']) && count($data['variants']) > 0) {
                    $position = 1;
                    $variantIds = [];
                    foreach ($data['variants'] as $variant2) {
                        $variant = FoquzQuestionFile::findOne($variant2['id']);
                        $variant->description = $variant2['description'];
                        $variant->position = $position;
                        $variant->save();
                        $variantIds[] = $variant->id;
                        $position++;
                    }
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $variantIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }
                break;
            case self::TYPE_GALLERY_RATING:
                if (!$this->id) {
                    $this->save();
                }
                $this->is_self_answer = $data['commentEnabled'] === 'true' || $data['commentEnabled'] === 1;
                $this->comment_minlength = $data['commentLengthRange'][0];
                $this->comment_maxlength = $data['commentLengthRange'][1];
                if (isset($data['gallery']) && count($data['gallery']) > 0) {
                    $position = 1;
                    $mediaIds = [];
                    foreach ($data['gallery'] as $galleryMedia) {
                        $galleryMediaModel = FoquzQuestionFile::findOne($galleryMedia['id']);
                        $galleryMediaModel->description = $galleryMedia['description'];
                        $galleryMediaModel->position = $position;
                        $galleryMediaModel->save();
                        $mediaIds[] = $galleryMediaModel->id;
                        $position++;
                    }
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $mediaIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }
                break;
            case self::TYPE_SMILE_RATING:
                if (!$this->id) {
                    $this->save();
                }
                $this->is_self_answer = $data['commentEnabled'] == 'true' || $data['commentEnabled'] == 1;
                $this->comment_minlength = $data['commentLengthRange'][0];
                $this->comment_maxlength = $data['commentLengthRange'][1];
                $this->smile_type = $data['smileType'];
                $this->smiles_count = $data['smilesCount'] ?? null;
                if (isset($data['smiles']) && count($data['smiles']) > 0) {
                    $smileIds = [];
                    foreach ($data['smiles'] as $key => $smileData) {
                        $smileModel = isset($smileData['id']) ? FoquzQuestionSmile::findOne($smileData['id']) : new FoquzQuestionSmile();
                        $smileModel->foquz_question_id = $this->id;
                        $smileModel->label = $smileData['label'] ?? '';
                        $smileModel->file = UploadedFile::getInstanceByName('smile' . $key);
                        if ($data['smileType'] === FoquzQuestion::SMILE_CUSTOM && $smileModel->file) {
                            $dir = "uploads/smiles/custom$this->id";
                            if (!file_exists($dir) && !mkdir($dir, 0775, true) && !is_dir($dir)) {
                                throw new \RuntimeException(sprintf('Directory "%s" was not created', $dir));
                            }
                            ++$key;
                            $filePath = $dir . "/$key.{$smileModel->file->extension}";
                            $smileModel->smile_url = '/' . $filePath;
                        } else {
                            $smileModel->smile_url = $smileData['url'];
                        }
                        if ($smileModel->save()) {
                            if ($smileModel->file) {
                                $smileModel->file->saveAs($filePath);
                            }
                            $smileIds[] = $smileModel->id;
                        }
                    }
                    FoquzQuestionSmile::deleteAll([
                        'AND',
                        ['foquz_question_id' => $this->id],
                        ['NOT', ['id' => $smileIds]]
                    ]);
                } else {
                    FoquzQuestionSmile::deleteAll(['foquz_question_id' => $this->id]);
                }
                if (isset($data['enableGallery']) && $data['enableGallery'] == 1) {
                    $position = 1;
                    $mediaIds = [];
                    foreach ($data['gallery'] as $galleryFile) {
                        $mediaModel = FoquzQuestionFile::findOne($galleryFile['id']);
                        $mediaModel->description = $galleryFile['description'];
                        $mediaModel->position = $position;
                        if (!$mediaModel->save()) {
                            print_r($mediaModel->errors);
                            die();
                        }
                        $mediaIds[] = $mediaModel->id;
                        $position++;
                    }
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $mediaIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }
                break;
            case self::TYPE_NPS_RATING:
                if (!$this->id) {
                    $this->save();
                }
                $this->is_self_answer = $data['commentEnabled'] == 'true' || $data['commentEnabled'] == 1;
                $this->comment_minlength = $data['commentLengthRange'][0];
                $this->comment_maxlength = $data['commentLengthRange'][1];
                $npsRatingSetting = $this->npsRatingSetting ?? new FoquzQuestionNpsRatingSetting(['foquz_question_id' => $this->id]);
                $npsRatingSetting->design = $data['design'];
                $npsRatingSetting->start_point_color = $data['start_point_color'] ?? null;
                $npsRatingSetting->end_point_color = $data['end_point_color'] ?? null;
                $npsRatingSetting->start_label = $data['start_label'] ?? null;
                $npsRatingSetting->end_label = $data['end_label'] ?? null;
                $npsRatingSetting->save();
                if (isset($data['gallery']) && count($data['gallery']) > 0) {
                    $position = 1;
                    $mediaIds = [];
                    foreach ($data['gallery'] as $galleryMedia) {
                        $galleryMediaModel = FoquzQuestionFile::findOne($galleryMedia['id']);
                        $galleryMediaModel->description = $galleryMedia['description'];
                        $galleryMediaModel->position = $position;
                        $galleryMediaModel->save();
                        $mediaIds[] = $galleryMediaModel->id;
                        $position++;
                    }
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $mediaIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }
                break;
            case self::TYPE_SIMPLE_MATRIX:
                if (!$this->id) {
                    $this->save();
                }
                $this->matrix_settings = json_encode($data['matrixSettings']);
                $this->is_self_answer = $data['commentEnabled'] == 'true' || $data['commentEnabled'] == 1;
                $this->comment_minlength = $data['commentLengthRange'][0];
                $this->comment_maxlength = $data['commentLengthRange'][1];
                $this->random_variants_order = $data['random_variants_order'];
                if (isset($data['gallery']) && count($data['gallery']) > 0) {
                    $position = 1;
                    $mediaIds = [];
                    foreach ($data['gallery'] as $galleryMedia) {
                        $galleryMediaModel = FoquzQuestionFile::findOne($galleryMedia['id']);
                        $galleryMediaModel->description = $galleryMedia['description'];
                        $galleryMediaModel->position = $position;
                        $galleryMediaModel->save();
                        $mediaIds[] = $galleryMediaModel->id;
                        $position++;
                    }
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $mediaIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }
                break;
            case self::TYPE_SEM_DIFFERENTIAL:
                if (!$this->id) {
                    $this->save();
                }
                $position = 1;
                $rowIds = [];
                $semDifSetting = $this->semDifSetting ?? new FoquzQuestionSemDifSetting(['foquz_question_id' => $this->id]);
                $semDifSetting->form = $data['form'] ?? null;
                $semDifSetting->start_point_color = $data['start_point_color'] ?? null;
                $semDifSetting->end_point_color = $data['end_point_color'] ?? null;
                $semDifSetting->save();
                foreach ($data['differentialRows'] as $differentialRow) {
                    if ($differentialRow['id'] == 0) {
                        $row = new FoquzQuestionDifferentialRow([
                            'question_id' => $this->id,
                        ]);
                    } else {
                        $row = FoquzQuestionDifferentialRow::findOne($differentialRow['id']);
                    }
                    $row->start_label = $differentialRow['start_label'];
                    $row->end_label = $differentialRow['end_label'];
                    $row->position = $position;
                    $row->save();
                    $rowIds[] = $row->id;
                    $position++;
                }
                if (count($rowIds) > 0) {
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $rowIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }
                $this->is_self_answer = $data['commentEnabled'] == 'true' || $data['commentEnabled'] == 1;
                $this->comment_minlength = $data['commentLengthRange'][0];
                $this->comment_maxlength = $data['commentLengthRange'][1];
                if (isset($data['gallery']) && count($data['gallery']) > 0) {
                    $position = 1;
                    $mediaIds = [];
                    foreach ($data['gallery'] as $galleryMedia) {
                        $galleryMediaModel = FoquzQuestionFile::findOne($galleryMedia['id']);
                        $galleryMediaModel->description = $galleryMedia['description'];
                        $galleryMediaModel->position = $position;
                        $galleryMediaModel->save();
                        $mediaIds[] = $galleryMediaModel->id;
                        $position++;
                    }
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $mediaIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }
                break;
            case self::TYPE_RATING:
            case self::TYPE_STAR_RATING:
                if (!$this->id) {
                    $this->save();
                }
                $this->detail_question = $data['clarifyingQuestionEnabled'] === 'true' ? $data['clarifyingQuestion'] : null;
                $this->is_self_answer = ($data['clarifyingQuestionEnabled'] === 'true' && $data['customClarifyingQuestionAnswerAvailable'] === 'true');
                $this->variants_element_type = $data['variantsType'];
                $this->comment_enabled = $data['commentEnabled'] === 'true' ? 1 : 0;
                $variants = $data['variants'] ?? [];
                $ids = [];
                $variantPosition = 1;
                foreach ($variants as $variant) {
                    if (!isset($variant['id']) || $variant['id'] === '0') {
                        $formFieldModel = new FoquzQuestionDetail([
                            'foquz_question_id' => $this->id,
                            'question'          => $variant['variant']
                        ]);
                    } else {
                        $formFieldModel = FoquzQuestionDetail::findOne($variant['id']);
                        $formFieldModel->question = $variant['variant'];
                    }
                    $formFieldModel->position = $variantPosition;
                    if (!$formFieldModel->save()) {
                        print_r($formFieldModel->errors);
                        die();
                    }
                    $variantPosition++;
                    $ids[] = $formFieldModel->id;
                }
                if (count($ids) > 0) {
                    FoquzQuestionDetail::deleteAll(
                        ['AND', ['foquz_question_id' => $this->id], ['NOT', ['id' => $ids]]]
                    );
                }
                $this->comment_minlength = $data['commentLengthRange'][0] ?? 0;
                $this->comment_maxlength = $data['commentLengthRange'][1] ?? 255;
                if (isset($data['gallery']) && count($data['gallery']) > 0) {
                    $position = 1;
                    $mediaIds = [];
                    foreach ($data['gallery'] as $galleryMedia) {
                        $galleryMediaModel = FoquzQuestionFile::findOne($galleryMedia['id']);
                        $galleryMediaModel->description = $galleryMedia['description'];
                        $galleryMediaModel->position = $position;
                        $galleryMediaModel->save();
                        $mediaIds[] = $galleryMediaModel->id;
                        $position++;
                    }
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $mediaIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }
                $starOptions = $this->starRatingOptions ?? new FoquzQuestionStarRatingOptions(['foquz_question_id' => $this->id]);
                $starOptions->color = $data['starOptions']['color'] ?? '#f8cd1c';
                $starOptions->count = $data['starOptions']['count'] ?? 5;
                $starOptions->size = $data['starOptions']['size'] ?? 'md';
                $starOptions->labels = json_encode($data['starOptions']['labels'], JSON_UNESCAPED_UNICODE);
                $starOptions->extra_question_rate_from = $data['starOptions']['extra_question_rate_from'] ?? null;
                $starOptions->extra_question_rate_to = $data['starOptions']['extra_question_rate_to'] ?? null;
                $starOptions->save();
                break;
            case self::TYPE_INTERMEDIATE_BLOCK:
                if (!$this->id) {
                    $this->save();
                }
                $intermediateBlock = $this->intermediateBlock ?? new FoquzQuestionIntermediateBlockSetting([
                    'question_id' => $this->id
                ]);
                $intermediateBlock->load($data['intermediateBlock'], '');
                if (!$intermediateBlock->save()) {
                    print_r($intermediateBlock->errors);
                    die();
                }
                if (isset($data['endScreenLogos']) && count($data['endScreenLogos']) > 0) {
                    $position = 1;
                    $mediaIds = [];
                    foreach ($data['endScreenLogos'] as $galleryFile) {
                        $mediaModel = FoquzQuestionEndScreenLogo::findOne($galleryFile['id']);
                        $mediaModel->description = $galleryFile['description'] ?? '';
                        $mediaModel->position = $position;
                        $mediaModel->width = $galleryFile['width'] ?? null;
                        $mediaModel->height = $galleryFile['height'] ?? null;
                        $mediaModel->link = $galleryFile['link'] ?? '';
                        if (!$mediaModel->save()) {
                            print_r($mediaModel->errors);
                            die();
                        }
                        $mediaIds[] = $mediaModel->id;
                        $position++;
                    }
                    FoquzQuestionEndScreenLogo::deleteAll([
                        'AND',
                        ['foquz_question_id' => $this->id],
                        ['NOT', ['id' => $mediaIds]]
                    ]);
                } else {
                    FoquzQuestionEndScreenLogo::deleteAll(['foquz_question_id' => $this->id]);
                }
                break;
            case self::TYPE_3D_MATRIX:
                if (!$this->id) {
                    $this->save();
                }
                $this->matrix_settings = json_encode($data['matrixSettings'] ?? '');
                $transaction = \Yii::$app->db->beginTransaction();
                try {
                    $rows = !empty($data['FoquzQuestionMatrixElement']['rows']) ? $data['FoquzQuestionMatrixElement']['rows'] : [];
                    $columns = !empty($data['FoquzQuestionMatrixElement']['columns']) ? $data['FoquzQuestionMatrixElement']['columns'] : [];
                    $rows = array_map(static function ($row) {
                        $row['type_id'] = FoquzQuestionMatrixElement::TYPE_ROW;
                        return $row;
                    }, $rows);
                    $columns = array_map(static function ($column) {
                        $column['type_id'] = FoquzQuestionMatrixElement::TYPE_COLUMN;
                        return $column;
                    }, $columns);
                    $elements = array_merge($rows, $columns);
                    $elementsIDs = array_values(array_filter(ArrayHelper::getColumn($elements, 'id')));
                    $elementsIDsForDelete = FoquzQuestionMatrixElement::find()
                        ->select('id')
                        ->where(['AND', ['foquz_question_id' => $this->id], ['NOT', ['id' => $elementsIDs]]])
                        ->column();
                    FoquzQuestionMatrixElement::updateAll(
                        ['is_deleted' => 1],
                        ['id' => $elementsIDsForDelete]
                    );
                    FoquzQuestionMatrixElementVariant::updateAll(
                        ['is_deleted' => 1],
                        ['matrix_element_id' => $elementsIDsForDelete]
                    );
                    $elementModels = FoquzQuestionMatrixElement::find()
                        ->where(['id' => $elementsIDs, 'foquz_question_id' => $this->id, 'is_deleted' => 0])
                        ->all();
                    $elementModels = ArrayHelper::index($elementModels, 'id');
                    foreach ($elements as $element) {
                        if (empty($element['id'])) {
                            $elementModel = new FoquzQuestionMatrixElement();
                        } else {
                            $elementModel = !empty($elementModels[$element['id']]) ? $elementModels[$element['id']] : null;
                            if (!$elementModel) {
                                $elementModel = new FoquzQuestionMatrixElement();
                            }
                        }
                        $elementModel->load($element, '');
                        $elementModel->foquz_question_id = $this->id;
                        if (!$elementModel->validate()) {
                            throw new BadRequestHttpException('Ошибка валидации элемента матрицы c ID ' .
                                $elementModel->id . ' - ' . implode(', ', $elementModel->getFirstErrors()));
                        }
                        if (!$elementModel->save()) {
                            throw new ServerErrorHttpException('Ошибка сохранения элемента матрицы c ID ' . $elementModel->id);
                        }
                        if ($elementModel->type_id === FoquzQuestionMatrixElement::TYPE_COLUMN) {
                            $variants = !empty($element['variants']) ? $element['variants'] : [];
                            if (empty($element['id'])) {
                                $variants = array_map(static function ($variant) {
                                    unset($variant['id']);
                                    return $variant;
                                }, $variants);
                            }
                            $variantsIDs = array_values(array_filter(ArrayHelper::getColumn($variants, 'id')));
                            FoquzQuestionMatrixElementVariant::updateAll(
                                ['is_deleted' => 1],
                                ['AND', ['matrix_element_id' => $elementModel->id], ['NOT', ['id' => $variantsIDs]]]
                            );
                            $variantModels = FoquzQuestionMatrixElementVariant::find()
                                ->where([
                                    'id'                => $variantsIDs,
                                    'matrix_element_id' => $elementModel->id,
                                    'is_deleted'        => 0
                                ])
                                ->all();
                            $variantModels = ArrayHelper::index($variantModels, 'id');
                            foreach ($variants as $variant) {
                                if (empty($variant['id'])) {
                                    $variantModel = new FoquzQuestionMatrixElementVariant();
                                } else {
                                    $variantModel = !empty($variantModels[$variant['id']]) ? $variantModels[$variant['id']] : null;
                                    if (!$variantModel) {
                                        $variantModel = new FoquzQuestionMatrixElementVariant();
                                    }
                                }
                                $variantModel->load($variant, '');
                                $variantModel->matrix_element_id = $elementModel->id;
                                if (!$variantModel->validate()) {
                                    throw new BadRequestHttpException('Ошибка валидации варианта элемента матрицы c ID ' .
                                        $variantModel->id . ' - ' . implode(', ', $variantModel->getFirstErrors()));
                                }
                                if (!$variantModel->save()) {
                                    throw new ServerErrorHttpException('Ошибка сохранения варианта элемента матрицы c ID ' .
                                        $variantModel->id);
                                }
                            }
                        }
                    }
                    if (isset($data['gallery']) && count($data['gallery']) > 0) {
                        $position = 1;
                        $mediaIds = [];
                        foreach ($data['gallery'] as $galleryMedia) {
                            $galleryMediaModel = FoquzQuestionFile::findOne($galleryMedia['id']);
                            if (!$galleryMediaModel) {
                                continue;
                            }
                            $galleryMediaModel->description = $galleryMedia['description'];
                            $galleryMediaModel->position = $position;
                            $galleryMediaModel->save();
                            $mediaIds[] = $galleryMediaModel->id;
                            $position++;
                        }
                        FoquzQuestionFile::deleteAll([
                            'AND',
                            ['question_id' => $this->id],
                            ['NOT', ['id' => $mediaIds]]
                        ]);
                    } else {
                        FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                    }
                    $transaction->commit();
                } catch (BadRequestHttpException|NotFoundHttpException $e) {
                    $transaction->rollBack();
                    throw $e;
                } catch (\Throwable $e) {
                    $transaction->rollBack();
                    throw new ServerErrorHttpException($e->getMessage());
                }
                break;
            case self::TYPE_SCALE:
            case self::TYPE_DICTIONARY:
                if (isset($data['gallery']) && count($data['gallery']) > 0) {
                    $position = 1;
                    $mediaIds = [];
                    foreach ($data['gallery'] as $galleryMedia) {
                        $galleryMediaModel = FoquzQuestionFile::findOne($galleryMedia['id']);
                        $galleryMediaModel->description = $galleryMedia['description'];
                        $galleryMediaModel->position = $position;
                        $galleryMediaModel->save();
                        $mediaIds[] = $galleryMediaModel->id;
                        $position++;
                    }
                    FoquzQuestionFile::deleteAll(['AND', ['question_id' => $this->id], ['NOT', ['id' => $mediaIds]]]);
                } else {
                    FoquzQuestionFile::deleteAll(['question_id' => $this->id]);
                }
                break;
        }
    }

    public function collectQuizFields()
    {
        $quizzes = [];
        foreach ($this->formFields as $formField) {
            $quizzes[] = [
                'id'                            => $formField->id,
                'name'                          => $formField->name,
                'required'                      => $formField->is_required,
                'maskType'                      => $formField->mask_type,
                'variantsType'                  => (string)$formField->variants_type,
                'customVariantLengthRange'      => $formField->variants_type === 1 ? [
                    $formField->comment_minlength,
                    $formField->comment_maxlength
                ] : [0, 250],
                'customVariantLengthRangeShort' => $formField->variants_type === 0 ? [
                    $formField->comment_minlength,
                    $formField->comment_maxlength
                ] : [0, 50],
                'isNew'                         => true,
                'linkWithClientField'           => $formField->link_with_client_field,
                'linkedClientField'             => $formField->linked_client_field,
                'rewriteLinkedField'            => $formField->rewrite_linked_field,
                'placeholderText'               => $formField->placeholder_text,
                'maskConfig'                    => json_decode($formField->mask_config),
            ];
        }
        return $quizzes;
    }

    public function grabMaskAnswers($answer = null)
    {
        $mask = [];
        if ($this->mask == 5 && $this->mask_config) {
            foreach (json_decode($this->mask_config, true) as $name => $maskField) {
                $mask[$name] = $maskField + [
                        'value' => $answer ? (json_decode($answer->answer, true)[$name] ?? '') : ''
                    ];
            }
        }
        return (object)$mask;
    }

    public function generateFakeDateTime()
    {
        $date = '';
        $time = '';
        switch ($this->date_type) {
            case 0:
                if ($this->only_date_month) {
                    $date = date('d.m', strtotime('+' . rand(1, 7) . ' days'));
                } else {
                    $date = date('d.m.Y', strtotime('+' . rand(1, 7) . ' days'));
                }
                break;
            case 1:
                $firstPart = rand(0, 24);
                if ($firstPart < 10) {
                    $firstPart = '0' . $firstPart;
                }
                $secondPart = rand(0, 59);
                if ($secondPart < 10) {
                    $secondPart = '0' . $secondPart;
                }
                $time = $firstPart . ' : ' . $secondPart;
                break;
            case 2:
                if ($this->only_date_month) {
                    $date = date('d.m', strtotime('+' . rand(1, 7) . ' days'));
                } else {
                    $date = date('d.m.Y', strtotime('+' . rand(1, 7) . ' days'));
                }
                $firstPart = rand(0, 24);
                if ($firstPart < 10) {
                    $firstPart = '0' . $firstPart;
                }
                $secondPart = rand(0, 59);
                if ($secondPart < 10) {
                    $secondPart = '0' . $secondPart;
                }
                $time = $firstPart . ' : ' . $secondPart;
                break;
        }
        return [$date, $time];
    }

    /**
     * @return FoquzQuestionDetail[] | ActiveQuery | RecipientQuestionDetail[]
     */
    public function collectQuestionDetails()
    {
        $details = $this->getQuestionDetails()->orderBy('RAND()')->all();
        if (!$details) {
            $details = $this->getRecipientQuestionDetails()->orderBy('RAND()')->all();
        }

        return $details;
    }

    public function generateVariants()
    {
        $selectedVariants = [];
        if ($this->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO) {
            if ($this->donor) {
                $detail = $this->getRecipientsQuestionDetailsActive()->orderBy('RAND()')->one();
                $selectedVariants[] = $detail->question_detail_id ?: -1;
            } else {
                $selectedVariants[] = $this->getQuestionDetailsActive()->orderBy('RAND()')->one()->id;
            }
        } else {
            $details = $this->collectQuestionDetails();

            $needAnswers = rand(1, count($details));
            $details = $this->getQuestionDetailsActive()->orderBy('RAND()')->all();
            if (!$details) {
                $details = $this->getRecipientsQuestionDetailsActive()->orderBy('RAND()')->all();
            }
            $count = count($details);
            $needAnswers = rand(1, $count);
            for ($i = 0; $i < $needAnswers; $i++) {
                if ($this->donor) {
                    $selectedVariants[] = $details[$i]->question_detail_id ?: -1;
                } else {
                    $selectedVariants[] = $details[$i]->id;
                }
            }
        }
        return $selectedVariants;
    }

    /** Новый генератор */
    public function generateNewVariants()
    {
        $selectedVariants = [];

        if (!$this->donor) {
            $details = $this->collectQuestionDetails();
            /** @var FoquzQuestionDetail $detail */
            foreach ($details as $detail) {
                if ($this->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR) {
                    if (!$detail->extra_question) {
                        $selectedVariants[$detail->id] = $this->genStarRating();
                    }
                }
                if ($this->main_question_type === FoquzQuestion::TYPE_NPS_RATING) {

                    $selectedVariants[$detail->id] = $this->genNpsRating();
                }
            }
        }


        return $selectedVariants;
    }

    public function generateFilialVariants()
    {
        $questionDetails = json_decode($this->detail_question) ?? [];

        return [(int)$questionDetails[array_rand($questionDetails)]];
    }

    public function generateFakeTextAnswer($mask)
    {
        $answer = '';
        switch ($mask) {
            case 1:
                $answer = '+7 (' . rand(100, 999) . ') ' . rand(100, 999) . ' ' . rand(1000, 9999);
                break;
            case 2:
                $answer = rand(1, 100) . '@mail.ru';
                break;
            case 3:
                $answer = rand(1, 100);
                break;
            case 4:
                $answer = rand(1, 100) . '.ru';
                break;
            case 5:
                $answer = [
                    'name'       => 'Василий',
                    'surname'    => 'Петров',
                    'patronymic' => 'Геннадьевич',
                ];
                break;
            case 6:
                $answer = date('d.m.Y', strtotime('-10 days'));
                break;
            case 7:
                $answer = date('d.m.Y', strtotime('-14 days')) . ' - ' . date('d.m.Y', strtotime('-10 days'));
                break;
            case 8:
                $answer = date('d.m', strtotime('+' . rand(1, 7) . ' days'));
                break;
            default:
                $answer = $this->randomTextAnswers(true);
                break;
        }
        return $answer;
    }

    public function randomTextAnswers($random = false)
    {
        $array = [
            'Не понравилось обслуживание',
            'Всё супер',
            'Музыка слишком громкая',
            'Буду рекомендовать друзьям',
            'Нагрубили по телефону',
            'Удобное расположение, отличный сервис',
            'Не смог долго дозвониться',
            'Окей',
            'Может новое блюдо добавите?',
            'Нормально',
            'Куда делся сет "Цезарь"?',
            'Вкусно, постоянно делаем доставку у вас',
            'А вакансии свободные есть?',
            'Большое ожидание',
            'Уютно',
            'Не понравилась подача',
            ':))) Сделайте скидку для постоянных клиентов!',
            'Привет! Можете добавить в меню больше десертов?',
            'Обычно',
            'Всем привет! Небольшой комментарий.'
        ];
        return $random ? $array[array_rand($array)] : $array;
    }

    public function randomAddressAnswers($random = false)
    {
        $array = [
            'Москва, Большой Николопесковский переулок, 3',
            'Москва, Басманный переулок, 6с1',
            'Москва, улица Бахрушина, 18с1',
            'Москва, Валовая улица, 18',
            'Москва, Волгоградский проспект, 15с2',
            'Москва, Тверской бульвар, 22',
            'Москва, Большая Никитская улица, 43',
            'Москва, Краснопресненская набережная, 12с2',
            'Москва, Кутузовский проспект, 9к1',
            'Москва, Малый Николопесковский переулок, 6'
        ];
        return $random ? $array[array_rand($array)] : $array;
    }

    public function randomImages($random = false)
    {
        $array = [
            '1.jpg',
            '2.jpg',
            '3.jpg',
            '4.jpg',
            '5.jpg',
        ];
        return $random ? $array[array_rand($array)] : $array;
    }

    public function randomVideos($random = false)
    {
        $array = [
            'video-mp4-test1.mp4',
            'video-mp4-test2.mp4'
        ];
        return $random ? $array[array_rand($array)] : $array;
    }

    public function getEmployeeName($answer_id)
    {
        $answer = FoquzPollAnswer::findOne($answer_id);
        if ($this->is_system) {
            if ($this->service_name === 'Оператор') {
                return $answer->order && $answer->order->operator ? $answer->order->operator->name : '';
            } elseif ($this->service_name === 'Курьер') {
                return $answer->order && $answer->order->driver ? $answer->order->driver->name : '';
            }
            return null;
        }
        return null;
    }

    public function getNpsRatingSetting()
    {
        return $this->hasOne(FoquzQuestionNpsRatingSetting::className(), ['foquz_question_id' => 'id']);
    }

    /**
     * @return ActiveQuery | FoquzQuestionScaleRatingSetting
     */
    public function getScaleRatingSetting()
    {
        return $this->hasOne(FoquzQuestionScaleRatingSetting::className(), ['foquz_question_id' => 'id']);
    }

    /**
     * @param array $data
     */
    public function createScaleRatingSetting(array $data)
    {
        if ($data) {
            $FoquzQuestionScaleRatingSetting = $this->scaleRatingSetting ? $this->scaleRatingSetting : new FoquzQuestionScaleRatingSetting();
            $FoquzQuestionScaleRatingSetting->load($data, '');
            $FoquzQuestionScaleRatingSetting->foquz_question_id = $this->id;
            $FoquzQuestionScaleRatingSetting->save();
        }
    }

    public function getSemDifSetting()
    {
        return $this->hasOne(FoquzQuestionSemDifSetting::className(), ['foquz_question_id' => 'id']);
    }

    public function getMainDonor(): ?FoquzQuestion
    {
        if (!$this->mainDonor && $this->donor && $this->id !== $this->donor) {
            $donor = self::findOne($this->donor);
            while ($donor && $donor->donor && $donor->id !== $donor->donor) {
                $donor = self::findOne($donor->donor);
            }
            $this->mainDonor = $donor;
        }

        return $this->mainDonor;
    }

    public function getMainDonorRows(): ?FoquzQuestion
    {
        if (!$this->mainDonorRows && $this->donor_rows && $this->id !== $this->donor_rows) {
            $donor = self::findOne($this->donor_rows);
            while ($donor && $donor->donor && $donor->id !== $donor->donor) {
                $donor = self::findOne($donor->donor);
            }
            $this->mainDonorRows = $donor;
        }

        return $this->mainDonorRows;
    }

    public function getMainDonorColumns(): ?FoquzQuestion
    {
        if (!$this->mainDonorColumns && $this->donor_columns && $this->id !== $this->donor_columns) {
            $donor = self::findOne($this->donor_columns);
            while ($donor && $donor->donor && $donor->id !== $donor->donor) {
                $donor = self::findOne($donor->donor);
            }
            $this->mainDonorColumns = $donor;
        }

        return $this->mainDonorColumns;
    }

    public function getRowsDonor()
    {
        return $this->hasOne(self::class, ['id' => 'donor_rows']);
    }

    public function getColumnsDonor()
    {
        return $this->hasOne(self::class, ['id' => 'donor_columns']);
    }

    public function getAnswerQuotesDetail()
    {
        return $this->hasMany(FoquzPollAnswerQuotesDetail::class, ['question_id' => 'id'])
            ->andWhere(['deleted_at' => null]);
    }

    public function getMainTypeString()
    {
        $types = [
            self::TYPE_ASSESSMENT       => 'Оценка',
            self::TYPE_VARIANTS         => 'Варианты на выбор',
            self::TYPE_TEXT_ANSWER      => 'Текстовый ответ',
            self::TYPE_DATE             => 'Дата/время',
            self::TYPE_ADDRESS          => 'Адрес',
            self::TYPE_FILE_UPLOAD      => 'Загрузка файла',
            self::TYPE_FORM             => 'Анкета',
            self::TYPE_VARIANT_STAR     => 'Звёздный рейтинг вариантов',
            self::TYPE_PRIORITY         => 'Приоритет',
            self::TYPE_CHOOSE_MEDIA     => 'Выбор изображения/видео',
            self::TYPE_GALLERY_RATING   => 'Рейтинг галереи',
            self::TYPE_SMILE_RATING     => $this->collectSmileRatingString(),
            self::TYPE_NPS_RATING       => 'Рейтинг NPS',
            self::TYPE_SIMPLE_MATRIX    => 'Простая матрица',
            self::TYPE_3D_MATRIX        => '3D матрица',
            self::TYPE_SEM_DIFFERENTIAL => 'Семантический дифференциал',
            self::TYPE_STAR_RATING => 'Звёздный рейтинг',
            self::TYPE_FILIAL => 'Выбор филиала',
            self::TYPE_RATING => 'Рейтинг',
            self::TYPE_DICTIONARY => 'Классификатор',
            self::TYPE_SCALE => 'Шкала',
            self::TYPE_CARD_SORTING_CLOSED => 'Закрытая карточная сортировка',
            self::TYPE_DISTRIBUTION_SCALE => 'Распределительная шкала',
            self::TYPE_FIRST_CLICK => 'Тест первого клика',
        ];
        return $types[$this->main_question_type];
    }

    public function isRating()
    {
        return in_array($this->main_question_type, [
            self::TYPE_ASSESSMENT,
            self::TYPE_GALLERY_RATING,
            self::TYPE_SMILE_RATING,
            self::TYPE_NPS_RATING,
            self::TYPE_SEM_DIFFERENTIAL,
            self::TYPE_STAR_RATING,
            self::TYPE_VARIANT_STAR,
        ]);
    }

    public function collectSmileRatingString()
    {
        $string = 'Смайл рейтинг, ';
        switch ($this->smile_type) {
            case self::SMILE_HEART:
                $string .= 'сердце';
                break;
            case self::SMILE_LIKE:
                $string .= 'лайк';
                break;
            case self::SMILE_FACE:
                $string .= 'лицо (контур), ' . $this->smiles_count;
                break;
            case self::SMILE_YELLOW:
            case self::SMILE_COLOR_FACE:
                $string .= 'лицо (жёлтый фон), ' . $this->smiles_count;
                break;
            case self::SMILE_ROBOT:
                $string .= 'робот, ' . $this->smiles_count;
                break;
            case self::SMILE_EMOJI:
            case self::SMILE_DIFFERENT:
                $string .= 'эмодзи, ' . $this->smiles_count;
                break;
            case self::SMILE_WEATHER:
                $string .= 'погода, ' . $this->smiles_count;
                break;
            default:
                $string .= 'кастом, ' . $this->smiles_count;
        }

        return trim($string, ', ');
    }

    public function getTriggerString()
    {
        $triggers = [
            1 => 'Заказ доставлен',
            2 => 'Заказ оформлен',
            3 => 'Не было заказа, дней'
        ];
        return $triggers[$this->trigger];
    }

    /**
     * Возвращает максимальное количество баллов за вопрос
     * @return int|null
     */
    public function getMaxPoints(): ?int
    {
        switch ($this->main_question_type) {
            case self::TYPE_VARIANTS:
            case self::TYPE_CHOOSE_MEDIA:
                $arrayPoints = [];
                if ($this->main_question_type === self::TYPE_VARIANTS) {
                    if ($this->donor) {
                        $recipientVariants = $this->getRecipientQuestionDetails()->joinWith('questionDetail')->all();
                        foreach ($recipientVariants as $variant) {
                            if ($variant->questionDetail) {
                                $points = $variant->points ?: $variant->questionDetail->points ?: 0;
                            } else {
                                $points = $variant->points ?: 0;
                            }
                            if ($points >= 0) {
                                $arrayPoints[] = $points;
                            }
                        }
                    } else {
                        foreach ($this->questionDetails as $detail) {
                            if ($detail->type === FoquzQuestionDetail::TYPE_NOTHING) {
                                $points = $detail->points ?? 0;
                                if ($points >= 0) {
                                    $nothingVariantPoints[] = $points;
                                }
                            } else {
                                $points = $detail->points ?? 0;
                                if ($points >= 0) {
                                    $arrayPoints[] = $points;
                                }
                            }

                        }
                    }
                } else {
                    foreach ($this->questionFiles as $file) {
                        $points = $file->points ?? 0;
                        if ($points >= 0) {
                            $arrayPoints[] = $points;
                        }
                    }
                }
                if (empty($arrayPoints)) {
                    return 0;
                }
                if ($this->variants_element_type === self::VARIANT_ELEMENT_TYPE_RADIO) {
                    $max = max($arrayPoints);
                    if (isset($nothingVariantPoints) && max($nothingVariantPoints) > $max) {
                        $max = max($nothingVariantPoints);
                    }
                    return $max < 0 ? 0 : $max;
                } else {
                    if (!$this->max_choose_variants) {
                        $val = 0;
                        foreach ($arrayPoints as $ap) {
                            if ($ap > 0) {
                                $val += $ap;
                            }
                        }
                        if (isset($nothingVariantPoints) && max($nothingVariantPoints) > $val) {
                            $val = max($nothingVariantPoints);
                        }
                        return $val;
                    } else {
                        $arrayPoints = array_filter($arrayPoints, function ($item) {
                            return $item > 0;
                        });
                        arsort($arrayPoints);
                        $points = array_slice($arrayPoints, 0, $this->max_choose_variants);
                        $points = array_sum($points);
                        if (isset($nothingVariantPoints) && max($nothingVariantPoints) > $points) {
                            $points = max($nothingVariantPoints);
                        }
                        return $points;
                    }
                }
                break;
            case self::TYPE_DATE:
            case self::TYPE_PRIORITY:
                return $this->rightAnswer && (int)$this->rightAnswer->points >= 0 ? $this->rightAnswer->points : 0;
                break;
            case self::TYPE_SIMPLE_MATRIX:
                $matrixSettings = json_decode($this->matrix_settings);
                $isNull = true;
                $max = 0;
                if (isset($matrixSettings->points)) {
                    foreach ($matrixSettings->points as $row) {
                        $row = array_filter($row, static function ($item) {
                            return $item >= 0;
                        });
                        if (count($row) == 0) {
                            $row = [0];
                        }
                        if (isset($matrixSettings->multiple_choice) && $matrixSettings->multiple_choice == 1) {
                            $max += array_sum($row);
                        } else {
                            $max += max($row);
                        }
                        if (min($row) <> 0 || max($row) <> 0) {
                            $isNull = false;
                        }
                    }
                }
                return $isNull ? null : $max;
                break;
            default:
                return 0;
        }
    }

    public function getMinPoints()
    {
        switch ($this->main_question_type) {
            case self::TYPE_VARIANTS:
            case self::TYPE_CHOOSE_MEDIA:
                $arrayPoints = [];
                if ($this->main_question_type === self::TYPE_VARIANTS) {
                    if ($this->donor) {
                        $recipientVariants = $this->getRecipientQuestionDetails()->joinWith('questionDetail')->all();
                        foreach ($recipientVariants as $variant) {
                            if ($variant->questionDetail) {
                                $arrayPoints[] = $variant->points ?: $variant->questionDetail->points ?: 0;
                            } else {
                                $arrayPoints[] = $variant->points ?: 0;
                            }
                        }
                    } else {
                        foreach ($this->questionDetails as $detail) {
                            $arrayPoints[] = $detail->points ?? 0;
                        }
                    }
                } else {
                    foreach ($this->questionFiles as $file) {
                        $arrayPoints[] = $file->points ?? 0;
                    }
                }

                //print_r($arrayPoints); exit;
                $minValue = count($arrayPoints) == 0 || min($arrayPoints) < 0 ? 0 : min($arrayPoints);
                $minValue = $this->is_self_answer ? 0 : $minValue;
                return $this->is_required ? $minValue : 0;
                break;
            case self::TYPE_DATE:
            case self::TYPE_PRIORITY:
                return 0;
                break;
            case self::TYPE_SIMPLE_MATRIX:
                $matrixSettings = json_decode($this->matrix_settings);
                $min = 0;
                if ($this->is_required && isset($matrixSettings->points)) {
                    foreach ($matrixSettings->points as $row) {
                        $minValueInRow = min($row);
                        $min += ($minValueInRow < 0 ? 0 : $minValueInRow);
                    }
                }
                return $min;
                break;
            default:
                return 0;
        }
    }

    public function getAvgPoint()
    {
        if (isset(Yii::$app->user->identity) && Yii::$app->user->identity->isFilialEmployee() && Yii::$app->user->identity->getUserFilials()->count() > 0) {
            $filials = ArrayHelper::getColumn(Yii::$app->user->identity->userFilials, 'filial_id');
            return (int)(new Query())
                ->select(new Expression('ROUND(AVG(foquz_poll_answer_item.points))'))
                ->from('foquz_poll_answer_item')
                ->leftJoin('foquz_poll_answer', 'foquz_poll_answer.id = foquz_poll_answer_item.foquz_poll_answer_id')
                ->leftJoin('foquz_poll', 'foquz_poll.id = foquz_poll_answer.id')
                ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
                ->where(['foquz_question_id' => $this->id])
                ->andWhere([
                    'in',
                    'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)',
                    $filials
                ])
                ->scalar();
        } else {
            return (int)(new Query())
                ->select(new Expression('AVG(points)'))
                ->from('foquz_poll_answer_item')
                ->where([
                    'foquz_question_id' => $this->id
                ])
                ->scalar();
        }
    }

    public function copy($data): FoquzQuestion
    {
        $extraQuestionService = new ExtraQuestionService();
        $IDs = [];
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $newQuestion = new FoquzQuestion();
            $newQuestion->attributes = $this->attributes;
            $newQuestion->load($data, '');
            $newQuestion->save();

            if ($this->donor) {
                $thisRecipientDetail = RecipientQuestionDetail::findAll(['recipient_id' => $this->id]);
                if ($thisRecipientDetail) {
                    foreach ($thisRecipientDetail as $detail) {
                        $recipientDetail = new RecipientQuestionDetail();
                        $recipientDetail->attributes = $detail->attributes;
                        $recipientDetail->recipient_id = $newQuestion->id;
                        $recipientDetail->save();
                    }
                }
            }

            if ($this->addressCodes) {
                $pollQuestionAC = new FoquzQuestionAddressCodes();
                $pollQuestionAC->attributes = $this->addressCodes->attributes;
                $pollQuestionAC->question_id = $newQuestion->id;
                $pollQuestionAC->save();
            }

            $langsIDCorrespondence = [];
            if ($this->poll_id !== $newQuestion->poll_id) {
                $sourcePollLangs = ArrayHelper::index($this->poll->foquzPollLangs, 'poll_lang_id');
                $targetPollLangs = ArrayHelper::index($newQuestion->poll->foquzPollLangs, 'poll_lang_id');
                /**
                 * @var int $langID
                 * @var FoquzPollLang $targetPollLang
                 */
                foreach ($targetPollLangs as $langID => $targetPollLang) {
                    if (isset($sourcePollLangs[$langID])) {
                        $langsIDCorrespondence[$sourcePollLangs[$langID]->id] = $targetPollLang->id;
                    }
                }
            }

            if ($this->questionDetails) {

                $sourceDetailsTemp = [];

                foreach ($this->questionDetails as $tqd) {
                    if ($this->donor && !$tqd->extra_question) {
                        continue;
                    }

                    if (!$tqd->is_deleted) {
                        $pollQuestionDetail = new FoquzQuestionDetail();
                        $pollQuestionDetail->attributes = $tqd->attributes;
                        $pollQuestionDetail->foquz_question_id = $newQuestion->id;
                        $pollQuestionDetail->save();

                        $sourceDetailsTemp[$tqd->id] = $pollQuestionDetail;

                        if ($tqd->file) {
                            (new FileService())->copyFile($tqd->file, $pollQuestionDetail->id);
                        }
                        if ($tqd->fileSelfVariant) {
                            (new FileService())->copyFile($tqd->fileSelfVariant, $pollQuestionDetail->id);
                        }

                        foreach ($tqd->foquzQuestionDetailLangs as $tqdLang) {
                            $pollQuestionDetailLang = new FoquzQuestionDetailLang();
                            $pollQuestionDetailLang->attributes = $tqdLang->attributes;
                            $pollQuestionDetailLang->foquz_question_id = $newQuestion->id;
                            $pollQuestionDetailLang->foquz_question_detail_id = $pollQuestionDetail->id;
                            if ($this->poll_id !== $newQuestion->poll_id) {
                                $pollQuestionDetailLang->foquz_poll_lang_id = $langsIDCorrespondence[$tqdLang->foquz_poll_lang_id] ?? null;
                            }
                            if ($pollQuestionDetailLang->foquz_poll_lang_id) {
                                $pollQuestionDetailLang->save();
                            }
                        }
                    }
                }
                $extraQuestionService->fixQuestionDetailId($sourceDetailsTemp);
                unset($sourceDetailsTemp);

            }

            if ($this->main_question_type == self::TYPE_FILIAL || $this->main_question_type == self::TYPE_DICTIONARY) {
                foreach ($this->questionDetailLangs as $tqdLang) {
                    $pollQuestionDetailLang = new FoquzQuestionDetailLang();
                    $pollQuestionDetailLang->attributes = $tqdLang->attributes;
                    $pollQuestionDetailLang->foquz_question_id = $newQuestion->id;
                    if ($this->poll_id !== $newQuestion->poll_id) {
                        $pollQuestionDetailLang->foquz_poll_lang_id = $langsIDCorrespondence[$tqdLang->foquz_poll_lang_id] ?? null;
                    }
                    if ($pollQuestionDetailLang->foquz_poll_lang_id) {
                        $pollQuestionDetailLang->save();
                    }
                }
            }

            if ($selfVariantFile = FoquzFile::findOne([
                'entity_type' => FoquzFile::TYPE_SELF_VARIANT,
                'entity_id'   => $this->id
            ])) {
                (new FileService())->copyFile($selfVariantFile, $newQuestion->id);
            }

            if ($this->differentialRows) {
                foreach ($this->differentialRows as $tqdr) {
                    $pollQuestionDifferentialRow = new FoquzQuestionDifferentialRow();
                    $pollQuestionDifferentialRow->attributes = $tqdr->attributes;
                    $pollQuestionDifferentialRow->question_id = $newQuestion->id;
                    $pollQuestionDifferentialRow->save();
                    $IDs['differentialRows'][$tqdr->id] = $pollQuestionDifferentialRow->id;
                }
            }

            if ($this->intermediateBlock) {
                $pollQuestionIntBlock = new FoquzQuestionIntermediateBlockSetting();
                $pollQuestionIntBlock->attributes = $this->intermediateBlock->attributes;
                $pollQuestionIntBlock->question_id = $newQuestion->id;
                $pollQuestionIntBlock->save();
                if ($this->intermediateBlock->socNetworks) {
                    $pollQIBSN = new FoquzQuestionIntermediateBlockSettingSocNetworks();
                    $pollQIBSN->attributes = $this->intermediateBlock->socNetworks->attributes;
                    $pollQIBSN->intermediate_block_id = $pollQuestionIntBlock->id;
                    $pollQIBSN->save();
                }

                foreach ($this->intermediateBlock->langs as $lang) {
                    $pollQuestionIntBlockLang = new FoquzQuestionIntermediateBlockSettingLang();
                    $pollQuestionIntBlockLang->attributes = $lang->attributes;
                    $pollQuestionIntBlockLang->setting_id = $pollQuestionIntBlock->id;
                    if ($this->poll_id !== $newQuestion->poll_id) {
                        $pollQuestionIntBlockLang->lang_id = $langsIDCorrespondence[$lang->lang_id] ?? null;
                    }
                    if ($pollQuestionIntBlockLang->lang_id) {
                        $pollQuestionIntBlockLang->save();
                    }
                }
            }

            if ($this->semDifSetting) {
                $pollQuestionSemDif = new FoquzQuestionSemDifSetting();
                $pollQuestionSemDif->attributes = $this->semDifSetting->attributes;
                $pollQuestionSemDif->foquz_question_id = $newQuestion->id;
                $pollQuestionSemDif->save();
            }

            if ($this->questionSmiles) {
                foreach ($this->questionSmiles as $tqqs) {
                    $pollQuestionSmile = new FoquzQuestionSmile();
                    $pollQuestionSmile->attributes = $tqqs->attributes;
                    $pollQuestionSmile->foquz_question_id = $newQuestion->id;
                    $pollQuestionSmile->save();
                    $IDs['smiles'][$tqqs->id] = $pollQuestionSmile->id;
                }
            }

            if ($this->npsRatingSetting) {
                $pollQuestionNPS = new FoquzQuestionNpsRatingSetting();
                $pollQuestionNPS->attributes = $this->npsRatingSetting->attributes;
                $pollQuestionNPS->foquz_question_id = $newQuestion->id;
                $pollQuestionNPS->save();
            }

            if ($this->starRatingOptions) {
                $pollQuestionStarRating = new FoquzQuestionStarRatingOptions();
                $pollQuestionStarRating->attributes = $this->starRatingOptions->attributes;
                $pollQuestionStarRating->foquz_question_id = $newQuestion->id;
                $pollQuestionStarRating->save();
            }

            if ($this->scaleRatingSetting) {
                $pollQuestionStarRating = new FoquzQuestionScaleRatingSetting();
                $pollQuestionStarRating->attributes = $this->scaleRatingSetting->attributes;
                $pollQuestionStarRating->foquz_question_id = $newQuestion->id;
                $pollQuestionStarRating->save();
            }

            if ($this->rightAnswer) {
                $pollQuestionRightAnswer = new FoquzQuestionRightAnswer();
                $pollQuestionRightAnswer->attributes = $this->rightAnswer->attributes;
                $pollQuestionRightAnswer->foquz_question_id = $newQuestion->id;
                $pollQuestionRightAnswer->save();
            }

            if ($this->foquzQuestionPrioritySettings) {
                $settings = new FoquzQuestionPrioritySettings();
                $settings->attributes = $this->foquzQuestionPrioritySettings->attributes;
                $settings->foquz_question_id = $newQuestion->id;
                $settings->save();
            }

            if ($this->formFields) {
                foreach ($this->formFields as $tqff) {
                    $pollQuestionFormField = new FoquzQuestionFormField();
                    $pollQuestionFormField->attributes = $tqff->attributes;
                    $pollQuestionFormField->question_id = $newQuestion->id;
                    $pollQuestionFormField->save();

                    foreach ($tqff->langs as $tqffl) {
                        $pollQuestionFormFieldLang = new FoquzQuestionFormFieldLang();
                        $pollQuestionFormFieldLang->attributes = $tqffl->attributes;
                        $pollQuestionFormFieldLang->form_field_id = $pollQuestionFormField->id;
                        if ($this->poll_id !== $newQuestion->poll_id) {
                            $pollQuestionFormFieldLang->foquz_poll_lang_id = $langsIDCorrespondence[$tqffl->foquz_poll_lang_id] ?? null;
                        }
                        if ($pollQuestionFormFieldLang->foquz_poll_lang_id) {
                            $pollQuestionFormFieldLang->save();
                        }
                    }
                }
            }

            if ($this->questionFiles) {
                foreach ($this->questionFiles as $tqqf) {
                    $pollQuestionFile = new FoquzQuestionFile();
                    $pollQuestionFile->attributes = $tqqf->attributes;
                    $pollQuestionFile->created_at = time();
                    $pollQuestionFile->question_id = $newQuestion->id;

                    if ($tqqf->attachment_type !== 'link' && stristr('img/', $tqqf->file_path) === false) {
                        $pollQuestionFile->file_path = str_replace($tqqf->question_id, $newQuestion->id,
                            $tqqf->file_path);
                        $pollQuestionFile->file_full_path = str_replace($tqqf->question_id, $newQuestion->id,
                            $tqqf->file_full_path);

                        $fullPath = \Yii::getAlias("@app/web/uploads/foquz/" . $newQuestion->id);
                        if (!file_exists($fullPath) && !mkdir($fullPath, 0777, true) && !is_dir($fullPath)) {
                            throw new \RuntimeException(sprintf('Directory "%s" was not created', $fullPath));
                        }
                        try {
                            copy(\Yii::getAlias("@app/web/{$tqqf->file_path}"),
                                \Yii::getAlias("@app/web/{$pollQuestionFile->file_path}"));
                            if (file_exists(\Yii::getAlias("@app/web/{$tqqf->file_path}.jpg"))) {
                                try {
                                    copy(\Yii::getAlias("@app/web/{$tqqf->file_path}.jpg"),
                                        \Yii::getAlias("@app/web/{$pollQuestionFile->file_path}.jpg"));
                                } catch (\Exception $e) {
                                    echo "Can't copy poster video image for question " . $newQuestion->id . "\n";
                                }
                            }
                        } catch (\Exception $e) {
                            echo "Can't copy question file for question " . $newQuestion->id . "\n";
                            echo $fullPath . "\n";
                            echo $e->getMessage() . "\n";
                        }
                    }

                    $pollQuestionFile->save();

                    foreach ($tqqf->langs as $tqqfl) {
                        $pollQuestionFileLang = new FoquzQuestionFileLang();
                        $pollQuestionFileLang->attributes = $tqqfl->attributes;
                        $pollQuestionFileLang->foquz_question_file_id = $pollQuestionFile->id;
                        if ($this->poll_id !== $newQuestion->poll_id) {
                            $pollQuestionFileLang->foquz_poll_lang_id = $langsIDCorrespondence[$tqqfl->foquz_poll_lang_id] ?? null;
                        }
                        if ($pollQuestionFileLang->foquz_poll_lang_id) {
                            $pollQuestionFileLang->save();
                        }
                    }
                }
            }

            if ($this->foquzQuestionEndScreenLogos) {
                foreach ($this->foquzQuestionEndScreenLogos as $tqqf) {
                    $pollQuestionFile = new FoquzQuestionEndScreenLogo();
                    $pollQuestionFile->attributes = $tqqf->attributes;
                    $pollQuestionFile->foquz_question_id = $newQuestion->id;

                    if ($tqqf->logo) {
                        $pollQuestionFile->logo = str_replace('/' . $tqqf->foquz_question_id . '/',
                            '/' . $newQuestion->id . '/', $tqqf->logo);

                        $fullPath = \Yii::getAlias("@app/web/uploads/foquz/end-screen-logos/" . $newQuestion->id);
                        if (!file_exists($fullPath) && !mkdir($fullPath, 0777, true) && !is_dir($fullPath)) {
                            throw new \RuntimeException(sprintf('Directory "%s" was not created', $fullPath));
                        }
                        try {
                            copy(\Yii::getAlias("@app/web/{$tqqf->logo}"),
                                \Yii::getAlias("@app/web/{$pollQuestionFile->logo}"));
                        } catch (\Exception $e) {
                            echo "Can't copy question file for question " . $newQuestion->id . "\n";
                            echo $fullPath . "\n";
                            echo $e->getMessage() . "\n";
                        }
                    }

                    $pollQuestionFile->save();

                    foreach ($tqqf->langs as $lang) {
                        $pollQuestionFileLang = new FoquzQuestionFileLang();
                        $pollQuestionFileLang->attributes = $lang->attributes;
                        $pollQuestionFileLang->end_screen_logo_id = $pollQuestionFile->id;
                        if ($this->poll_id !== $newQuestion->poll_id) {
                            $pollQuestionFileLang->foquz_poll_lang_id = $langsIDCorrespondence[$lang->foquz_poll_lang_id] ?? null;
                        }
                        if ($pollQuestionFileLang->foquz_poll_lang_id) {
                            $pollQuestionFileLang->save();
                        }
                    }
                }
            }

            if ($this->activeMatrixElements) {
                foreach ($this->activeMatrixElements as $tame) {
                    $pollQuestionActiveMatrixElement = new FoquzQuestionMatrixElement();
                    $pollQuestionActiveMatrixElement->attributes = $tame->attributes;
                    $pollQuestionActiveMatrixElement->foquz_question_id = $newQuestion->id;
                    $pollQuestionActiveMatrixElement->save();

                    if ($tame->langs) {
                        foreach ($tame->langs as $taml) {
                            $pollQuestionActiveMatrixElementLang = new FoquzQuestionMatrixElementLang();
                            $pollQuestionActiveMatrixElementLang->attributes = $taml->attributes;
                            $pollQuestionActiveMatrixElementLang->matrix_element_id = $pollQuestionActiveMatrixElement->id;
                            if ($this->poll_id !== $newQuestion->poll_id) {
                                $pollQuestionActiveMatrixElementLang->poll_lang_id = $langsIDCorrespondence[$taml->poll_lang_id] ?? null;
                            }
                            if ($pollQuestionActiveMatrixElementLang->poll_lang_id) {
                                $pollQuestionActiveMatrixElementLang->save();
                            }
                        }
                    }

                    foreach ($tame->activeVariants as $tav) {
                        $pollQuestionActiveVariant = new FoquzQuestionMatrixElementVariant();
                        $pollQuestionActiveVariant->attributes = $tav->attributes;
                        $pollQuestionActiveVariant->matrix_element_id = $pollQuestionActiveMatrixElement->id;
                        $pollQuestionActiveVariant->save();

                        if ($tav->langs) {
                            foreach ($tav->langs as $tavl) {
                                $pollQuestionActiveVariantLang = new FoquzQuestionMatrixElementVariantLang();
                                $pollQuestionActiveVariantLang->attributes = $tavl->attributes;
                                $pollQuestionActiveVariantLang->variant_id = $pollQuestionActiveVariant->id;
                                if ($this->poll_id !== $newQuestion->poll_id) {
                                    $pollQuestionActiveVariantLang->poll_lang_id = $langsIDCorrespondence[$tavl->poll_lang_id] ?? null;
                                }
                                if ($pollQuestionActiveVariantLang->poll_lang_id) {
                                    $pollQuestionActiveVariantLang->save();
                                }
                            }
                        }
                    }
                }
            }

            $columns = [];
            $rows = [];
            foreach ($this->foquzQuestionLangs as $item) {
                $attributes = $item->attributes;
                unset($attributes['id']);
                $attributes['foquz_question_id'] = $newQuestion->id;
                if ($this->poll_id !== $newQuestion->poll_id) {
                    $attributes['foquz_poll_lang_id'] = $langsIDCorrespondence[$item->foquz_poll_lang_id] ?? null;
                }
                if (is_string($attributes['labels'])) {
                    $attributes['labels'] = json_decode($attributes['labels'], true) ?? $attributes['labels'];
                }
                if (is_iterable($attributes['labels'])) {
                    foreach ($attributes['labels'] as $key => $value) {
                        switch ($this->main_question_type) {
                            case self::TYPE_SMILE_RATING:
                                $type = 'smiles';
                                break;
                            case self::TYPE_SEM_DIFFERENTIAL:
                                $type = 'differentialRows';
                                break;
                            default:
                                $type = null;
                        }
                        if ($type && !empty($IDs[$type][$key])) {
                            $attributes['labels'][$IDs[$type][$key]] = $value;
                            unset($attributes['labels'][$key]);
                        }
                    }
                }
                $attributes['labels'] = json_encode($attributes['labels']) ?? null;
                if (empty($rows)) {
                    $columns = array_keys($attributes);
                }
                if ($attributes['foquz_poll_lang_id']) {
                    $rows[] = $attributes;
                }
            }
            if (!empty($rows)) {
                try {
                    Yii::$app->db->createCommand()->batchInsert(FoquzQuestionLang::tableName(), $columns,
                        $rows)->execute();
                } catch (\Exception $e) {
                    echo "Can't copy rows for " . FoquzQuestionLang::tableName() . "\n";
                    echo $e->getMessage() . "\n";
                }
            }

            $rows = [];
            foreach ($this->dictionaryLangs as $item) {
                $attributes = $item->attributes;
                unset($attributes['id']);
                $attributes['foquz_question_id'] = $newQuestion->id;
                if ($this->poll_id !== $newQuestion->poll_id) {
                    $attributes['foquz_poll_lang_id'] = $langsIDCorrespondence[$item->foquz_poll_lang_id] ?? null;
                }
                if (empty($rows)) {
                    $columns = array_keys($attributes);
                }
                if ($attributes['foquz_poll_lang_id']) {
                    $rows[] = $attributes;
                }
            }
            if (!empty($rows)) {
                try {
                    Yii::$app->db->createCommand()->batchInsert(FoquzQuestionDetailLang::tableName(), $columns,
                        $rows)->execute();
                } catch (\Exception $e) {
                    echo "Can't copy rows for " . FoquzQuestionDetailLang::tableName() . "\n";
                    echo $e->getMessage() . "\n";
                }
            }

            if ($this->cardSortingCategories) {
                foreach ($this->cardSortingCategories as $category) {
                    $cardSortingCategory = new FoquzQuestionCardSortingCategory();
                    $attributes = $category->attributes;
                    unset($attributes['id']);
                    $attributes['foquz_question_id'] = $newQuestion->id;
                    $cardSortingCategory->attributes = $attributes;
                    $cardSortingCategory->save();

                    foreach ($category->langs as $lang) {
                        $pollQuestionCategoryLang = new FoquzQuestionCardSortingCategoryLang();
                        $pollQuestionCategoryLang->category_id = $cardSortingCategory->id;
                        $newLangId = FoquzPollLang::find()
                            ->select('id')
                            ->where(['foquz_poll_id' => $newQuestion->poll_id, 'poll_lang_id' => $lang->lang->poll_lang_id])
                            ->scalar();
                        $pollQuestionCategoryLang->foquz_poll_lang_id = $newLangId;
                        $pollQuestionCategoryLang->name = $lang->name;
                        $pollQuestionCategoryLang->save();
                    }
                }
            }

            if ($this->firstClick) {
                $this->firstClick->copy($newQuestion->id);
            }
            if ($this->firstClickArea) {
                foreach ($this->firstClickArea as $area) {
                    $area->copy($newQuestion->id);
                }
            }

            $transaction->commit();
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }

        return $newQuestion;
    }

    public function getPollDisplayPageQuestion()
    {
        return $this->hasOne(FoquzPollDisplayPageQuestion::class, ['question_id' => 'id']);
    }

    public function getFoquzQuestionEndScreenLogos()
    {
        return $this->hasMany(FoquzQuestionEndScreenLogo::class, ['foquz_question_id' => 'id']);
    }

    /** @return bool */
    public function isNpsWithVariants()
    {
        return $this->main_question_type === FoquzQuestion::TYPE_NPS_RATING && (bool)$this->set_variants;
    }

    /** @return bool */
    public function isScaleWithVariants()
    {
        return $this->main_question_type === FoquzQuestion::TYPE_SCALE && (bool)$this->set_variants;
    }

    /** Возвращает случайный рейтинг в диапазоне NPS
     *
     * @return int
     */
    public function genNpsRating()
    {
        return rand($this->from_one, 10);
    }

    /** Возвращает случайный рейтинг в диапазоне Star rating
     *
     * @return int
     * @throws \Exception
     */
    public function genStarRating()
    {
        return random_int($this->skip_variant ? 0 : 1, $this->starRatingOptions->count);
    }

    /** Возвращает случайный $self_variant
     *
     * @return string | null
     */
    public function genComment()
    {
        $self_variant = null;

        if ($this->comment_enabled || $this->is_self_answer) {
            if ($this->comment_required || rand(1, 2) === 2) {
                $self_variant = $this->randomTextAnswers(true);
            }
        }

        return $self_variant;
    }

    /** Возвращает список филиалов вместе с удаленными
     *
     * @param bool $getStatistic // вернуть статистику
     * @return array
     */
    public function filialActualList(bool $getStatistic = false): array
    {
        $variants = [];
        $statistics = [];

        $deletedVariants = $this->deleted_detail_question ? json_decode($this->deleted_detail_question) : [];
        if (!is_array($deletedVariants)) {
            $deletedVariants = [];
        }
        $detail_question = array_unique(array_merge(json_decode($this->detail_question) ?? [], $deletedVariants));

        if ($this->main_question_type !== self::TYPE_FILIAL || empty($detail_question)) {
            return [];
        }

        /** @var Filial[] $filials */
        $filials = Filial::find()
            ->where(['id' => $detail_question])
            ->all();

        foreach ($filials as $filial) {
            $statistics[$filial->id] = 0;

            $variants[] = [
                'id'         => (string)$filial->id,
                'value'      => $filial->name,
                'is_deleted' => !empty($deletedVariants) && array_key_exists($filial->id,
                    array_flip($deletedVariants)) ? 1 : 0,
            ];
        }

        if ($getStatistic === true) {
            return $statistics;
        }

        return $variants;
    }

    /** Возвращает список филиалов и категорий (включая удаленные)
     *
     * @param bool $withDeleted
     * @return array
     */
    public function getFilialsWithCategories(bool $withDeleted = true): array
    {
        $deletedVariants = json_decode($this->deleted_detail_question) ?? [];
        $detailQuestion = json_decode($this->detail_question) ?? [];

        if ($this->main_question_type !== self::TYPE_FILIAL || !is_array($deletedVariants) || !is_array($detailQuestion)) {
            return [];
        }

        if (!$withDeleted) {
            $filialIDs = array_unique($detailQuestion);
        } else {
            $filialIDs = array_unique(array_merge($detailQuestion, $deletedVariants));
        }

        if (empty($filialIDs)) {
            return [];
        }

        $filials = Filial::find()
            ->where(['id' => $filialIDs, 'company_id' => $this->poll->company_id]);

        if (!$withDeleted) {
            $filials->andWhere(['is_active' => 1]);
        }

        $filials = $filials->all();

        $filials = ArrayHelper::index($filials, null, 'category_id');

        if (!$filials) {
            return [];
        }

        if (isset($filials[null])) {
            $filials[0] = $filials[null];
            unset($filials[null]);
        }

        $filialCategories = [];
        if (!empty(array_keys($filials))) {
            $filialCategories = FilialCategory::find()
                ->where(['id' => array_keys($filials)]);

            if (!$withDeleted) {
                $filialCategories->andWhere(['is_deleted' => 0]);
            }

            $filialCategories = $filialCategories->all();
        }
        /** @var FilialCategory[] $filialCategories */

        $detailLangs = FoquzQuestionDetailLang::findAll([
            'foquz_question_id' => $this->id,
            'detail_id'         => $filialIDs,
        ]);
        $detailLangs = ArrayHelper::index($detailLangs, null, 'detail_id');

        $result = [];
        foreach ($filialCategories as $category) {
            /** @var Filial[] $categoryFilials */
            $categoryFilials = $filials[$category->id] ?? [];
            if (!$categoryFilials) {
                continue;
            }

            $result[] = [
                'category' => [
                    'id'         => $category->id,
                    'name'       => $category->name,
                    'is_deleted' => (bool)$category->is_deleted,
                    'count'      => count($categoryFilials),
                ],
                'items'    => array_map(static function ($filial) use ($detailLangs, $category) {
                    return [
                        'id'            => $filial->id,
                        'name'          => $filial->name,
                        'category_name' => $category->name,
                        'is_deleted'    => !$filial->is_active,
                        'detailLangs'   => $detailLangs[$filial->id] ?? null,
                    ];
                }, $categoryFilials),
            ];
        }

        if (isset($filials[0])) {
            /** @var Filial[] $categoryFilials */
            $categoryFilials = $filials[0];
            $result[] = [
                'category' => [
                    'id'         => 0,
                    'name'       => 'Без категории',
                    'is_deleted' => false,
                    'count'      => count($categoryFilials),
                ],
                'items'    => array_map(static function ($filial) use ($detailLangs) {
                    return [
                        'id'            => $filial->id,
                        'name'          => $filial->name,
                        'category_name' => 'Без категории',
                        'is_deleted'    => !$filial->is_active,
                        'detailLangs'   => $detailLangs[$filial->id] ?? null,
                    ];
                }, $categoryFilials),
            ];
        }
        return $result;
    }

    /** Возвращает список филиалов и категорий (включая удаленные)
     *
     * @param bool $withDeleted
     * @return array
     */
    public function getFilialsTree(bool $withDeleted = true): array
    {
        $deletedVariants = json_decode($this->deleted_detail_question) ?? [];
        $detailQuestion = json_decode($this->detail_question) ?? [];

        if ($this->main_question_type !== self::TYPE_FILIAL || !is_array($deletedVariants) || !is_array($detailQuestion)) {
            return [];
        }

        if (!$withDeleted) {
            $filialIDs = array_unique($detailQuestion);
        } else {
            $filialIDs = array_unique(array_merge($detailQuestion, $deletedVariants));
        }

        if (empty($filialIDs)) {
            return [];
        }

        $filials = Filial::find()
            ->where(['id' => $filialIDs, 'company_id' => $this->poll->company_id]);

        if (!$withDeleted) {
            $filials->andWhere(['is_active' => 1]);
        }

        $filials = $filials->all();

        $filials = ArrayHelper::index($filials, null, 'category_id');

        if (!$filials) {
            return [];
        }

        if (isset($filials[null])) {
            $filials[0] = $filials[null];
            unset($filials[null]);
        }

        $filialCategories = [];
        if (!empty(array_keys($filials))) {
            $filialCategories = FilialCategory::find()
                ->where(['id' => array_keys($filials)]);

            if (!$withDeleted) {
                $filialCategories->andWhere(['is_deleted' => 0]);
            }

            $filialCategories = $filialCategories->all();
        }
        /** @var FilialCategory[] $filialCategories */

        $detailLangs = FoquzQuestionDetailLang::findAll([
            'foquz_question_id' => $this->id,
            'detail_id'         => $filialIDs,
        ]);
        $detailLangs = ArrayHelper::index($detailLangs, null, 'detail_id');

        $result = [];
        foreach ($filialCategories as $position => $category) {
            /** @var Filial[] $categoryFilials */
            $categoryFilials = $filials[$category->id] ?? [];
            $result[$category->name] = [
                'id'          => $category->id,
                'title'       => $category->name,
                'description' => null,
                'langs'       => null,
                'position'    => $position,
                'isCategory'  => true,
                'children'    => [],
            ];
            foreach ($categoryFilials as $positionF => $filial) {
                $result[$category->name]['children'][$filial->name] = [
                    'id'          => $filial->id,
                    'title'       => $filial->name,
                    'description' => null,
                    'langs'       => $detailLangs[$filial->id] ?? null,
                    'isCategory'  => false,
                    'position'    => $positionF,
                ];
            }
        }

        $position = count($filialCategories);
        if (isset($filials[0])) {
            /** @var Filial[] $categoryFilials */
            $categoryFilials = $filials[0];
            foreach ($categoryFilials as $positionF => $filial) {
                $result[$filial->name] = [
                    'id'          => $filial->id,
                    'title'       => $filial->name,
                    'description' => null,
                    'langs'       => $detailLangs[$filial->id] ?? null,
                    'isCategory'  => false,
                    'position'    => $position + $positionF,
                ];
            }
        }
        return $result;
    }

    public function getDictionaryTree(bool $withDeleted = true): array
    {
        $deletedVariants = json_decode($this->deleted_detail_question) ?? [];
        $detailQuestion = json_decode($this->detail_question) ?? [];

        if ($this->main_question_type !== self::TYPE_DICTIONARY || !is_array($deletedVariants) || !is_array($detailQuestion)) {
            return [];
        }

        if (!$withDeleted) {
            $dictionaryElementsIDs = array_unique($detailQuestion);
        } else {
            $dictionaryElementsIDs = array_unique(array_merge($detailQuestion, $deletedVariants));
        }

        if (empty($dictionaryElementsIDs)) {
            return [];
        }

        $elements = DictionaryElement::find()
            ->where(['dictionary_id' => $this->dictionary_id])
            ->asArray()
            ->all();
        $elements = ArrayHelper::index($elements, 'id');
        DictionariesHelper::getTreeIDS($elements, $dictionaryElementsIDs);
        $dictionaryElementsIDs = array_unique($dictionaryElementsIDs);
        $langs = FoquzQuestionDetailLang::find()
            ->where(['foquz_question_id' => $this->id, 'detail_id' => $dictionaryElementsIDs])
            ->all();
        $langs = ArrayHelper::index($langs, null, 'detail_id');
        $elements = array_filter($elements, static function ($element) use ($dictionaryElementsIDs) {
            return in_array($element['id'], $dictionaryElementsIDs);
        });
        $tree = DictionariesHelper::buildTreeLight($elements);
        $questionTree = [];
        DictionariesHelper::prepareTreeForQuestion($tree, $questionTree, $langs);
        return $questionTree['children'] ?? [];
    }

    public function getSelfVariantFile(): ?array
    {
        if (
            ($this->main_question_type === self::TYPE_NPS_RATING || $this->main_question_type === self::TYPE_SMILE_RATING) &&
            $this->is_self_answer &&
            $this->variants_with_files &&
            $selfVariantFile = FoquzFile::findOne([
                'entity_type' => FoquzFile::TYPE_SELF_VARIANT,
                'entity_id'   => $this->id
            ])
        ) {
            return [
                'file_id'     => $selfVariantFile->id ?? null,
                'file_url'    => $selfVariantFile->fileUrl ?? null,
                'preview_url' => $selfVariantFile->previewUrl ?? null,
            ];
        }

        return null;
    }

    /**
     * Удаляет из HTML вредоносные и невалидные теги
     * @param $value
     * @return string
     */
    public function filterHTML($value)
    {
        if (!is_string($value)) {
            return $value;
        }
        return HtmlPurifier::process($value, [
            'HTML.TargetBlank' => true,
        ]);
    }

    public function clearHTML($value)
    {
        if (!is_string($value)) {
            return $value;
        }
        $value = str_replace(['<', '>'], [' <', '> '], $value);
        $value = str_replace('&nbsp;', ' ', strip_tags($value));
        return trim(preg_replace('/\s+/', ' ', $value));
    }

    private function syncRecipients(): void
    {
        $recipients = self::find()
            ->where(['is_deleted' => 0])
            ->andWhere([
                'OR',
                ['donor_columns' => $this->id],
                ['donor_rows' => $this->id],
            ])->all();
        if (!$recipients) {
            return;
        }
        /** @var FoquzQuestion $recipient */
        foreach ($recipients as $recipient) {
            if ($recipient->donor_rows === $this->id) {
                if (!$this->is_self_answer) {
                    FoquzQuestionMatrixElement::updateAll(['is_deleted' => 1], [
                        'is_deleted'        => 0,
                        'foquz_question_id' => $recipient->id,
                        'type_id'           => FoquzQuestionMatrixElement::TYPE_ROW,
                        'donor_variant_id'  => null
                    ]);
                } else {
                    if (!$matrixRow = FoquzQuestionMatrixElement::findOne([
                        'foquz_question_id' => $recipient->id,
                        'type_id'           => FoquzQuestionMatrixElement::TYPE_ROW,
                        'donor_variant_id'  => null
                    ])) {
                        $model = new FoquzQuestionMatrixElement();
                    } else {
                        $model = $matrixRow;
                    }
                    $model->type_id = FoquzQuestionMatrixElement::TYPE_ROW;
                    $model->foquz_question_id = $recipient->id;
                    $model->name = $this->self_variant_text ?: 'Свой вариант';
                    $model->position = FoquzQuestionMatrixElement::find()->where([
                            'foquz_question_id' => $recipient->id,
                            'type_id'           => FoquzQuestionMatrixElement::TYPE_ROW
                        ])->count() + 1;
                    $model->donor_variant_id = null;
                    $model->is_deleted = 0;
                    $model->save();
                }
            }
            if ($recipient->donor_columns === $this->id) {
                if (!$this->is_self_answer) {
                    FoquzQuestionMatrixElement::updateAll(['is_deleted' => 1], [
                        'is_deleted'        => 0,
                        'foquz_question_id' => $recipient->id,
                        'type_id'           => FoquzQuestionMatrixElement::TYPE_COLUMN,
                        'donor_variant_id'  => null
                    ]);
                } else {
                    if (!$matrixColumn = FoquzQuestionMatrixElement::findOne([
                        'foquz_question_id' => $recipient->id,
                        'type_id'           => FoquzQuestionMatrixElement::TYPE_COLUMN,
                        'donor_variant_id'  => null
                    ])) {
                        $model = new FoquzQuestionMatrixElement();
                    } else {
                        $model = $matrixColumn;
                    }
                    $model->type_id = FoquzQuestionMatrixElement::TYPE_COLUMN;
                    $model->foquz_question_id = $recipient->id;
                    $model->name = $this->self_variant_text ?: 'Свой вариант';
                    $model->position = FoquzQuestionMatrixElement::find()->where([
                            'foquz_question_id' => $recipient->id,
                            'type_id'           => FoquzQuestionMatrixElement::TYPE_COLUMN
                        ])->count() + 1;
                    $model->donor_variant_id = null;
                    $model->is_deleted = 0;
                    $model->save();
                }
            }
        }
    }

    /**
     * @param FoquzQuestion[] $questions
     * @param int $donorQuestionID
     * @return FoquzQuestion[]
     */
    public static function getRecipients(array $questions, int $donorQuestionID): array
    {
        $recipients = [];
        foreach ($questions as $question) {
            if (
                $question->donor === $donorQuestionID ||
                $question->donor_columns === $donorQuestionID ||
                $question->donor_rows === $donorQuestionID
            ) {
                $recipients[] = $question;
            }
        }
        return $recipients;
    }

    /**
     * @param array $questions
     * @param int $donorQuestionID
     * @return FoquzQuestion[]
     */
    public static function getRecipientsRecursive(array $questions, int $donorQuestionID): array
    {
        $recipients = self::getRecipients($questions, $donorQuestionID);
        foreach ($recipients as $recipient) {
            $recipients = array_merge($recipients, self::getRecipientsRecursive($questions, $recipient->id));
        }
        return $recipients;
    }

    public static function getNecessaryRelations(int|array $pollsID): array
    {
        $questionTypes = self::find()
            ->where(['poll_id' => $pollsID])
            ->select(['main_question_type'])
            ->distinct()
            ->column();

        // Проверяем, есть ли переводы для опроса
        $hasTranslations = FoquzPollLang::find()
            ->where(['foquz_poll_id' => $pollsID])
            ->exists();

        $relations = [
            'poll', 'foquzQuestionLangs', 'questionLogic', 'foquzPollQuestionViewLogics',
        ];

        foreach ($questionTypes as $type) {
            switch ($type) {
                case self::TYPE_NPS_RATING:
                    $relations[] = 'npsRatingSetting';
                    break;

                case self::TYPE_SCALE:
                case self::TYPE_DISTRIBUTION_SCALE:
                    $relations[] = 'scaleRatingSetting';
                    break;

                case self::TYPE_PRIORITY:
                    $relations[] = 'foquzQuestionPrioritySettings';
                    $relations[] = 'rightAnswer';
                    break;

                case self::TYPE_SIMPLE_MATRIX:
                case self::TYPE_3D_MATRIX:
                    $relations[] = 'activeMatrixElements.activeVariants';
                    $relations[] = 'activeMatrixElements.langs';
                    $relations[] = 'activeMatrixElements.activeVariants.langs';
                    break;

                case self::TYPE_SEM_DIFFERENTIAL:
                    $relations[] = 'differentialRows';
                    $relations[] = 'semDifSetting';
                    break;

                case self::TYPE_STAR_RATING:
                case self::TYPE_VARIANT_STAR:
                case self::TYPE_RATING:
                    $relations[] = 'starRatingOptions';
                    break;

                case self::TYPE_DATE:
                    $relations[] = 'rightAnswer';
                    break;

                case self::TYPE_INTERMEDIATE_BLOCK:
                    $relations[] = 'intermediateBlock';
                    $relations[] = 'foquzQuestionEndScreenLogos';
                    break;

                case self::TYPE_CARD_SORTING_CLOSED:
                    $relations[] = 'cardSortingCategories';
                    break;

                case self::TYPE_FIRST_CLICK:
                    $relations[] = 'firstClick';
                    $relations[] = 'firstClickArea';
                    break;

                case self::TYPE_FORM:
                    $relations[] = 'formFields';
                    break;

                case self::TYPE_ADDRESS:
                    $relations[] = 'addressCodes';
                    break;
            }
        }

        // Добавляем связи переводов только если у опроса есть переводы
        if ($hasTranslations) {
            $translationRelations = [];

            foreach ($translationRelations as $relation) {
                if (!in_array($relation, $relations)) {
                    $relations[] = $relation;
                }
            }
        }

        return array_unique($relations);
    }
}
