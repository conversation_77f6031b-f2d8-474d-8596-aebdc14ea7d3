<?php

namespace app\modules\foquz\models;

use app\models\DictionaryElement;
use Yii;
use yii\caching\TagDependency;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "foquz_question_detail".
 *
 * @property int $id
 * @property int $foquz_question_id
 * @property int $type
 * @property string $question
 * @property string $description Текст для тултипа
 * @property bool $is_empty Пустое название варианта (для вариантов и изображением/видео)
 * @property int $position
 * @property int $points
 * @property bool $without_points Без баллов
 * @property bool $is_deleted
 * @property bool $extra_question Является вариантом ответа на уточняющий вопрос (для типа вопроса Звёздный рейтинг для вариантов и Простая матрица)
 * @property bool $need_extra Вариант требует уточняющего вопроса
 * @property bool $comment_required Комментарий обязателен
 * @property int|null $dictionary_element_id ID элемента справочника для варианта
 * @property int|null $question_detail_id ID варианта опроса для уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)
 * @property string $detail_question Текст уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)
 * @property int|null $extra_question_rate_from Минимальная оценка для отображения уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)
 * @property int|null $extra_question_rate_to Максимальная оценка для отображения уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)
 * @property bool $extra_required Уточняющий вопрос обязательный (для типа УВ Для каждого варианта свой вопрос)
 * @property int|null $min_choose_extra_variants Минимальное количество выбранных вариантов для уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)
 * @property int|null $max_choose_extra_variants Максимальное количество выбранных вариантов для уточняющего вопроса (для типа УВ Для каждого варианта свой вопрос)
 * @property bool $variants_with_files Варианты ответов с изображениями/видео (плиткой)
 * @property string $self_variant_text Текст своего варианта для уточняющего вопроса
 * @property string|null $self_variant_placeholder_text Плейсхолдер для своего варианта уточняющего вопроса
 * @property int|null $variants_element_type Тип УВ (0 - один ответ, 1 - неск ответов, 2 - текстовый)
 * @property bool $for_all_rates УВ для всех оценок
 * @property string $placeholder_text Текстовый ответ для уточняющего вопроса (для УВ)
 * @property int|null $self_variant_minlength Минимальная длина своего ответа (для УВ)
 * @property int|null $self_variant_maxlength Максимальная длина своего ответа (для УВ)
 * @property int|null $text_variant_minlength Минимальная длина текстового ответа (для УВ)
 * @property int|null $text_variant_maxlength Максимальная длина текстового ответа (для УВ)
 * @property bool $is_self_answer Можно указать свой вариант? (для УВ)
 * @property bool $random_exclusion Вариант исключен из случайного порядка
 * @property int|null $recipient_question_detail_id ID УВ для варианта из донора
 *
 * @property FoquzQuestion $foquzQuestion
 * @property FoquzQuestionDetailLang[] $foquzQuestionDetailLangs
 * @property-read DictionaryElement $dictionaryElement
 * @property-read FoquzFile $file Файл, связанный с вариантом ответа
 * @property-read FoquzFile $fileSelfVariant Файл, связанный со своим вариантом ответа
 * @property-read array|null $selfVariantFile Изображение для своего варианта
 */
class FoquzQuestionDetail extends BaseModel
{
    public const TYPE_GENERAL = 0; #Обычный вариант
    public const TYPE_NOTHING = 1; #Ничего из перечисленного

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'foquz_question_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['foquz_question_id'], 'required'],
            [['foquz_question_id', 'type', 'position', 'points', 'dictionary_element_id'], 'integer'],
            [['question'], 'string', 'max' => 255],
            [['description'], 'string'],
            [['foquz_question_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzQuestion::class, 'targetAttribute' => ['foquz_question_id' => 'id']],
            [['is_deleted', 'extra_question', 'need_extra', 'without_points', 'is_empty', 'comment_required'], 'boolean'],
            [['dictionary_element_id'], 'exist', 'skipOnError' => true, 'targetClass' => DictionaryElement::class, 'targetAttribute' => ['dictionary_element_id' => 'id']],
            [['question_detail_id', 'extra_question_rate_from',
                'extra_question_rate_to', 'min_choose_extra_variants', 'max_choose_extra_variants',
                'variants_element_type'], 'integer'],
            [['question_detail_id'], 'exist', 'skipOnError' => true, 'targetAttribute' => 'id'],
            [['extra_required', 'variants_with_files', 'random_exclusion'], 'boolean'],
            [['for_all_rates','is_self_answer'], 'safe'],
            [['detail_question', 'self_variant_text', 'self_variant_placeholder_text', 'placeholder_text'], 'filter', 'filter' => 'trim'],
            [['detail_question', 'self_variant_text', 'self_variant_placeholder_text', 'placeholder_text'], 'string', 'max' => 255],
            [['self_variant_minlength', 'self_variant_maxlength', 'text_variant_minlength', 'text_variant_maxlength'], 'integer'],
        ];
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ((int) $this->foquzQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
            $this->syncRecipients($insert, $changedAttributes);
        }
        TagDependency::invalidate(Yii::$app->cache, 'foquz-question-details-' . $this->id);
        parent::afterSave($insert, $changedAttributes);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFoquzQuestion()
    {
        return $this->hasOne(FoquzQuestion::class, ['id' => 'foquz_question_id']);
    }

    public function getFoquzQuestionDetailLangs()
    {
        return $this->hasMany(FoquzQuestionDetailLang::class, ['foquz_question_detail_id' => 'id', 'foquz_question_id' => 'foquz_question_id']);
    }

    /**
     * Gets query for [[DictionaryElement]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDictionaryElement()
    {
        return $this->hasOne(DictionaryElement::class, ['id' => 'dictionary_element_id']);
    }

    private function syncRecipients($insert, $changedAttributes): void
    {
        $recipients = [];
        $poll = $this->foquzQuestion->poll;
        if ($poll) {
            /** @var FoquzQuestion[] $questions */
            $questions = $poll->getFoquzQuestions()
                ->where(['NOT', ['main_question_type' => FoquzQuestion::TYPE_INTERMEDIATE_BLOCK]])
                ->all();
            $questionIndexes = [];
            foreach ($questions as $key => $question) {
                $questionIndexes[$question->id] = $key + 1;
                if (!in_array($question->main_question_type, FoquzQuestion::TYPES_RECIPIENTS)) {
                    continue;
                }
                if (
                    $question->donor === $this->foquz_question_id ||
                    $question->donor_rows === $this->foquz_question_id ||
                    $question->donor_columns === $this->foquz_question_id
                ) {
                    $recipients[] = $question;
                    continue;
                }
                $mainDonor = $question->getMainDonor();
                $mainDonorColumns = $question->getMainDonorColumns();
                $mainDonorRows = $question->getMainDonorRows();
                if (
                    ($mainDonor && $mainDonor->id === $this->foquz_question_id) ||
                    ($mainDonorColumns && $mainDonorColumns->id === $this->foquz_question_id) ||
                    ($mainDonorRows && $mainDonorRows->id === $this->foquz_question_id)
                ) {
                    $recipients[] = $question;
                }
            }
            /** @var FoquzQuestion[] $recipients */
            $recipients = ArrayHelper::index($recipients, 'id');
            $matrixElements = FoquzQuestionMatrixElement::find()
                ->where(['donor_variant_id' => $this->id])
                ->andWhere(['foquz_question_id' => ArrayHelper::getColumn($recipients, 'id')])
                ->all();
            $matrixElements = ArrayHelper::index($matrixElements, null, 'type_id');
            $matrixRows = ArrayHelper::index($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW] ?? [], null, 'foquz_question_id');
            $matrixColumns = ArrayHelper::index($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN] ?? [], null, 'foquz_question_id');
            /** @var FoquzQuestion $recipient */
            foreach ($recipients as $recipient) {
                if ($recipient->donor_rows === $this->foquz_question_id) {
                    $matrixQuestionRows = ArrayHelper::index($matrixRows[$recipient->id] ?? [], 'donor_variant_id');
                    if ($insert || !isset($matrixQuestionRows[$this->id])) {
                        $model = new FoquzQuestionMatrixElement();
                    } else {
                        $model = $matrixQuestionRows[$this->id];
                    }
                    $model->type_id = FoquzQuestionMatrixElement::TYPE_ROW;
                    $model->foquz_question_id = $recipient->id;
                    $model->name = $this->question;
                    $model->position = $this->position;
                    $model->donor_variant_id = $this->id;
                    $model->is_deleted = $this->is_deleted;
                    $model->save();
                }
                if ($recipient->donor_columns === $this->foquz_question_id) {
                    $matrixQuestionColumns = ArrayHelper::index($matrixColumns[$recipient->id] ?? [],'donor_variant_id');
                    if ($insert || !isset($matrixQuestionColumns[$this->id])) {
                        $model = new FoquzQuestionMatrixElement();
                    } else {
                        $model = $matrixQuestionColumns[$this->id];
                    }
                    $model->type_id = FoquzQuestionMatrixElement::TYPE_COLUMN;
                    $model->foquz_question_id = $recipient->id;
                    $model->name = $this->question;
                    $model->position = $this->position;
                    $model->donor_variant_id = $this->id;
                    $model->is_deleted = $this->is_deleted;
                    $model->save();
                }
                if (
                    $recipient->donor === $this->foquz_question_id &&
                    $recipient->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX &&
                    $conditions = FoquzPollQuestionViewLogic::findAll(['condition_question_id' => $recipient->id])
                ) {
                    foreach ($conditions as $condition) {
                        $variants = $condition->variants;
                        $skipped = $condition->skipped;
                        if (empty($variants) && empty($skipped)) {
                            continue;
                        }
                        if (is_array($variants)) {
                            foreach ($variants as $key => $variant) {
                                if (
                                    isset($variant['row'], $changedAttributes['question']) &&
                                    $variant['row'] === $changedAttributes['question']
                                ) {
                                    $variants[$key]['row'] = $this->question;
                                }
                            }
                            $condition->variants = $variants;
                        }
                        if (
                            is_array($skipped) &&
                            isset($changedAttributes['question']) &&
                            $index = array_search($changedAttributes['question'], $skipped, true)
                        ) {
                            $skipped[$index] = $this->question;
                            $condition->skipped = $skipped;
                        }
                        $condition->save();
                    }
                }
            }
        }
    }

    /**
     * Возвращает файл, связанный с вариантом ответа
     * @return \yii\db\ActiveQuery
     */
    public function getFile()
    {
        return $this->hasOne(FoquzFile::class, ['entity_id' => 'id'])
            ->onCondition(['entity_type' => FoquzFile::TYPE_DETAIL]);
    }

    /**
     * Возвращает файл, связанный со своим вариантом ответа
     * @return \yii\db\ActiveQuery
     */
    public function getFileSelfVariant()
    {
        return $this->hasOne(FoquzFile::class, ['entity_id' => 'id'])
            ->onCondition(['entity_type' => FoquzFile::TYPE_SELF_VARIANT]);
    }


    public function getSelfVariantFile(): ?array
    {
        if (
            $this->is_self_answer &&
            $this->variants_with_files &&
            $selfVariantFile = FoquzFile::findOne(['entity_type' => FoquzFile::TYPE_SELF_VARIANT, 'entity_id' => $this->id])
        ) {
            return [
                'file_id' => $selfVariantFile->id ?? null,
                'file_url' => $selfVariantFile->fileUrl ?? null,
                'preview_url' => $selfVariantFile->previewUrl ?? null,
            ];
        }

        return null;
    }
}
