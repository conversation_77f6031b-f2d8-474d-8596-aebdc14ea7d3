<?php

use yii\helpers\Url;
use app\modules\foquz\assets\FoquzAsset;
use app\modules\foquz\models\FoquzQuestion;

$asset = FoquzAsset::register($this);
$isEditor = Yii::$app->user->identity->isEditor();

$logicLink = Url::to(['/foquz/foquz-poll/logic', 'id' => $_GET['pollId']], 'https');
$hasQuestions = false;
if ($poll->is_auto) {
    foreach ($questions as $q) {
        if (!$q['is_tmp']) {
            $hasQuestions = true;
            break;
        }
    }
} else {
    $hasQuestions = true;
}

foreach ($questions as $q) {
    if ($q->intermediateBlock && !$poll->point_system) {
        $q->intermediateBlock->scores_button = 0;
    }
}

$host = $_SERVER['HTTP_HOST'];

// @NOTE: Для локального хоста используется http, так как https не работает

$isLocal = strpos($host, 'localhost') !== false || strpos($host, '.local') !== false;

$scheme = $isLocal ? 'http' : 'https';

// Проверка на режим старого превью
$isOldPreview = isset($_GET['old-preview']) && $_GET['old-preview'] === '1';

// URL для прохождения на Vue (теперь по умолчанию)
$previewUrl = $isLocal
    ? 'http://localhost:5173/poll-vue/preview?pollId='.$poll->id.'&questionId='.$question_id
    : Url::to(['/poll-vue/preview', 'pollId' => $poll->id, 'questionId' => $question_id], $scheme);

// URL для прохождения на Vue в режиме торговой точки
$tradePreviewUrl = $isLocal
    ? 'http://localhost:5173/poll-vue/preview/t/?pollId='.$poll->id.'&questionId='.$question_id
    : Url::to(['/poll-vue/preview/t/', 'pollId' => $poll->id, 'questionId' => $question_id], $scheme);

// URL для старого превью
$oldPreviewUrl = Url::to(['/foquz/default/poll', 'authKey' => 'dummy', 'questionId' => $_GET['id'], 'pollPreview' => 1], $scheme);

$this->registerJs("
    var POLL = " . \yii\helpers\Json::encode($poll) . ";
    var POLL_ID = '" . $poll->id . "';
    var POLL_NAME = '" . addslashes($poll->name) . "';
    var QUESTION_ID = " . $question_id . ";
    var POLL_IS_AUTO = " . $poll->is_auto . ";
    var QUESTIONS = " . \yii\helpers\Json::encode($questions) . ";
    var IS_CONTACTS_ENABLED = " . (int) $poll->company->isContactsAccessEnabled() . ";
    var ACCESS_TOKEN = '" . Yii::$app->user->identity->access_token . "';

    var BASE_URL = '" . $asset->baseUrl . "';
", \yii\web\View::POS_HEAD);

$this->registerCSSFile('/js/poll.questions.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->registerJSFile('/js/poll.questions.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);
?>

<style>
    .hidden {
        display: none;
    }
</style>

<?= $this->render('../../../../ko/components/question-form/template.php'); ?>


<div class="poll-question-form survey-question__content survey-question__content--initializing" data-bind="childrenComplete: onInit, css: { 'survey-question__content--initializing': initializing() }">
    <!-- ko let: { pollName: ko.observable(POLL_NAME) } -->

    <!-- ko let: { onChangePollName: function(newName) { pollName(newName); } } -->
    <?= $this->render('../foquz-poll/_poll_header', [
        'model' => $poll,
        'page' => 'questions'
    ]) ?>
    <!-- /ko -->

    <?= $this->render('../foquz-poll/_menu_quests', [
        'model' => $poll,
        'page' => 'questions'
    ]) ?>

    <?php if (!$hasQuestions) : ?>
        <div class="f-card f-card--shadow f-card--lg f-card--min-height">
            <div class="f-card__inner align-items-center justify-content-center">
                <div class="f-color-text">
                    <?php $link = Url::to(['/foquz/foquz-poll/launch', 'id' => $poll->id, 'tab' => 'points']) ?>
                    <?= \Yii::t('questions', 'Ни одной точки контакта не добавлено в опрос. Перейти к {before}настройке точек контакта{after}', [
                        'before' => '<a href="' . $link . '">',
                        'after' => '</a>'
                    ]) ?>
                </div>
            </div>
        <?php else : ?>
            <div class="d-flex justify-content-between app__inner-content align-items-start">
                <div class="mr-4">
                    <question-form-paginator params="
                            poll: POLL,
                            questions: questions,
                            activeQuestion: activeQuestion,
                            activeQuestionId: currentQuestionId,
                            randomOrder: POLL.displaySetting && POLL.displaySetting.random_order,
                            isAuto: POLL_IS_AUTO,
                            hasLogic: hasLogic,
                            hasEndLogic: hasEndLogic,
                            activateQuestion: function(id) {
                                onActivateQuestion(id);
                            },
                            blinked: blinked,
                            pagesMode: pagesMode,
                            pages: pages,
                            isSortableEnabled: $root.currentQuestionRecipients().length === 0,
                            rootResortQuestions: resortQuestions,
                            isSaved: $root.isSaved,
                            openUnsavedChangesModal: $root.openUnsavedChangesModal.bind($root),
                        ">
                        <!-- ko ifnot: POLL_IS_AUTO || isBlocked -->



                        <!-- ko if: pagesMode && pages.length > 1 -->
                        <fc-button params="shape: 'circle', color: 'primary', icon: 'plus', size: 'sm'" id="add-question-button"></fc-button>

                        <fc-popper params="target: 'add-question-button'">
                            <div class="fc-dropdown-list">
                                <!-- ko foreach: $root.pages -->
                                <a class="fc-dropdown-list__item" href="javascript:void(0)" data-bind="click: function () {
                                $root.onAddQuestion(id);
                            }, text: name() || $root.translator.t('Название страницы {number}', {
                                number: $index() + 1
                            })">

                                </a>
                                <!-- /ko -->
                            </div>
                        </fc-popper>
                        <!-- /ko -->

                        <!-- ko if: !pagesMode || pages.length <= 1 -->
                        <!-- ko if: !window.CURRENT_USER.watcher -->
                        <fc-button params="shape: 'circle',
                            size: 'sm',
                            color: 'primary',
                            icon: { name: 'plus', size: 12 },
                            click: function () { 
                                onAddQuestion(); 
                            },
                            disabled: addQuestionButtonDisabled" id="add-question-button" data-bind="fbTooltip: $root.translator.t('Добавить вопрос')">
                        </fc-button>
                        <!-- /ko -->
                        <!-- /ko -->
                        <!-- /ko -->
                    </question-form-paginator>
                </div>


                <div class="d-flex align-items-center flex-shrink-0">

                    <!-- ko if: useLangs -->
                    <fc-button params="
                            icon: { name: 'translate', size: 20 },
                            shape: 'square',
                            
                            click: function() {
                                $root.configLangs();
                            }" data-bind="fbPopper, title: 'Перевод вопросов на другие языки'"></fc-button>
                    <!-- /ko -->

                    <div class="px-10p py-2 my-n2" data-bind="tooltip, tooltipText: logicButtonTooltipText">
                        <a data-bind="attr: { href: '<?= $logicLink ?>' }, " class="f-btn f-btn--square p-0 flex-shrink-0">
                            <svg-icon params="name: 'logic'" class="f-color-service svg-icon--lg"></svg-icon>

                            <!-- ko if: hasAnyLogic -->
                            <div class="f-badge f-badge--success">
                                <svg-icon params="name: 'check-bold'" class="f-color-white svg-icon--xs"></svg-icon>
                            </div>
                            <!-- /ko -->
                        </a>
                    </div>


                    <?php if (!$poll->is_auto || $poll->point_system) : ?>
                        <!-- ko ifnot: isBlocked -->
                        <!-- ko if: !window.CURRENT_USER.watcher -->
                        <fc-button params="shape: 'square',
                                icon: 'ellipsis'" id="poll-actions-button"></fc-button>
                        <!-- /ko -->
                        <fc-popper params="target: 'poll-actions-button',
                                        options: {
                                            placement: 'bottom-start'
                                        }">
                            <div class="fc-dropdown-list">

                                <?php if (!$poll->is_auto) : ?>
                                    <a href="javascript:void(0)" data-bind="click: function() { copyQuestions() }" class="fc-dropdown-list__item">
                                        <fc-icon params="name: 'poll-copy', color: 'secondary'" class="mr-3"></fc-icon>
                                        <?= \Yii::t('questions', 'Копировать вопросы<br>из других опросов') ?>
                                    </a>
                                <?php endif; ?>

                                <?php if ($poll->point_system) : ?>
                                    <a href="javascript:void(0)" data-bind="click: function() { configPoints() }" class="fc-dropdown-list__item">
                                        <fc-icon params="name: 'poll-points', color: 'secondary'" class="mr-3"></fc-icon>
                                        <?= \Yii::t('questions', 'Интерпретация баллов') ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </fc-popper>
                        <!-- /ko -->
                    <?php endif; ?>
                </div>

            </div>



            <div class="survey-question__body">
                <div class="f-card__grow">
                    <div class="d-flex gap-4 align-items-start">
                        <div class="pt-20p flex-grow-1 f-card f-card--shadow f-card--lg f-card--min-height mr-20p" style="max-width: calc(100% - 390px)">
                            <div class=" position-relative pt-1 px-20p">
                                <div class="d-flex align-items-center pb-1 position-relative">
                                    <?= $this->render('parts/logic-badge.php') ?>

                                    <?= $this->render('parts/view-logic-badge.php') ?>
                                    <?= $this->render('parts/donor-badge.php') ?>
                                    <!-- ko if: controller.question().usesDictionaryElements() || controller.question().dictionaryElementId -->
                                    <div
                                        class="logic-badge mr-3 pb-20p"
                                        data-bind="
                                            tooltip,
                                            tooltipText: controller.question().dictionaryBadgeTooltipText,
                                        ">
                                        <svg-icon params="name: 'dictionary-connection', color: 'secondary'" class="svg-icon--lg"></svg-icon>
                                        <div class="logic-badge__cover" ></div>
                                    </div>
                                    <!-- /ko -->



                                    <!-- ko ifnot: $root.isLoading -->
                                    <!-- ko if: !$root.hasEndLogic() && $root.hasMultipleEndScreens() -->
                                    <div class="d-flex align-items-center  mb-20p">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="f-exclamation-mark"></div>
                                        </div>
                                        <div class="flex-grow-1 f-color-service f-fs-1 pr-4">
                                            <?= \Yii::t('questions', 'Если логика вопросов не настроена, при прохождении первый в списке вопросов конечный экран будет заканчивать опрос') ?>


                                        </div>
                                    </div>
                                    <!-- /ko -->
                                    <!-- /ko -->
                                </div>



                                <!-- ko ifnot: $root.isLoading -->
                                <div class="">

                        <?php if (false): // убрано в рамках задачи #4484?>
                                    <!-- ko ifnot: POLL_IS_AUTO -->
                                    <div class="form-group">
                                        <radio-group class="poll-question-form__source" params="options: [
                                        { value: 'common', label: $root.translator.t('Вопрос') },
                                        { value: 'point', label: $root.translator.t('Точка контакта') },
                                    ], value: questionSource, disabled: !isNew()" data-bind="style: {
                                        opacity: isNew() ? 1 : 0.5
                                    }"></radio-group>


                                        <!-- ko if: questionSource() == 'point' && collections.contactPoints.loaded() && !collections.contactPoints.list().length -->
                                        <div class="f-color-service f-fs-1 mt-2">
                                            <?= \Yii::t('questions', 'Для создания вопроса, связанного с точкой контакта, необходимо добавить хотя бы один шаблон в раздел {section}', [
                                                'section' => '<a href="/foquz/contact-points" target="_blank">' . \Yii::t('questions', 'Точки контакта') . '</a>'
                                            ]) ?>

                                        </div>
                                        <!-- /ko -->


                                    </div>
                                    <!-- /ko -->



                                    <!-- ko if: questionSource() == 'point' -->
                                    <!-- ko ifnot: collections.contactPoints.loaded -->
                                    <spinner></spinner>
                                    <!-- /ko -->

                                    <!-- ko template: {
                                  foreach: templateIf(collections.contactPoints.loaded() && collections.contactPoints.list().length, $data),
                                  afterAdd: fadeAfterAddFactory(400, 200),
                                  beforeRemove: fadeBeforeRemoveFactory(100)
                                } -->
                                    <div class="form-group">
                                        <label class="form-label"><?= \Yii::t('questions', 'Точка контакта') ?></label>



                                        <collection-select params="
                                                collection: collections.contactPoints,
                                                value: questionSourceId,
                                                placeholder: '<?= \Yii::t('questions', 'Выберите точку контакта') ?>',
                                                search: true,
                                                disabled: !isNew()" data-bind="event: {
                                                click: function() {
                                                    if (!isNew()) $root.tryChangePointParam()
                                                }}"></collection-select>

                                        <!-- ko if: questionSource() == 'point' && !isNew() && questionSourceId() -->
                                        <div class="f-color-service f-fs-1 mt-2">
                                            <?= \Yii::t('questions', 'Вопрос связан с точкой контакта {point}.', [
                                                'point' => '<span class="bold" data-bind="text: questionSourceName"></span>'
                                            ]) ?>

                                            <?php if (!$isEditor) : ?>
                                                <?= \Yii::t('questions', 'Перейти к {before}настройкам{after} точки контакта', [
                                                    'before' => '<a class="bold" data-bind="attr: {
                                                    href: \'/foquz/contact-points?nameFilter=\' + questionSourceName()
                                                }" target="_blank">',
                                                    'after' => '</a>'
                                                ]) ?>
                                            <?php endif; ?>


                                        </div>
                                        <!-- /ko -->
                                    </div>
                                    <!-- /ko -->

                                    <!-- /ko -->
                        <?php endif;?>
                                    <!-- ko if: questionSource() === 'point' && contactPointLoading() -->
                                    <spinner></spinner>
                                    <!-- /ko -->

                                    <!-- ko template: {
                                  foreach: templateIf(hasVisibleQuestion(), $data),
                                  afterAdd: fadeAfterAddFactory(400, 200),
                                  beforeRemove: fadeBeforeRemoveFactory(100)
                                } -->
                                    <question-form params="controller: controller,
                                    isAuto: POLL_IS_AUTO,"></question-form>
                                    <!-- /ko -->



                                </div>
                                <!-- /ko -->
                                <!-- ko if: $root.isLoading -->
                                <div class="pages-loader survey-question__question-loader" title="<?= \Yii::t('questions', 'Пожалуйста, подождите...') ?>">
                                    <i class="fa fa-spinner fa-pulse fa-2x fa-fw"></i>
                                </div>
                                <!-- /ko -->
                            </div>

                            <!-- ko ifnot: $root.isLoading && window.CURRENT_USER.watcher -->
                            <div class="f-card__footer" data-bind="stickyFooter">
                                <div class="survey-question__card-action-list justify-content-between flex-wrap">
                                    <div class="my-1">
                                        <!-- ko if: $root.pollIsAuto -->
                                        <button type="button" class="f-btn f-btn-danger" data-bind="click: function () { $root.delete(); }">
                                            <span class="f-btn-prepend">
                                                <svg-icon params="name: 'times'" class="svg-icon--sm"></svg-icon>
                                            </span>
                                            <?= \Yii::t('questions', 'Удалить') ?>
                                        </button>
                                        <!-- /ko -->

                                        <!-- ko ifnot: $root.pollIsAuto -->
                                        <!-- ko if: $root.hasVisibleQuestion -->
                                        <button class="f-btn" type="button" data-bind="dropdown, dropdownMode: 'button'" aria-expanded="false">
                                            <?= \Yii::t('questions', 'Действия') ?>
                                            <span class="f-btn-append-section">
                                                <foquz-icon params="icon: 'arrow-bottom'"></foquz-icon>
                                            </span>

                                            <template id="foquz-dropdown-template-1">
                                                <div class="tippy-list">
                                                    <a class="tippy-list__item" href="javascript:void(0)" data-bind="click: function () {
                                                            if ($root.copyDisable()) return;
                                                            $root.copyQuestion();
                                                        }, css: {
                                                            'disabled': copyDisable
                                                        }">
                                                        <?= \Yii::t('questions', 'Копировать') ?>
                                                    </a>
                                                    <a class="tippy-list__item" href="javascript:void(0)" data-bind="click: function () { $root.delete(); }">
                                                        <?= \Yii::t('questions', 'Удалить') ?>
                                                    </a>
                                                </div>
                                            </template>
                                        </button>
                                        <!-- /ko -->
                                        <!-- /ko -->
                                    </div>

                                    <div class="my-1">
                                        <button type="button" class="f-btn" data-bind="click: function () { $root.cancel(); }">
                                            <span class="f-btn-prepend">
                                                <svg-icon params="name: 'bin'"></svg-icon>
                                            </span>
                                            <?= \Yii::t('questions', 'Отменить') ?>
                                        </button>

                                <?php if (false): // убрано в рамках задачи #4484?>
                                        <?php if (!$isEditor) : ?>
                                            <!-- ko if: !activeQuestion().pointId() && !isNew() && (!window.POLL || !window.POLL.dictionary_id) -->
                                            <button
                                                type="button"
                                                class="f-btn f-btn-success"
                                                data-bind="
                                                    click: function () {
                                                        $root.saveAsContactPoint();
                                                    },
                                                    disable: $root.isSubmitted() && !$root.controller.isValid() || !$root.hasVisibleQuestion(),
                                                "
                                            >
                                                <span class="f-btn-prepend">
                                                    <svg-icon params="name: 'save'"></svg-icon>
                                                </span>
                                                <?= \Yii::t('questions', 'Сохранить как точку контакта') ?>
                                            </button>
                                            <!-- /ko -->
                                        <?php endif; ?>
                                <?php endif; ?>

                                        <button type="button" class="f-btn f-btn-success" data-bind="
                                                        click: function () { 
                                                            $root.save(); },
                                                        disable: $root.isSubmitted() && !$root.controller.isValid() || !$root.hasVisibleQuestion()
                                                    ">
                                            <span class="f-btn-prepend">
                                                <svg-icon params="name: 'save'"></svg-icon>
                                            </span>
                                            <?= \Yii::t('questions', 'Сохранить') ?>
                                        </button>
                                    </div>
                                </div>


                                <success-message params="show: showSuccessMessage"></success-message>
                                <success-message class="foquz-success-message--error" params="show: errorMessage, delay: 3000, text: errorMessage"></success-message>

                            </div>
                            <!-- /ko -->
                        </div>
                        <div class="pt-15p pr-5p pl-5p flex-shrink-0 f-card f-card--shadow f-card--lg f-card--min-height poll-preview-wrapper" style="width: 370px">
                            <poll-preview params="onChange: function() { $root.changeActiveIframe() },
                            mode: 'design',
                            onOpen: function() { $root.onOpenView();  },
                            isAuto: <?= $poll->is_auto ? 1 : 0 ?>,
                            url: '<?= $isOldPreview ? $oldPreviewUrl : $previewUrl ?>',
                            tradeUrl: '<?= $tradePreviewUrl ?>',
                            title: pollName,
                            isVuePreview: <?= $isOldPreview ? 0 : 1 ?>,
                            subtitle: 'Вопросы'" data-bind="fade: hasVisibleQuestion()"></poll-preview>
                        </div>
                    </div>
                </div>
            </div>


            <!-- ko component: { name: 'modal-container', params: { opens: modalOpens } } -->
            <!-- /ko -->

            <foquz-modals-container params="modals: modals"></foquz-modals-container>



            <script type="text/html" id="survey-question-change-modal-content-template">
                <div data-bind="component: { name: 'survey-question-change-modal-dialog', params: { data, modalElement, cancel: function () { close(); }, submit: function (data) { close(data); } } }" role="document"></div>
            </script>

            <script type="text/html" id="survey-question-leave-unsaved-template">
                <div data-bind="component: { name: 'survey-question-leave-modal-dialog', params: { data, modalElement, cancel: function () { close(); }, submit: function (data) { close(data); } } }" role="document"></div>
            </script>

            <script type="text/html" id="survey-question-unsaved-changes-modal-content-template">
                <div data-bind="component: { name: 'survey-question-unsaved-changes-modal-dialog', params: { data, modalElement, cancel: function () { close(); }, submit: function () { close(true); } } }" role="document"></div>
            </script>


        <?php endif; ?>
        <!-- /ko -->

        </div>




        <template id="survey-question-unsaved-changes-modal-dialog-template">
            <!-- ko template: { afterRender: onInit } -->
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title"><?= \Yii::t('questions', 'Данные не сохранены') ?></h2>

                    <button type="button" class="close" aria-label="Close" data-bind="click: function() { cancel(); }">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <?= \Yii::t('questions', 'Данные в текущем вопросе не сохранены и будут потеряны без возможности восстановления') ?>
                </div>

                <div class="modal-footer">
                    <div class="modal-actions">
                        <button type="button" class="btn btn-link" data-bind="click: function() { cancel(); }">
                            <?= \Yii::t('questions', 'Отменить') ?>
                        </button>

                        <button type="submit" class="btn btn-default" data-bind="click: function() { submit(); }">
                            <?= \Yii::t('questions', 'Перейти') ?>
                        </button>
                    </div>
                </div>
            </div>
            <!-- /ko -->
        </template>


        <template id="survey-question-leave-modal-dialog-template">
            <!-- ko template: { afterRender: onInit } -->
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title"><?= \Yii::t('questions', 'Данные не сохранены') ?></h2>

                    <button type="button" class="close" aria-label="Close" data-bind="click: function() { cancel(); }">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <?= \Yii::t('questions', 'Данные в текущем вопросе не сохранены и будут потеряны без возможности восстановления') ?>
                </div>

                <div class="modal-footer">
                    <div class="modal-actions">
                        <button type="button" class="btn btn-link" data-bind="click: function() { cancel(); }">
                            <?= \Yii::t('questions', 'Отменить') ?>
                        </button>

                        <button type="submit" class="btn btn-default" data-bind="click: function() { submit(true); }">
                            <?= \Yii::t('questions', 'Перейти') ?>
                        </button>
                    </div>
                </div>
            </div>
            <!-- /ko -->
        </template>




        <input type="hidden" class="main-form-state" value="0">
        <input type="hidden" class="main-form-id" value="">
        <input type="hidden" class="main-form-link" value="">

        <!-- При наличии превью на странице, кнопка "Вверх" должна быть выше всех элементов -->
        <style>
            .upward-button-container {
                z-index: 9999;
            }
        </style>