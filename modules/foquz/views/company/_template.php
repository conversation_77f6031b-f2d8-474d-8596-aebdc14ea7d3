<div class="company__content company__content--initializing" data-bind="childrenComplete: onInit, css: { 'company__content--initializing': initializing() }">
  <div class="page-header">
    <div class="breadcrumbs page-header__breadcrumbs">
      <span class="breadcrumbs__item" data-bind="text: mode == 'create' ? 'Новая компания' : formModel().companyName"></span>
    </div>
  </div>

  <div class="f-card f-card--min-height f-card--lg f-card--shadow mt-4">
    <div class="f-tabs">
      <nav class="nav f-tabs__nav">
        <a class="text-center nav-item f-tabs__nav-item nav-link active" id="nav-common-tab" data-toggle="tab" href="#nav-common" role="tab" aria-controls="nav-common">
          Общие настройки
        </a>

        <a class="text-center nav-item nav-link f-tabs__nav-item" id="nav-service-accesses-tab" data-bind="css: {'disabled': mode == 'create'}" data-toggle="tab" href="#service-accesses" role="tab" aria-controls="service-accesses">
          Доступы к сервисам
        </a>

        <a class="text-center nav-item nav-link f-tabs__nav-item" id="nav-tarif-tab" data-bind="css: {'disabled': mode == 'create'}" data-toggle="tab" href="#nav-tarif" role="tab" aria-controls="nav-tarif">
          Тариф
        </a>

        <a class="text-center nav-item nav-link f-tabs__nav-item" id="nav-payment-tab" data-bind="css: {'disabled': mode == 'create'}, event: {
          'shown.bs.tab': function() {
            paymentModel.reset();
            return true;
          }
        }" data-toggle="tab" href="#nav-payment" role="tab" aria-controls="nav-payment">
          Оплата
        </a>

        <a class="text-center nav-item nav-link f-tabs__nav-item" id="nav-affiliate-tab" data-bind="css: {'disabled': mode == 'create'}" data-toggle="tab" href="#nav-affiliate" role="tab" aria-controls="nav-affiliate">
          Партнёрская программа
        </a>
      </nav>

      <div class="f-tabs__content">
        <div class="f-tab tab-pane show active pt-0" id="nav-common" role="tabpanel" aria-labelledby="nav-common-tab" data-bind="using: formModel">

          <div class="f-card__section f-card__grow">
            <div class="company__section">
              <div class="row">
                <div class="col-6">
                  <div class="form-group">
                    <label class="form-label" for="name">Название</label>

                    <button class="btn-question" data-bind="fbPopper, title: 'Название'" type="button">
                    </button>

                    <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: name().length">
                      <input class="form-control" data-bind="
                                   textInput: name,
                                   css: {
                                       'is-invalid': $root.formControlErrorStateMatcher(name),
                                       'is-valid': $root.formControlSuccessStateMatcher(name)
                                   }
                               " id="name" maxlength="50">

                      <div class="chars-counter__value"></div>
                    </div>

                    <!-- ko if: $root.formControlErrorStateMatcher(name) -->
                    <div class="form-error" data-bind="text: name.error()"></div>
                    <!-- /ko -->
                  </div>
                </div>
                <div class="col-6">
                  <div class="form-group">
                    <label class="form-label" for="domain">Домен</label>

                    <button class="btn-question" data-bind="fbPopper, title: 'Идентификатор компании может содержать только латинские буквы, цифры и дефис. Дефис нельзя использовать в начале и конце.'" type="button" >
                    </button>

                    <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: domain().length">
                      <input class="form-control" data-bind="
                                   textInput: domain,
                                   css: {
                                       'is-invalid': $root.formControlErrorStateMatcher(domain),
                                       'is-valid': $root.formControlSuccessStateMatcher(domain)
                                   }
                               " id="domain" maxlength="50">

                      <div class="chars-counter__value"></div>
                    </div>

                    <!-- ko if: $root.formControlErrorStateMatcher(domain) -->
                    <div class="form-error" data-bind="text: domain.error()"></div>
                    <!-- /ko -->
                  </div>
                </div>
              </div>
              <div class="row mt-25p mb-n5p">
                <div class="col-12">
                  <div class="form-group sg-form-group switch-form-group">
                    <switch
                      class="mb-0"
                      params="checked: isCreateMailings"
                    >
                      <span>Модуль рассылок</span>
                    </switch>
                  </div>
                </div>
              </div>
              <div class="row mt-25p mb-n5p">
                <div class="col-12">
                  <div class="form-group sg-form-group switch-form-group">
                    <switch
                      class="mb-0"
                      params="checked: isCreateAutoMailings"
                    >
                      <span>Создание автоматических опросов/рассылок</span>
                    </switch>
                  </div>
                </div>
              </div>
              <div class="row mt-25p mb-n5p">
                <div class="col-12">
                  <div class="form-group sg-form-group switch-form-group">
                    <switch
                      class="mb-0"
                      params="
                        checked: isContactsEnabled() || !isBaseTariff(),
                        disabled: !isBaseTariff(),
                        onChange: (val) => isContactsEnabled(val)
                      "
                    >
                      <span>Доступ к разделу «Контакты»</span>
                    </switch>
                  </div>
                </div>
              </div>
            </div>

            <div class="company__section">
              <span class="company__section-name">Пользователь</span>

              <div class="row">
                <div class="col-3">
                  <div class="form-group">
                    <label class="form-label" for="login">Логин</label>

                    <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Логин">
                    </button>

                    <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: login().length">
                      <input class="form-control" data-bind="
                                           textInput: login,
                                           css: {
                                               'is-invalid': $root.formControlErrorStateMatcher(login),
                                               'is-valid': $root.formControlSuccessStateMatcher(login)
                                           }
                                       " id="login" name="new-login" maxlength="50" autocomplete="new-login">

                      <div class="chars-counter__value"></div>
                    </div>

                    <!-- ko if: $root.formControlErrorStateMatcher(login) -->
                    <div class="form-error" data-bind="text: login.error()"></div>
                    <!-- /ko -->
                  </div>
                </div>
                <div class="col-3">
                  <div class="form-group">
                    <label class="form-label" for="password">Пароль</label>

                    <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Пароль">
                    </button>

                    <!-- ko let: { isPasswordHidden: ko.observable(true), tooltip: ko.observable(null) } -->
                    <div class="input-group inner-input-group password-inner-input-group">
                      <input class="form-control" data-bind="
                                               textInput: password,
                                               css: {
                                                   'is-invalid': $root.formControlErrorStateMatcher(password),
                                                   'is-valid': $root.formControlSuccessStateMatcher(password)
                                               },
                                               attr: { type: isPasswordHidden() ? 'password' : 'text' }
                                           " id="password" name="new-password" maxlength="50" autocomplete="new-password">
                      <button class="btn btn-icon btn-icon--simple inner-input-group__suffix password-inner-input-group__visibility-toggle-icon-button-suffix" data-bind="
                                                click: function () { isPasswordHidden(!isPasswordHidden()); tooltip().updateTitle(); },
                                                css: {
                                                    'password-inner-input-group__show-password-icon-button-suffix': isPasswordHidden,
                                                    'password-inner-input-group__hide-password-icon-button-suffix': !isPasswordHidden()
                                                },
                                                attr: { title: isPasswordHidden() ? 'Показать пароль' : 'Скрыть пароль' },
                                                tooltip: tooltip
                                            ">
                      </button>
                    </div>
                    <!-- /ko -->

                    <!-- ko if: $root.formControlErrorStateMatcher(password) -->
                    <div class="form-error" data-bind="text: password.error()"></div>
                    <!-- /ko -->
                  </div>
                </div>
                <div class="col-3">
                  <div class="form-group">
                    <label class="form-label" for="password-check">Повторите пароль</label>

                    <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Повторите пароль">
                    </button>

                    <!-- ko let: { isPasswordHidden: ko.observable(true), tooltip: ko.observable(null) } -->
                    <div class="input-group inner-input-group password-inner-input-group">
                      <input class="form-control" data-bind="
                                                   textInput: passwordCheck,
                                                   css: {
                                                       'is-invalid': $root.formControlErrorStateMatcher(passwordCheck),
                                                       'is-valid': $root.formControlSuccessStateMatcher(passwordCheck)
                                                   },
                                                   attr: { type: isPasswordHidden() ? 'password' : 'text' }
                                               " id="password-check" maxlength="50">
                      <button class="btn btn-icon btn-icon--simple inner-input-group__suffix password-inner-input-group__visibility-toggle-icon-button-suffix" data-bind="
                                                click: function () { isPasswordHidden(!isPasswordHidden()); tooltip().updateTitle(); },
                                                css: {
                                                    'password-inner-input-group__show-password-icon-button-suffix': isPasswordHidden,
                                                    'password-inner-input-group__hide-password-icon-button-suffix': !isPasswordHidden()
                                                },
                                                attr: { title: isPasswordHidden() ? 'Показать пароль' : 'Скрыть пароль' },
                                                tooltip: tooltip
                                            ">
                      </button>
                    </div>
                    <!-- /ko -->

                    <!-- ko if: $root.formControlErrorStateMatcher(passwordCheck) -->
                    <div class="form-error" data-bind="text: passwordCheck.error()"></div>
                    <!-- /ko -->
                  </div>
                </div>
                <div class="col-3">
                  <div class="form-group">
                    <div class="form-group-header">
                      <label class="form-label" for="email">Email</label>

                      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Email">
                      </button>

                      <span class="spacer"></span>

                      <span class="form-group-note">необязательное</span>
                    </div>

                    <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: email().length">
                      <input
                        class="form-control"
                        data-bind="
                          textInput: email,
                          css: {
                            'is-invalid': email.error(),
                            'is-valid': !email.error(),
                          },
                        "
                        id="email"
                        maxlength="50"
                      />
                      <div class="chars-counter__value"></div>
                    </div>

                    <!-- ko if: email.error() -->
                    <div class="form-error" data-bind="text: email.error()"></div>
                    <!-- /ko -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="f-card__footer" data-bind="stickyFooter">
            <div class="company__actions">

              <a class="f-btn f-btn-text f-btn-xl" href="/foquz/company">
                <span class="f-btn-prepend">
                  <span class="f-icon f-icon--back">
                    <svg>
                      <use href="#back-icon"></use>
                    </svg>
                  </span>
                </span>
                Назад
              </a>

              <div class="spacer"></div>

              <button type="button" class="f-btn" data-bind="click: function () { $root.cancel(); }">
                <span class="f-btn-prepend">
                  <svg-icon params="name: 'bin'"></svg-icon>
                </span>
                Отменить
              </button>

              <button type="submit" class="f-btn f-btn-success" data-bind="click: function () { $root.submit(); }, disable: $root.pending, css: {
                'f-btn--loading': $root.pending
              }">
                <span class="f-btn-prepend">
                  <svg-icon params="name: 'save'"></svg-icon>
                </span>
                Сохранить
              </button>
            </div>
          </div>
        </div>

        <div class="f-tab tab-pane company__service-accesses-tab-pane" id="service-accesses" role="tabpanel" aria-labelledby="service-accesses-tab" data-bind="using: formModel">
          <div class="f-card__section f-card__grow">
            <div class="company__service-accesses-tab-pane-header">
              <button class="btn button-add company__service-accesses-tab-pane-add-button" type="button" data-bind="click: function () { $root.openCreateServiceAccessModal(); }">
                Добавить сервис
              </button>
            </div>

            <div class="">
              <div class="foq-table__section">
                <table class="table foq-table foq-table--filterable company__service-accesses-tab-pane-table">
                  <thead>
                    <tr>
                      <th class="company__service-accesses-tab-pane-table-connected-at-head-cell">
                        <div class="foq-table__head-cell-title">
                          <div class="foq-table__head-cell-name sort_column" type-sort="asc" attr-sort="date_connect">
                            Подключен
                          </div>

                          <i class="foq-table__sorting-icon foq-table__sorting-icon--order_desc"></i>

                          <!-- ko if: $root.connectedAtStringFilter() !== '' -->
                          <i class="foq-table__filtering-icon"></i>
                          <!-- /ko -->
                        </div>
                        <input class="foq-table__head-cell-filter" attr-name="serviceConnect" data-bind="textInput: $root.connectedAtStringFilter">
                      </th>
                      <th>
                        <div class="foq-table__head-cell-title">
                          <div class="foq-table__head-cell-name sort_column" type-sort="asc" attr-sort="service">
                            Сервис
                          </div>


                          <!-- ko if: $root.serviceStringFilter() !== '' -->
                          <i class="foq-table__filtering-icon"></i>
                          <!-- /ko -->
                        </div>
                        <input class="foq-table__head-cell-filter" attr-name="serviceTypeName" data-bind="textInput: $root.serviceStringFilter">
                      </th>
                      <th>
                        <div class="foq-table__head-cell-title">
                          <div class="foq-table__head-cell-name sort_column" type-sort="asc" attr-sort="name">
                            Название
                          </div>
                          <!-- ko if: $root.nameStringFilter() !== '' -->
                          <i class="foq-table__filtering-icon"></i>
                          <!-- /ko -->
                        </div>
                        <input class="foq-table__head-cell-filter" attr-name="serviceName" data-bind="textInput: $root.nameStringFilter">
                      </th>
                      <th class="company__service-accesses-tab-pane-table-active-head-cell">
                        <div class="foq-table__head-cell-title">
                          <div class="foq-table__head-cell-name sort_column" type-sort="asc" attr-sort="is_active">
                            Активность
                          </div>
                          <!-- ko if: $root.activeStringFilter() !== '' -->
                          <i class="foq-table__filtering-icon"></i>
                          <!-- /ko -->
                        </div>
                        <input class="foq-table__head-cell-filter" attr-name="serviceActive" data-bind="textInput: $root.activeStringFilter">
                      </th>
                      <th class="company__service-accesses-tab-pane-table-actions-head-cell"></th>
                    </tr>
                  </thead>

                  <tbody>
                    <!-- ko foreach: { data: serviceAccesses, beforeRemove: $root.beforeServiceRemove, afterAdd: $root.afterServiceAdd } -->
                    <tr class="foq-table__row company__service-accesses-tab-pane-table-row" data-bind="css: { 'company__service-accesses-tab-pane-table-row--inactive': !active() }">
                      <td data-bind="text: connectedAt"></td>
                      <td class="company__service-accesses-tab-pane-table-service-cell" data-bind="text: $root.getServiceName(service())">
                      </td>
                      <td data-bind="text: name"></td>
                      <td class="company__service-accesses-tab-pane-table-active-cell">
                        <div class="form-check company__service-accesses-tab-pane-table-active-checkbox">
                          <input type="checkbox" class="form-check-input update_active" data-bind="
                                                     checked: active,
                                                     attr: { id: 'company-service-accesses-tab-pane-table-active-checkbox-' + $index(), idService: id }
                                                 ">
                          <label class="form-check-label" data-bind="attr: { for: 'company-service-accesses-tab-pane-table-active-checkbox-' + $index() }"></label>
                        </div>
                      </td>
                      <td class="company__service-accesses-tab-pane-table-actions-cell">
                        <button class="btn btn-icon btn-icon--simple company__service-accesses-tab-pane-table-action-button company__service-accesses-tab-pane-table-edit-button" title="Редактировать" data-bind="click: function () { $root.openEditServiceAccessModal(id); }, tooltip">
                        </button>

                        <button class="btn btn-icon btn-icon--simple company__service-accesses-tab-pane-table-action-button company__service-accesses-tab-pane-table-delete-button" title="Удалить" data-bind="click: function () { $root.openDeleteServiceAccessModal(id); }, tooltip">
                        </button>
                      </td>
                    </tr>
                    <!-- /ko -->
                  </tbody>
                </table>
                <div id="reviews-loader" class="pages-loader" style="display: none;" title="Пожалуйста, подождите ...">
                  <i class="fa fa-spinner fa-pulse fa-2x fa-fw top"></i>
                </div>
              </div>
            </div>
          </div>

          <div class="f-card__footer">
            <div class="company__actions">
              <a class="f-btn f-btn-text f-btn-xl" href="/foquz/company">
                <span class="f-btn-prepend">
                  <span class="f-icon f-icon--back">
                    <svg>
                      <use href="#back-icon"></use>
                    </svg>
                  </span>
                </span>
                Назад
              </a>
            </div>
          </div>
        </div>

        <div class="f-tab tab-pane company__tarif-tab-pane pt-0" id="nav-tarif" role="tabpanel" aria-labelledby="nav-tarif-tab">
          <div class="f-card__divider"></div>
          <!-- ko if: tariffModel.isBlocked -->
          <div class="f-card__section f-card__divider f-fs-2-5">
            <!-- ko template: {
              name: 'tariff-blocked-message-template',
              data: {
                tariff: tariffModel
              }
            } -->
            <!-- /ko -->
          </div>
          <!-- /ko -->

          <div class="f-card__divider">
            <!-- ko template: {
              name: 'tariff-info-template',
              data: {
                tariff: tariffModel,
              }
            } -->
            <!-- /ko -->
          </div>
          <div class="f-card__section">
            <button class="f-btn" data-bind="click: function() {
              tariffModel.change();
            }">Изменить тариф</button>
          </div>

          <div class="f-card__section" data-bind="using: promocodesTable">
            <h2 class="f-h2">История бонусного счета</h2>

            <!-- ko if: $data.initing -->
            <fc-spinner class="mt-4 f-color-primary"></fc-spinner>
            <!-- /ko -->

            <!-- ko ifnot: $data.initing -->
            <interactive-table params="table: $data, notFoundText: 'Бонусы пока не использовались'">
              <table class="table f-table f-table-dense">
                <thead>
                  <tr>
                    <th>Промокод</th>
                    <th>Использован</th>
                    <th>Пользователь</th>
                    <th>Бонусы</th>
                    <th>Акция</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- ko foreach: items -->
                  <tr>
                    <td valign="middle" data-bind="text: promocode"></td>
                    <td valign="middle" data-bind="text: usedAt"></td>
                    <td valign="middle" class="py-2">
                      <fc-user params="name: userName, avatar: userAvatar"></fc-user>
                    </td>
                    <td valign="middle">
                      <span class="f-color-success font-weight-700" data-bind="text: '+' + bonuses.toLocaleString()"></span>
                    </td>
                    <td>
                      <!-- ko if: coupon -->
                      <a data-bind="text: coupon.promo_name, attr: {
                        href: '/foquz/coupons?search[promo_code]=' + promocode
                      }" target="_blank"></a>
                      <!-- /ko -->
                    </td>
                  </tr>
                  <!-- /ko -->
                </tbody>
              </table>
            </interactive-table>
            <!-- /ko -->
          </div>
        </div>

        <div class="f-tab tab-pane company__payment-tab-pane pt-0" id="nav-payment" role="tabpanel" aria-labelledby="nav-payment-tab">
          <?= $this->render('_payment.php') ?>
        </div>

        <div class="f-tab tab-pane company__affiliate-tab-pane pt-0" id="nav-affiliate" role="tabpanel" aria-labelledby="nav-affiliate-tab">

          <!-- ko ifnot: mode == 'create' -->
          <div class="f-card__section pt-30p">
            <!-- ko if: data.who_brought -->
            <div class="f-fs-2-5 mb-4">
              <span class="f-color-service">Кто привел: </span>
              <a data-bind="attr: {
                  href: '/foquz/company/update?id=' + data.who_brought.id,

                }, text: data.who_brought.name"></a>
            </div>
            <!-- /ko -->

            <h2 class="f-h2">Приведенные компании</h2>

            <affiliate-companies-table params="table: affiliateCompanies"></affiliate-companies-table>
          </div>
          <!-- /ko -->

        </div>
      </div>
    </div>
  </div>

  <!-- ko component: { name: 'modal-container', params: { opens: modalOpens } } -->
  <!-- /ko -->
  <dialogs-container params="ref: dialogs"></dialogs-container>

  <?= $this->render('../../../../ko/legacy/models/tariff/template.php') ?>

  <script type="text/html" id="company-delete-service-access-modal-content-template">
    <div data-bind="component: { name: 'delete-service-access-modal-dialog', params: { data, modalElement, cancel: function () { close(); }, submit: function () { close(true); } } }" role="document"></div>
  </script>


</div>

<template id="delete-service-access-modal-dialog-template">
  <!-- ko template: { afterRender: onInit } -->
  <div class="modal-content">
    <div class="modal-header">
      <h2 class="modal-title">Удаление сервиса</h2>

      <button type="button" class="close" aria-label="Close" data-bind="click: function() { cancel(); }">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>

    <div class="modal-body">
      Сервис будет удален без возможности восстановления
    </div>

    <div class="modal-footer">
      <div class="modal-actions">
        <button type="button" class="btn btn-link" data-bind="click: function() { cancel(); }">
          Отменить
        </button>

        <button type="submit" class="btn btn-danger" data-bind="click: function() { submit(); }">
          Удалить
        </button>
      </div>
    </div>
  </div>
  <!-- /ko -->
</template>
