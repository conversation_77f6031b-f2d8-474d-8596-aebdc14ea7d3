<?php


namespace app\commands;


use app\models\company\Company;
use app\models\Filial;
use app\models\User;
use app\modules\foquz\models\FoquzFile;
use app\modules\foquz\models\FoquzPollAnswerItemFile;
use app\modules\foquz\models\FoquzPollDesign;
use app\modules\foquz\models\FoquzPollDesignTemplate;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionEndScreenLogo;
use app\modules\foquz\models\FoquzQuestionFile;
use app\modules\foquz\models\FoquzQuestionFileLang;
use app\modules\foquz\models\FoquzQuestionSmile;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessingFile;
use app\modules\foquz\models\processing\ProcessingFile;
use app\modules\foquz\models\RecipientQuestionDetail;
use Yii;
use yii\console\Controller;
use yii\db\ActiveQuery;
use yii\helpers\ArrayHelper;
use yii\helpers\FileHelper;

class TransferController extends Controller
{
    private function copyFile(string $sourceFile, string $destinationFile, string $itemName, int $itemId): bool
    {
        if (!file_exists($sourceFile)) {
            print "{$itemName} ID {$itemId} not found at {$sourceFile}\n";
            return false;
        }

        if (!is_dir(dirname($destinationFile))) {
            FileHelper::createDirectory(dirname($destinationFile), 0777);
        }

        if (is_dir($sourceFile)) {
            print "Skipping directory {$sourceFile} for {$itemName} ID {$itemId}\n";
            return false;
        }

        copy($sourceFile, $destinationFile);
        print "Copied {$itemName} ID {$itemId} to {$destinationFile}\n";
        return true;
    }

    private function copyFilialLogos(int $companyID, string $sourceFolder, string $destinationFolder): array
    {
        print 'Start copying Filials logos' . PHP_EOL;

        $filials = Filial::find()
            ->select(['id', 'logo'])
            ->where(['company_id' => $companyID, 'is_active' => 1])
            ->andWhere(['NOT', ['OR', ['logo' => null], ['logo' => '']]])
            ->asArray()
            ->all();

        print 'Found ' . count($filials) . ' active filials with logos' . PHP_EOL;

        $copied = 0;
        $notFound = 0;
        foreach ($filials as $filial) {
            if (empty($filial['logo'])) {
                continue;
            }
            $sourceFile = $sourceFolder . 'logos/' . $filial['logo'];
            $destinationFile = $destinationFolder . 'logos/' . $filial['logo'];

            if ($this->copyFile($sourceFile, $destinationFile, 'Logo for Filial', $filial['id'])) {
                $copied++;
            } else {
                $notFound++;
            }
        }

        print "Copying completed: {$copied} logos copied, {$notFound} logos not found." . PHP_EOL;
        return [$copied, $notFound];
    }

    private function copyQuestionFiles(array $questionIDs, string $sourceFolder, string $destinationFolder): array
    {
        print 'Start copying Question Files' . PHP_EOL;

        $questionFiles = FoquzQuestionFile::find()
            ->select(['id', 'file_path'])
            ->where(['question_id' => $questionIDs])
            ->asArray()
            ->all();

        $copied = 0;
        $notFound = 0;
        foreach ($questionFiles as $questionFile) {
            $filePath = $questionFile['file_path'];
            if (empty($filePath)) {
                print "File path is empty for Question ID {$questionFile['id']}\n";
                continue;
            }

            $sourceFile = $sourceFolder . '../' . $filePath;
            $destinationFilePath = str_starts_with($filePath, 'uploads/') ? substr($filePath, 8) : $filePath;
            $destinationFile = $destinationFolder . $destinationFilePath;

            if ($this->copyFile($sourceFile, $destinationFile, 'Question file', $questionFile['id'])) {
                $copied++;
            } else {
                $notFound++;
            }
        }

        print "Copying completed: {$copied} Question files copied, {$notFound} Question files not found." . PHP_EOL;
        return $questionFiles;
    }

    private function copyEndScreenLogos(array $questionIDs, string $sourceFolder, string $destinationFolder): array
    {
        print 'Start copying End Screen Logos' . PHP_EOL;

        $endScreenLogos = FoquzQuestionEndScreenLogo::find()
            ->select(['id', 'logo'])
            ->where(['foquz_question_id' => $questionIDs])
            ->asArray()
            ->all();

        $copied = 0;
        $notFound = 0;
        foreach ($endScreenLogos as $logo) {
            if (empty($logo['logo'])) {
                print "Logo path is empty for End Screen Logo ID {$logo['id']}\n";
                continue;
            }

            $sourceFile = $sourceFolder . '..' . $logo['logo'];
            $destinationFilePath = str_starts_with($logo['logo'], '/uploads/') ? substr($logo['logo'], 9) : $logo['logo'];
            $destinationFile = $destinationFolder . $destinationFilePath;

            if ($this->copyFile($sourceFile, $destinationFile, 'End Screen Logo', $logo['id'])) {
                $copied++;
            } else {
                $notFound++;
            }
        }

        print "Copying completed: {$copied} End Screen Logos copied, {$notFound} End Screen Logos not found." . PHP_EOL;
        return $endScreenLogos;
    }

    private function copyLanguageFiles(array $files, string $sourceFolder, string $destinationFolder, bool $endScreen = false): void
    {
        if (!$endScreen) {
            $entityName = 'Question File';
            $columnName = 'foquz_question_file_id';
        } else {
            $entityName = 'End Screen Logo File';
            $columnName = 'end_screen_logo_id';
        }

        print 'Start copying Question Language Files for ' . $entityName . PHP_EOL;

        $questionLanguageFiles = FoquzQuestionFileLang::find()
            ->select(['id', 'file_path'])
            ->where([$columnName => ArrayHelper::getColumn($files, 'id')])
            ->asArray()
            ->all();

        $copied = 0;
        $notFound = 0;
        foreach ($questionLanguageFiles as $langFile) {
            $langFilePath = $langFile['file_path'];
            if (empty($langFilePath)) {
                print "Language file path is empty for Question File for {$entityName} Lang ID {$langFile['id']}\n";
                continue;
            }

            $sourceFile = $sourceFolder . '../' . $langFilePath;
            $destinationFilePath = str_starts_with($langFilePath, 'uploads/') ? substr($langFilePath, 8) : $langFilePath;
            $destinationFile = $destinationFolder . $destinationFilePath;

            if ($this->copyFile($sourceFile, $destinationFile, 'Question File Lang for ' . $entityName, $langFile['id'])) {
                $copied++;
            } else {
                $notFound++;
            }
        }

        print "Copying completed: {$copied} Question Language Files for {$entityName} copied, {$notFound} Question Language Files for {$entityName} not found." . PHP_EOL;
    }

    private function copyDesignImages(array $designs, string $sourceFolder, string $destinationFolder, string $entityName): void
    {
        print "Start copying {$entityName}s" . PHP_EOL;

        $imageFields = ['background_image', 'mobile_background_image', 'logo_image', 'cover_image'];
        $counters = array_fill_keys($imageFields, ['copied' => 0, 'notFound' => 0]);

        foreach ($designs as $design) {
            foreach ($imageFields as $field) {
                if (!empty($design[$field]) && !str_starts_with($design[$field], 'img') && !str_starts_with($design[$field], '/img')) {
                    $sourceFile = $sourceFolder . '..' . $design[$field];
                    $destinationFilePath = str_starts_with($design[$field], '/uploads/') ? substr($design[$field], 9) : $design[$field];
                    $destinationFile = $destinationFolder . $destinationFilePath;

                    if ($this->copyFile($sourceFile, $destinationFile, "{$entityName} {$field}", $design['id'])) {
                        $counters[$field]['copied']++;
                    } else {
                        $counters[$field]['notFound']++;
                    }
                }
            }
        }

        foreach ($imageFields as $field) {
            $fieldName = ucwords(str_replace('_', ' ', $field));
            print "Copying {$entityName} completed: {$counters[$field]['copied']} {$fieldName}s copied, {$counters[$field]['notFound']} {$fieldName}s not found." . PHP_EOL;
        }

    }

    private function copyUserAvatars(int $companyID, string $sourceFolder, string $destinationFolder): void
    {
        print 'Start copying User avatar files' . PHP_EOL;

        $userAvatars = User::find()
            ->select(['user.id', 'user.avatar'])
            ->joinWith('companyStaff', false)
            ->where(['company_staff.company_id' => $companyID])
            ->andWhere(['NOT', ['OR', ['avatar' => null], ['avatar' => '']]])
            ->asArray()
            ->all();

        $copied = 0;
        $notFound = 0;
        $previewCopied = 0;
        $previewNotFound = 0;
        foreach ($userAvatars as $user) {
            $sourceFile = $sourceFolder . 'avatars/' . $user['id'] . '/' . $user['avatar'];
            $destinationFile = $destinationFolder . 'avatars/' . $user['id'] . '/' . $user['avatar'];

            if ($this->copyFile($sourceFile, $destinationFile, 'User avatar', $user['id'])) {
                $copied++;
            } else {
                $notFound++;
            }

            $sourceFilePreview = $sourceFolder . 'avatars/' . $user['id'] . '/preview-' . $user['avatar'];
            $destinationFilePreview = $destinationFolder . 'avatars/' . $user['id'] . '/preview-' . $user['avatar'];

            if ($this->copyFile($sourceFilePreview, $destinationFilePreview, 'Preview User avatar', $user['id'])) {
                $previewCopied++;
            } else {
                $previewNotFound++;
            }
        }

        print "Copying completed: {$copied} avatars copied, {$notFound} avatars not found." . PHP_EOL;
        print "Preview files: {$previewCopied} copied, {$previewNotFound} not found." . PHP_EOL;
    }

    private function copyFilesWithPathField(array $items, string $sourceFolder, string $destinationFolder, string $entityName, string $pathField, ?callable $validator = null, string $pathPrefix = '../'): array
    {
        print "Start copying {$entityName}s" . PHP_EOL;

        $copied = 0;
        $notFound = 0;

        foreach ($items as $item) {
            $filePath = $item[$pathField];
            if (empty($filePath)) {
                print "{$pathField} is empty for {$entityName} ID {$item['id']}\n";
                continue;
            }

            if ($validator && !$validator($item)) {
                print "Skipping a non-custom smile for {$entityName} ID {$item['id']}\n";
                continue;
            }

            $sourceFile = $sourceFolder . $pathPrefix . $filePath;
            $destinationFilePath = str_starts_with($filePath, '/uploads/') ? substr($filePath, 9) : $filePath;
            $destinationFile = $destinationFolder . $destinationFilePath;

            if ($this->copyFile($sourceFile, $destinationFile, $entityName, $item['id'])) {
                $copied++;
            } else {
                $notFound++;
            }
        }

        print "Copying completed: {$copied} {$entityName}s copied, {$notFound} {$entityName}s not found." . PHP_EOL;
        return [$copied, $notFound];
    }

    private function copyFilesBatch(ActiveQuery $query, string $sourceFolder, string $destinationFolder, string $entityName, string $pathPrefix = '../'): array
    {
        print "Start copying {$entityName}s" . PHP_EOL;

        $copied = 0;
        $notFound = 0;
        $previewCopied = 0;
        $previewNotFound = 0;

        foreach ($query->batch(1000) as $files) {
            foreach ($files as $file) {
                $filePath = $file['file_path'];
                if (empty($filePath)) {
                    print "File path is empty for {$entityName} ID {$file['id']}\n";
                    continue;
                }

                $sourceFile = $sourceFolder . $pathPrefix . $filePath;
                $destinationFilePath = str_starts_with($filePath, '/uploads/') ? substr($filePath, 9) : $filePath;
                $destinationFile = $destinationFolder . $destinationFilePath;

                if ($this->copyFile($sourceFile, $destinationFile, $entityName, $file['id'])) {
                    $copied++;
                } else {
                    $notFound++;
                }

                $previewFilePath = $file['preview_path'] ?? $file['poster_url'] ?? $file['image_link'] ?? null;
                if (empty($previewFilePath)) {
                    continue;
                }

                $sourceFile = $sourceFolder . $pathPrefix . $previewFilePath;
                $destinationFilePath = str_starts_with($previewFilePath, '/uploads/') ? substr($previewFilePath, 9) : $previewFilePath;
                $destinationFile = $destinationFolder . $destinationFilePath;

                if ($this->copyFile($sourceFile, $destinationFile, 'Preview' . $entityName, $file['id'])) {
                    $previewCopied++;
                } else {
                    $previewNotFound++;
                }
            }
        }

        print "Copying {$entityName}s completed: {$copied} {$entityName}s copied, {$notFound} {$entityName}s not found." . PHP_EOL;
        print "Preview files: {$previewCopied} copied, {$previewNotFound} not found." . PHP_EOL;
        return [$copied, $notFound];
    }

    public function actionCopyFiles(int $companyID, string $destinationFolder): void
    {
        $sourceFolder = Yii::getAlias('@uploads/');
        if (!is_dir($sourceFolder)) {
            $this->stderr("Исходная папка {$sourceFolder} не существует.\n");
            return;
        }

        if (!str_ends_with($destinationFolder, '/')) {
            $destinationFolder .= '/';
        }

        if (!is_dir($destinationFolder) && !FileHelper::createDirectory($destinationFolder, 0777)) {
            $this->stderr("Не удалось создать папку назначения {$destinationFolder}.\n");
            return;
        }

        $company = Company::findOne($companyID);
        if (!$company) {
            $this->stderr("Компания с ID {$companyID} не найдена.\n");
            return;
        }

        print 'Start copying Files for Company ' . $company->alias . '(ID = ' . $company->id . ')' . PHP_EOL;

        $questionIDs = FoquzQuestion::find()
            ->select('foquz_question.id')
            ->joinWith('poll', false)
            ->where(['foquz_poll.company_id' => $companyID])
            ->column();

        $this->copyFilialLogos($companyID, $sourceFolder, $destinationFolder);

        $questionFiles = $this->copyQuestionFiles($questionIDs, $sourceFolder, $destinationFolder);

        $endScreenLogos = $this->copyEndScreenLogos($questionIDs, $sourceFolder, $destinationFolder);
        $this->copyLanguageFiles($questionFiles, $sourceFolder, $destinationFolder);
        $this->copyLanguageFiles($endScreenLogos, $sourceFolder, $destinationFolder, true);

        $questionSmiles = FoquzQuestionSmile::find()
            ->select(['id', 'smile_url'])
            ->where(['foquz_question_id' => $questionIDs])
            ->andWhere(['is not', 'smile_url', null])
            ->asArray()
            ->all();

        $this->copyFilesWithPathField($questionSmiles, $sourceFolder, $destinationFolder, 'Question Smile', 'smile_url', function($item) {
            return !empty($item['smile_url']) && str_starts_with($item['smile_url'], '/uploads');
        });

        $pollDesigns = FoquzPollDesign::find()
            ->select([
                'foquz_poll_design.id',
                'foquz_poll_design.background_image',
                'foquz_poll_design.mobile_background_image',
                'foquz_poll_design.logo_image',
                'foquz_poll_design.cover_image'
            ])
            ->joinWith('foquzPoll', false)
            ->where(['foquz_poll.company_id' => $companyID])
            ->asArray()
            ->all();

        $this->copyDesignImages($pollDesigns, $sourceFolder, $destinationFolder, 'Poll Design');

        $pollDesignTemplates = FoquzPollDesignTemplate::find()
            ->select(['id', 'background_image', 'mobile_background_image', 'logo_image', 'cover_image'])
            ->where(['company_id' => $companyID])
            ->asArray()
            ->all();

        $this->copyDesignImages($pollDesignTemplates, $sourceFolder, $destinationFolder, 'Poll Design Template');

        $answerItemFilesBatch = FoquzPollAnswerItemFile::find()
            ->select([
                'foquz_poll_answer_item_files.id',
                'foquz_poll_answer_item_files.file_path',
                'foquz_poll_answer_item_files.image_link'
            ])
            ->joinWith('foquzPollAnswerItem.foquzPollAnswer.foquzPoll', false)
            ->where(['foquz_poll.company_id' => $companyID])
            ->asArray();
        $this->copyFilesBatch($answerItemFilesBatch, $sourceFolder, $destinationFolder, 'Answer Item File', '..');


        $answerProcessFilesBatch = FoquzPollAnswerProcessingFile::find()
            ->select(['foquz_poll_answer_processings_files.id', 'foquz_poll_answer_processings_files.file_path'])
            ->joinWith('processing.foquzAnswer.foquzPoll', false)
            ->where(['foquz_poll.company_id' => $companyID])
            ->asArray();
        $this->copyFilesBatch($answerProcessFilesBatch, $sourceFolder, $destinationFolder, 'Answer Processing File (old)', '..');

        $answerProcessFilesBatch = ProcessingFile::find()
            ->select(['foquz_poll_answer_processings_files.id', 'foquz_poll_answer_processings_files.file_path'])
            ->joinWith(['processing.foquzAnswer.foquzPoll', 'file'], false)
            ->where(['foquz_poll.company_id' => $companyID])
            ->asArray();
        $this->copyFilesBatch($answerProcessFilesBatch, $sourceFolder, $destinationFolder, 'Answer Processing File', '..');

        $this->copyUserAvatars($companyID, $sourceFolder, $destinationFolder);

        $foquzDetailIDs = FoquzQuestionDetail::find()
            ->select('id')
            ->where(['foquz_question_id' => $questionIDs])
            ->column();

        $foquzRecipientDetailIDs = RecipientQuestionDetail::find()
            ->select('id')
            ->where(['recipient_id' => $foquzDetailIDs])
            ->column();

        $foquzDetailFilesBatch = FoquzFile::find()
            ->select(['id', 'file_path', 'preview_path'])
            ->where(['entity_type' => FoquzFile::TYPE_DETAIL, 'entity_id' => $foquzDetailIDs])
            ->asArray();
        $this->copyFilesBatch($foquzDetailFilesBatch, $sourceFolder, $destinationFolder, 'Foquz Detail File', '');

        $foquzSelfVariantFilesBatch = FoquzFile::find()
            ->select(['id', 'file_path', 'preview_path'])
            ->where(['entity_type' => FoquzFile::TYPE_SELF_VARIANT, 'entity_id' => ArrayHelper::merge(
                $foquzDetailIDs,
                $foquzRecipientDetailIDs,
                $questionIDs
            )])
            ->asArray();
        $this->copyFilesBatch($foquzSelfVariantFilesBatch, $sourceFolder, $destinationFolder, 'Foquz Self Variant File', '');

        print 'All files copied successfully!' . PHP_EOL;
    }
}
